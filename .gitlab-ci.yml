stages:  
  - build  
  - deploy  

variables:  
  NODE_ENV: production  
  DEPLOY_PATH: "/var/www/marktzoom-frontend"  

build:  
  stage: build  
  script:  
    - echo "Fixing permissions in build directory..."  
    - sudo chown -R "$USER":"$USER" .  
    - echo "Removing old build artifacts and dependencies..."  
    - sudo rm -rf node_modules .next  
    - echo "Creating environment file for build..."  
    - echo "NEXT_PUBLIC_BACKEND=https://api-dev.marktzoom.com" > .env  
    - echo "Installing dependencies with sudo..."  
    - sudo npm install  
    - echo "Building Next.js app..."  
    - sudo npm run build  
  artifacts:  
    name: "marktzoom-frontend-dist"  
    paths:  
      - .next  
      - public  
      - package.json  
      - package-lock.json  
      - next.config.mjs  
      - postcss.config.mjs  
      - .env  
    expire_in: 1 hour  

deploy:  
  stage: deploy  
  dependencies:  
    - build  
  script:  
    - echo "Deploying to $DEPLOY_PATH..."  
    - sudo mkdir -p $DEPLOY_PATH  
    - echo "Syncing built files and essentials..."  
    - ls -la  # List files to verify artifacts are here  
    - sudo rsync -avz --delete .next/ $DEPLOY_PATH/.next/  
    - sudo rsync -avz --delete public/ $DEPLOY_PATH/public/  
    - sudo cp package.json package-lock.json next.config.mjs postcss.config.mjs .env $DEPLOY_PATH/  
    - echo "Fixing permissions for deployment directory..."  
    - sudo chown -R www-data:www-data $DEPLOY_PATH  
    - echo "Installing production dependencies in $DEPLOY_PATH with sudo npm install..."  
    - sudo bash -c "cd $DEPLOY_PATH && npm install --omit=dev"  
    - echo "Restarting Next.js app with PM2 using sudo..."  
    - sudo bash -c "cd $DEPLOY_PATH && pm2 delete marktzoom-frontend || true"  
    - sudo bash -c "cd $DEPLOY_PATH && pm2 start npm --name marktzoom-frontend -- start"  
    - echo "Deployment complete."