"use client";

import React, { useState, useEffect, useRef } from "react";
import { useMutateApiMutation } from "@/redux/services/api";
import GoBack from "@/components/backend/GoBack";
import Image from "next/image";
import Button from "@/components/backend/Button";
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import toast from 'react-hot-toast';
import ReCA<PERSON><PERSON><PERSON> from 'react-google-recaptcha';

const Login = () => {
  const [formData, setFormData] = useState({ login: "", password: "" });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [recaptchaToken, setRecaptchaToken] = useState(null);
  const recaptchaRef = useRef(null);

  const router = useRouter();

  // reCAPTCHA site key - replace with your actual site key
  const RECAPTCHA_SITE_KEY = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Test key

  // Example: Mutating data (POST request)
  const [mutateApi, { data: mutationData, error: mutationError }] = useMutateApiMutation();

  useEffect(() => {
    if (mutationData) {
      // Set cookies
      document.cookie = `accessToken=${mutationData?.data?.accessToken};path=/;max-age=31536000`;
      document.cookie = `refreshToken=${mutationData?.data?.refreshToken};path=/;max-age=31536000`;
      document.cookie = `user=${JSON.stringify(mutationData?.data?.user)};path=/;max-age=31536000`;

      // Check if profile is incomplete using is_complete field or fallback to name fields
      const user = mutationData?.data?.user;
      const isIncompleteProfile = user.is_complete === false ||
        (!user.first_name || !user.last_name);

      // Add a small delay to ensure cookies are set before navigation
      setTimeout(() => {
        if (isIncompleteProfile) {
          toast.success('Login successful! Please complete your profile...');
          router.replace("/complete-profile");
        } else {
          toast.success('Login successful...!');
          router.replace("/dashboard");
        }
      }, 100); // 100ms delay to ensure cookies are processed
    }
    if (mutationError) {
      // Use a safer way to log the error
      console.log("Mutation API Error:", JSON.stringify(mutationError, null, 2));
      const errorMessage = mutationError.data?.message || "An error occurred during login. Please try again.";
      setErrors({ server: errorMessage });
      toast.error(errorMessage);
    }
  }, [mutationData, mutationError, router]);

  const validateForm = () => {
    let newErrors = {};
    if (!formData.login) {
      newErrors.login = "Username or email is required";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    }

    // if (!recaptchaToken) {
    //   newErrors.recaptcha = "Please complete the reCAPTCHA verification";
    // }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      const loginData = {
        login: formData.login.trim(),
        password: formData.password
      };

      mutateApi({
        endpoint: "/auth/new-login",
        method: "POST",
        data: loginData,
      })
      .unwrap()
      .then(() => {
        // Handle success - reset reCAPTCHA
        if (recaptchaRef.current) {
          recaptchaRef.current.reset();
          setRecaptchaToken(null);
        }
      })
      .catch(() => {
        // Handle error - reset reCAPTCHA
        if (recaptchaRef.current) {
          recaptchaRef.current.reset();
          setRecaptchaToken(null);
        }
      });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }

    // Also clear server error when user modifies any field
    if (errors.server) {
      setErrors(prev => ({ ...prev, server: undefined }));
    }
  };

  // Handle reCAPTCHA change
  const handleRecaptchaChange = (token) => {
    setRecaptchaToken(token);
    // Clear reCAPTCHA error when user completes it
    if (errors.recaptcha && token) {
      setErrors(prev => ({ ...prev, recaptcha: undefined }));
    }
  };

  // Handle reCAPTCHA expiry
  const handleRecaptchaExpired = () => {
    setRecaptchaToken(null);
    setErrors(prev => ({ ...prev, recaptcha: "reCAPTCHA has expired. Please verify again." }));
  };

  return (
    <>
      <div className="flex min-h-screen">
        {/* Left Side - Login Form */}
        <div className="w-full lg:w-1/2 p-8 flex flex-col">
          {/* Login Form */}
          <div className="max-w-md mx-auto w-full">
            {/* Back Button */}
            <div className="mb-16">
              <GoBack />
            </div>
            {/* Logo */}
            <div className="mb-8">
              <Image
                src="/assets/backend_assets/images/site-logo.svg"
                alt="logo"
                width={100}
                height={100}
                className="w-[200px]"
              />{" "}
            </div>
            <h2 className="text-4xl inter font-bold text-gray-800 mb-2">
              Log in to your Account
            </h2>
            <p className="text-gray-600 mt-4 mb-4 poppins">
              Welcome back! Select method to sign in:
            </p>

            {/* Server Error Message */}
            {errors.server && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-start">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>{errors.server}</span>
              </div>
            )}

            {/* Social Login Buttons */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <button className="flex justify-center items-center border border-gray-300 rounded-md py-3 px-4 inter font-semibold text-base text-[#4D4D4D]">
                <svg
                  className="w-5 h-5 mr-2"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 48 48"
                >
                  <path
                    fill="#FFC107"
                    d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
                  />
                  <path
                    fill="#FF3D00"
                    d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
                  />
                  <path
                    fill="#4CAF50"
                    d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
                  />
                  <path
                    fill="#1976D2"
                    d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
                  />
                </svg>
                Google
              </button>
              <button className="flex justify-center items-center border border-gray-300 rounded-md py-3 px-4 inter font-semibold text-base text-[#4D4D4D]">
                <svg
                  className="w-5 h-5 mr-2 text-blue-600"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 48 48"
                  fill="#1877F2"
                >
                  <path d="M24 4C12.954 4 4 12.954 4 24C4 34.046 11.954 42 22 42H24V28H20V24H24V19C24 14.582 27.582 11 32 11H36V15H32C30.895 15 30 15.895 30 17V24H36V28H30V42.338C38.055 40.298 44 32.943 44 24C44 12.954 35.046 4 24 4Z" />
                </svg>
                Facebook
              </button>
            </div>
            {/* Divider */}
            <div className="flex items-center mb-6 poppins text-sm font-medium text-[#666666]">
              <div className="flex-grow border-t border-gray-300" />
              <span className="mx-4 text-gray-500 text-sm">
                Or continue with email
              </span>
              <div className="flex-grow border-t border-gray-300" />
            </div>
            {/* Login and Password Fields */}
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <div className={`flex items-center border ${errors.login ? 'border-red-500' : 'border-gray-300'} rounded-md`}>
                  <div className="pl-4 pr-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                  <input
                    type="text"
                    name="login"
                    className={`w-full py-3 px-2 text-gray-700 focus:outline-none ${errors.login ? 'placeholder-red-300' : ''}`}
                    placeholder="Username or Email"
                    value={formData.login}
                    onChange={handleChange}
                  />
                </div>
                {errors.login && (
                  <p className="text-red-500 text-sm mt-1">{errors.login}</p>
                )}
              </div>
              <div className="mb-4">
                <div className={`flex items-center border ${errors.password ? 'border-red-500' : 'border-gray-300'} relative rounded-md`}>
                  <div className="pl-4 pr-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                  </div>
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    className={`w-full py-3 px-2 text-gray-700 focus:outline-none ${errors.password ? 'placeholder-red-300' : ''}`}
                    placeholder="Password"
                    value={formData.password}
                    onChange={handleChange}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-gray-600"
                  >
                    {showPassword ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                        />
                      </svg>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-red-500 text-sm mt-1">{errors.password}</p>
                )}
              </div>
              {/* Remember Me and Forgot Password */}
              <div className="flex justify-between mb-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="remember"
                    className="h-4 w-4 text-blue-600"
                    defaultChecked
                  />
                  <label
                    htmlFor="remember"
                    className="ml-2 text-[#666666] poppins font-medium text-sm"
                  >
                    Remember me.
                  </label>
                </div>
                <Link
                  href="/forgot-password"
                  className="text-[#666666] poppins font-medium text-sm"
                >
                  Forgot Password?
                </Link>
              </div>

              {/* reCAPTCHA */}
              <div className="mb-6">
                <ReCAPTCHA
                  ref={recaptchaRef}
                  sitekey={RECAPTCHA_SITE_KEY}
                  onChange={handleRecaptchaChange}
                  onExpired={handleRecaptchaExpired}
                  theme="light"
                  size="normal"
                />
                {errors.recaptcha && (
                  <p className="text-red-500 text-sm mt-2">{errors.recaptcha}</p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full bg-blue-500 text-white py-3 rounded-md font-semibold transition text-xl"
              >
                Sign In
              </Button>
            </form>
            {/* Create Account Link */}
            <div className="text-center mt-6 poppins font-medium text-sm">
              <span className="text-gray-600">Don&apos;t have an account?</span>
              <Link href="/register" className="text-blue-500 hover:underline ml-1">Create Account</Link>
            </div>
          </div>
        </div>
        {/* Right Side - Image and Info */}
        <div className="hidden lg:block lg:w-1/2 relative overflow-hidden">
          <Image
            src="/assets/login-img.png"
            alt="Login Image"
            height={1000}
            width={1000}
            className="object-contain h-[99vh] w-full"
          />
        </div>
      </div>
    </>
  );
};

export default Login;