/* eslint-disable react/display-name */
/* eslint-disable import/no-anonymous-default-export */
import React from "react";

export default ({ name, className }) => {
  if (name === "dashboard") {
    return (
      <svg
        className={className}
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.75 16.25H17.5V10.625L17.6828 10.8078C17.8003 10.9251 17.9596 10.9909 18.1256 10.9907C18.2916 10.9906 18.4507 10.9245 18.568 10.807C18.6852 10.6896 18.751 10.5303 18.7509 10.3643C18.7508 10.1983 18.6847 10.0392 18.5672 9.92188L10.8836 2.24063C10.6492 2.00639 10.3314 1.8748 10 1.8748C9.66862 1.8748 9.3508 2.00639 9.11641 2.24063L1.43281 9.92188C1.31564 10.0392 1.24986 10.1982 1.24993 10.364C1.25 10.5297 1.31593 10.6887 1.4332 10.8059C1.55048 10.923 1.7095 10.9888 1.87528 10.9887C2.04106 10.9887 2.20002 10.9227 2.31719 10.8055L2.5 10.625V16.25H1.25C1.08424 16.25 0.925268 16.3159 0.808058 16.4331C0.690848 16.5503 0.625 16.7092 0.625 16.875C0.625 17.0408 0.690848 17.1997 0.808058 17.3169C0.925268 17.4342 1.08424 17.5 1.25 17.5H18.75C18.9158 17.5 19.0747 17.4342 19.1919 17.3169C19.3092 17.1997 19.375 17.0408 19.375 16.875C19.375 16.7092 19.3092 16.5503 19.1919 16.4331C19.0747 16.3159 18.9158 16.25 18.75 16.25ZM3.75 9.375L10 3.125L16.25 9.375V16.25H12.5V11.875C12.5 11.7092 12.4342 11.5503 12.3169 11.4331C12.1997 11.3159 12.0408 11.25 11.875 11.25H8.125C7.95924 11.25 7.80027 11.3159 7.68306 11.4331C7.56585 11.5503 7.5 11.7092 7.5 11.875V16.25H3.75V9.375ZM11.25 16.25H8.75V12.5H11.25V16.25Z"
          fill="currentColor"
        />
      </svg>
    );
  }

  if (name === "requests") {
    return (
      <svg
        className={className}
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_502_372)">
          <path
            d="M3.12213 -0.00627785C3.15029 -0.00647659 3.17845 -0.00667534 3.20747 -0.0068801C3.30232 -0.00749573 3.39717 -0.00782771 3.49202 -0.00815718C3.55983 -0.00852966 3.62765 -0.00891603 3.69546 -0.0093153C3.91856 -0.0105425 4.14167 -0.011333 4.36478 -0.0120577C4.4027 -0.0121865 4.44063 -0.0123153 4.47971 -0.012448C4.83724 -0.0136503 5.19477 -0.0146939 5.55231 -0.015355C6.01003 -0.0162113 6.46773 -0.0178518 6.92544 -0.0205674C7.24707 -0.022411 7.56869 -0.02336 7.89032 -0.0236439C8.08252 -0.0238378 8.27471 -0.0244291 8.46691 -0.0259777C8.64771 -0.0274153 8.82849 -0.0277471 9.0093 -0.0272275C9.07567 -0.0272296 9.14205 -0.0276335 9.20842 -0.0284678C9.29904 -0.0295461 9.38955 -0.0291792 9.48018 -0.0284508C9.53088 -0.0286161 9.58159 -0.0287814 9.63383 -0.0289517C9.81737 0.011363 9.85719 0.0807411 9.96094 0.234372C9.98688 0.31738 9.98688 0.31738 9.98535 0.390622C9.98586 0.414792 9.98636 0.438962 9.98688 0.463864C9.9422 0.606829 9.89131 0.70054 9.76562 0.781247C9.56529 0.817092 9.36523 0.810847 9.16255 0.807854C9.10018 0.807788 9.0378 0.807825 8.97543 0.807958C8.80627 0.808025 8.63714 0.806647 8.46799 0.805011C8.29115 0.803546 8.11432 0.803408 7.93748 0.80313C7.60268 0.802402 7.26791 0.800478 6.93312 0.798126C6.48067 0.795016 6.02822 0.793751 5.57576 0.792383C4.863 0.79018 4.15027 0.785511 3.4375 0.781247C3.4375 2.01875 3.4375 3.25625 3.4375 4.53125C3.9918 4.53125 4.54609 4.53125 5.11719 4.53125C5.11537 4.35068 5.11356 4.17011 5.11169 3.98407C5.1109 3.86942 5.11014 3.75478 5.10956 3.64013C5.10863 3.4583 5.10752 3.27649 5.1054 3.09467C5.1037 2.94802 5.10277 2.80139 5.10236 2.65473C5.10207 2.59888 5.10151 2.54303 5.10067 2.48719C5.09953 2.40876 5.09947 2.33031 5.09945 2.25187C5.0991 2.20728 5.09876 2.1627 5.0984 2.11676C5.12152 1.96348 5.17792 1.8784 5.27344 1.75781C5.37133 1.70886 5.43771 1.71367 5.54731 1.71339C5.58898 1.7132 5.63065 1.713 5.67359 1.71279C5.71974 1.71277 5.76588 1.71276 5.81343 1.71273C5.86217 1.71256 5.91091 1.71239 5.96113 1.71222C6.09686 1.71175 6.23259 1.71153 6.36832 1.71135C6.51454 1.71112 6.66075 1.71066 6.80697 1.71024C7.16028 1.70929 7.5136 1.70872 7.86691 1.70821C7.96666 1.70807 8.0664 1.70791 8.16615 1.70775C8.78521 1.70677 9.40428 1.7059 10.0233 1.70542C10.1668 1.7053 10.3102 1.70519 10.4537 1.70507C10.4893 1.70504 10.525 1.70501 10.5617 1.70498C11.1395 1.70449 11.7173 1.70319 12.2951 1.70156C12.888 1.69991 13.4808 1.69899 14.0737 1.69878C14.4067 1.69865 14.7397 1.69819 15.0728 1.6969C15.3563 1.69581 15.6399 1.69538 15.9234 1.69582C16.0681 1.69602 16.2129 1.6959 16.3576 1.69494C16.5144 1.6939 16.6711 1.69437 16.8279 1.695C16.8968 1.69418 16.8968 1.69418 16.967 1.69335C17.1516 1.69517 17.2699 1.69815 17.4306 1.79324C17.5302 1.96654 17.5271 2.1116 17.5248 2.30846C17.5253 2.36856 17.5253 2.36856 17.5257 2.42987C17.5265 2.56432 17.5259 2.69872 17.5253 2.83317C17.5255 2.92948 17.5259 3.02579 17.5263 3.1221C17.5271 3.35664 17.527 3.59117 17.5264 3.82571C17.526 4.01632 17.5259 4.20694 17.5261 4.39755C17.5262 4.42468 17.5262 4.45181 17.5262 4.47976C17.5263 4.53487 17.5264 4.58998 17.5264 4.64509C17.527 5.16188 17.5263 5.67866 17.5253 6.19544C17.5244 6.63888 17.5246 7.0823 17.5255 7.52573C17.5265 8.04066 17.5269 8.55559 17.5263 9.07053C17.5263 9.12544 17.5262 9.18035 17.5261 9.23526C17.5261 9.26227 17.5261 9.28929 17.526 9.31712C17.5259 9.50751 17.5262 9.69789 17.5266 9.88828C17.5272 10.1445 17.5268 10.4008 17.5257 10.657C17.5254 10.7511 17.5255 10.8453 17.5259 10.9395C17.5264 11.0678 17.5258 11.196 17.5248 11.3244C17.5252 11.3618 17.5257 11.3992 17.5261 11.4378C17.5235 11.6162 17.519 11.6939 17.4076 11.8394C17.2884 11.9259 17.2264 11.9393 17.0805 11.9417C17.0389 11.9428 16.9972 11.9439 16.9543 11.9451C16.8897 11.9455 16.8897 11.9455 16.8237 11.9458C16.758 11.9473 16.758 11.9473 16.6909 11.9488C16.583 11.9511 16.4751 11.9525 16.3672 11.9531C16.3841 11.9866 16.3841 11.9866 16.4014 12.0207C16.5451 12.3347 16.5234 12.4681 16.5234 12.8516C17.4129 12.8516 18.3023 12.8516 19.2188 12.8516C19.2188 8.86836 19.2188 4.88515 19.2188 0.781247C17.1449 0.788981 17.1449 0.788981 15.0712 0.798842C14.6703 0.799724 14.6703 0.799724 14.4828 0.799888C14.3516 0.80007 14.2205 0.800803 14.0893 0.801867C13.922 0.803206 13.7547 0.803608 13.5874 0.803331C13.526 0.803389 13.4646 0.803777 13.4032 0.804531C13.3202 0.805502 13.2373 0.805269 13.1544 0.804734C13.1079 0.804914 13.0614 0.805094 13.0135 0.80528C12.8534 0.773966 12.7959 0.711735 12.6953 0.585934C12.648 0.4234 12.6452 0.341908 12.7051 0.183102C12.7734 0.0781217 12.7734 0.0781217 12.8906 -3.26894e-06C13.0125 -0.0101619 13.0125 -0.0101619 13.1659 -0.0107124C13.1941 -0.010917 13.2223 -0.0111216 13.2513 -0.0113323C13.3461 -0.0119196 13.4408 -0.0119458 13.5355 -0.0119719C13.6033 -0.0122695 13.6712 -0.0125987 13.739 -0.0129571C13.9234 -0.0138155 14.1078 -0.0141582 14.2922 -0.0143566C14.4074 -0.0144988 14.5226 -0.0147425 14.6377 -0.0150207C15.0394 -0.0159779 15.4411 -0.0164875 15.8427 -0.0166258C16.2174 -0.0167679 16.592 -0.0179358 16.9666 -0.0195713C17.288 -0.0209256 17.6095 -0.02153 17.931 -0.0215573C18.123 -0.0215883 18.3151 -0.0219317 18.5072 -0.0230416C18.6878 -0.024062 18.8684 -0.0241516 19.0491 -0.0235238C19.1154 -0.0234527 19.1817 -0.0237159 19.248 -0.024337C19.3385 -0.0251334 19.429 -0.0247259 19.5195 -0.0240162C19.5955 -0.024134 19.5955 -0.024134 19.673 -0.0242543C19.8047 -3.26894e-06 19.8047 -3.26894e-06 19.9186 0.0663477C20.0493 0.273415 20.0269 0.480292 20.0248 0.720207C20.025 0.774858 20.0253 0.829509 20.0257 0.884159C20.0265 1.03433 20.0261 1.18448 20.0255 1.33465C20.0251 1.4966 20.0258 1.65855 20.0263 1.8205C20.0272 2.13776 20.027 2.45501 20.0264 2.77227C20.026 3.03007 20.0259 3.28787 20.0261 3.54568C20.0262 3.60068 20.0262 3.60068 20.0262 3.65679C20.0263 3.73128 20.0264 3.80577 20.0264 3.88026C20.027 4.57901 20.0263 5.27776 20.0253 5.9765C20.0244 6.57628 20.0246 7.17604 20.0255 7.77582C20.0265 8.47204 20.0269 9.16826 20.0263 9.86448C20.0263 9.9387 20.0262 10.0129 20.0261 10.0871C20.0261 10.1419 20.0261 10.1419 20.026 10.1978C20.0259 10.4553 20.0262 10.7129 20.0266 10.9704C20.0272 11.2842 20.027 11.598 20.026 11.9118C20.0255 12.0719 20.0253 12.2321 20.0259 12.3922C20.0265 12.5388 20.0262 12.6853 20.0251 12.8319C20.0248 12.9101 20.0254 12.9884 20.0261 13.0666C20.022 13.4451 20.022 13.4451 19.91 13.5665C19.7677 13.6561 19.6443 13.6588 19.4813 13.655C19.4493 13.655 19.4172 13.655 19.3842 13.6551C19.2787 13.6549 19.1733 13.6534 19.0678 13.6519C18.9946 13.6515 18.9213 13.6512 18.848 13.651C18.6554 13.6503 18.4628 13.6484 18.2702 13.6462C18.0736 13.6442 17.8769 13.6433 17.6803 13.6423C17.2947 13.6403 16.909 13.6369 16.5234 13.6328C16.5237 13.6599 16.524 13.687 16.5242 13.7149C16.5266 13.9728 16.5284 14.2307 16.5295 14.4887C16.5301 14.6212 16.5309 14.7537 16.5323 14.8863C16.545 16.2432 16.545 16.2432 16.2817 16.8066C16.2701 16.8324 16.2584 16.8582 16.2464 16.8847C16.1335 17.1249 15.9873 17.3331 15.8203 17.5391C15.7744 17.6007 15.7744 17.6007 15.7275 17.6636C15.5649 17.8378 15.371 18.0176 15.1562 18.125C15.1557 18.198 15.1557 18.198 15.1551 18.2725C15.1536 18.4534 15.151 18.6343 15.1481 18.8152C15.147 18.8935 15.1462 18.9717 15.1457 19.0499C15.1448 19.1625 15.143 19.2751 15.141 19.3877C15.1409 19.4225 15.1409 19.4574 15.1408 19.4934C15.137 19.6562 15.1309 19.7438 15.0425 19.8845C14.9249 19.9948 14.8613 20.005 14.7042 20.0063C14.6666 20.0067 14.6289 20.0071 14.5902 20.0075C14.5488 20.0077 14.5073 20.0079 14.4647 20.0082C14.4212 20.0085 14.3776 20.0089 14.3328 20.0093C14.1883 20.0105 14.0439 20.0113 13.8994 20.0121C13.8501 20.0123 13.8009 20.0126 13.7502 20.0128C13.5432 20.0138 13.3363 20.0148 13.1294 20.0154C12.8329 20.0162 12.5364 20.0178 12.2399 20.0206C12.0062 20.0226 11.7726 20.0234 11.539 20.0238C11.4396 20.0241 11.3402 20.0248 11.2408 20.026C11.1018 20.0275 10.9629 20.0275 10.8239 20.0272C10.7827 20.028 10.7416 20.0288 10.6991 20.0296C10.4296 20.0269 10.4296 20.0269 10.3214 19.9522C10.2209 19.8407 10.1709 19.7375 10.1731 19.5884C10.1732 19.5556 10.1732 19.5228 10.1732 19.4889C10.1742 19.4543 10.1752 19.4196 10.1762 19.3838C10.1765 19.3478 10.1768 19.3117 10.1771 19.2745C10.1782 19.1599 10.1806 19.0453 10.1831 18.9307C10.1841 18.8527 10.185 18.7747 10.1858 18.6967C10.1879 18.5061 10.1913 18.3156 10.1953 18.125C10.1655 18.1139 10.1357 18.1028 10.1049 18.0914C9.96945 18.0339 9.86813 17.9472 9.7583 17.8516C9.72489 17.8227 9.72489 17.8227 9.6908 17.7932C9.39281 17.5332 9.12055 17.2643 8.88184 16.9482C8.85728 16.9165 8.85728 16.9165 8.83223 16.884C8.49414 16.4207 8.29043 15.8641 8.08838 15.332C8.06646 15.2751 8.04451 15.2181 8.02254 15.1611C7.88891 14.8139 7.75833 14.4655 7.62778 14.117C7.61166 14.074 7.59554 14.031 7.57893 13.9867C7.53959 13.8818 7.50026 13.7768 7.46094 13.6719C7.43736 13.6719 7.41379 13.6718 7.3895 13.6718C6.81569 13.6712 6.24189 13.6696 5.66809 13.6667C5.39061 13.6654 5.11313 13.6644 4.83563 13.6642C4.56791 13.6641 4.30021 13.663 4.03249 13.6612C3.93028 13.6607 3.82806 13.6605 3.72585 13.6607C3.58283 13.6609 3.43987 13.6599 3.29686 13.6586C3.25446 13.6589 3.21206 13.6592 3.16838 13.6596C2.88074 13.6552 2.88074 13.6552 2.72852 13.5384C2.6345 13.4071 2.61231 13.3454 2.61249 13.1869C2.61241 13.1325 2.61241 13.1325 2.61233 13.077C2.61254 13.0171 2.61254 13.0171 2.61275 12.9561C2.61275 12.9141 2.61274 12.8722 2.61274 12.829C2.61276 12.69 2.61307 12.5509 2.61337 12.4118C2.61345 12.3156 2.6135 12.2193 2.61354 12.1231C2.61369 11.8695 2.61407 11.6158 2.61451 11.3621C2.6149 11.1035 2.61508 10.8448 2.61528 10.5861C2.6157 10.0782 2.61636 9.57036 2.61719 9.0625C2.57233 9.06315 2.57233 9.06315 2.52656 9.06381C2.24437 9.06773 1.9622 9.0707 1.67999 9.07262C1.53491 9.07364 1.38985 9.07502 1.24478 9.07723C1.10472 9.07936 0.964676 9.08052 0.824596 9.08103C0.771222 9.08139 0.717849 9.08209 0.664484 9.08315C0.193394 9.0921 0.193394 9.0921 0.0649738 8.96975C-0.00483329 8.85955 -0.0264375 8.80137 -0.0244141 8.67187C-0.0249176 8.63965 -0.0254211 8.60742 -0.0259399 8.57422C0.0130236 8.42753 0.0756177 8.37421 0.195312 8.28125C0.322374 8.25267 0.446753 8.25494 0.576553 8.25659C0.633847 8.25618 0.633847 8.25618 0.692298 8.25575C0.818403 8.25509 0.944453 8.25585 1.07056 8.25668C1.15826 8.25665 1.24597 8.25655 1.33368 8.2564C1.51744 8.25628 1.70118 8.25686 1.88494 8.25793C2.12047 8.25926 2.35596 8.25903 2.59149 8.25829C2.77258 8.25788 2.95367 8.25823 3.13476 8.25882C3.2216 8.25902 3.30845 8.259 3.39529 8.25876C3.51667 8.25854 3.63801 8.25934 3.75938 8.26041C3.81321 8.26003 3.81321 8.26003 3.86812 8.25964C4.03166 8.26209 4.11823 8.26722 4.25967 8.35579C4.37843 8.48302 4.38803 8.57431 4.38644 8.74847C4.36865 8.87232 4.30652 8.93825 4.21875 9.02343C3.98554 9.14004 3.69824 9.0625 3.4375 9.0625C3.4375 10.3129 3.4375 11.5633 3.4375 12.8516C5.29375 12.8322 5.29375 12.8322 7.1875 12.8125C7.20039 12.6707 7.21328 12.5289 7.22656 12.3828C7.27553 12.2589 7.32148 12.1475 7.38281 12.0312C7.3957 12.0055 7.40859 11.9797 7.42188 11.9531C7.38362 11.953 7.38362 11.953 7.34459 11.953C7.07907 11.9522 6.81358 11.9501 6.54807 11.9472C6.44899 11.9463 6.34991 11.9457 6.25083 11.9455C6.10837 11.9451 5.96597 11.9435 5.82352 11.9417C5.77924 11.9418 5.73496 11.942 5.68934 11.9421C5.34659 11.9358 5.34659 11.9358 5.22685 11.8196C5.13328 11.686 5.11231 11.6228 5.11249 11.4622C5.11241 11.4058 5.11241 11.4058 5.11233 11.3483C5.11247 11.3069 5.11261 11.2656 5.11275 11.223C5.11275 11.1795 5.11274 11.1361 5.11274 11.0913C5.11276 10.9472 5.11307 10.803 5.11337 10.6589C5.11345 10.5591 5.1135 10.4594 5.11354 10.3597C5.11367 10.1237 5.11398 9.88766 5.11437 9.65165C5.11481 9.38299 5.11503 9.11432 5.11522 8.84566C5.11563 8.29294 5.11632 7.74022 5.11719 7.1875C5.0677 7.18793 5.01822 7.18836 4.96724 7.18881C4.5011 7.19277 4.03497 7.1957 3.56881 7.19762C3.32915 7.19864 3.08951 7.20002 2.84986 7.20223C2.61861 7.20435 2.38738 7.20552 2.15613 7.20603C2.06787 7.20639 1.97961 7.20709 1.89135 7.20815C1.7678 7.20956 1.6443 7.20976 1.52074 7.20967C1.46591 7.21072 1.46591 7.21072 1.40997 7.2118C1.23229 7.21039 1.15721 7.20635 1.01253 7.09456C0.931227 6.98363 0.910963 6.93272 0.913086 6.79687C0.912582 6.76465 0.912079 6.73242 0.91156 6.69922C0.950607 6.55221 1.01257 6.49915 1.13281 6.40625C1.2622 6.37827 1.38881 6.38154 1.52074 6.38407C1.55963 6.38405 1.59852 6.38402 1.63859 6.38399C1.7669 6.38412 1.89516 6.38564 2.02347 6.38717C2.11252 6.38754 2.20157 6.38782 2.29062 6.38801C2.52484 6.38876 2.75902 6.39067 2.99323 6.39284C3.23228 6.39483 3.47134 6.39573 3.7104 6.39671C4.17934 6.3988 4.64826 6.40224 5.11719 6.40625C5.11719 6.04531 5.11719 5.68437 5.11719 5.3125C4.96911 5.31352 4.82104 5.31454 4.66847 5.3156C4.17969 5.31883 3.6909 5.321 3.20211 5.32262C2.9057 5.32362 2.6093 5.325 2.3129 5.32723C2.05454 5.32918 1.7962 5.33044 1.53784 5.33087C1.40104 5.33113 1.26426 5.33173 1.12747 5.33315C0.974745 5.33472 0.822036 5.33476 0.669298 5.33467C0.623977 5.33537 0.578656 5.33607 0.531962 5.3368C0.190596 5.3346 0.190596 5.3346 0.0696534 5.21956C-0.00554844 5.10903 -0.0264831 5.05429 -0.0244141 4.92187C-0.0249176 4.88965 -0.0254211 4.85742 -0.0259399 4.82422C0.00230211 4.71789 0.0388973 4.64883 0.118923 4.57259C0.304093 4.47237 0.538968 4.50811 0.744171 4.51217C0.798191 4.51254 0.852211 4.51282 0.906231 4.51301C1.04798 4.51375 1.18968 4.51567 1.33141 4.51784C1.47621 4.51984 1.62102 4.52073 1.76582 4.52171C2.04963 4.5238 2.3334 4.52713 2.61719 4.53125C2.61704 4.50952 2.6169 4.48779 2.61675 4.46541C2.6133 3.93625 2.61072 3.4071 2.60909 2.87794C2.60828 2.62204 2.60717 2.36615 2.6054 2.11025C2.6037 1.8633 2.60277 1.61637 2.60236 1.36941C2.60207 1.27519 2.60151 1.18097 2.60067 1.08674C2.59953 0.954785 2.59938 0.822859 2.59945 0.690896C2.59889 0.651925 2.59833 0.612954 2.59775 0.572802C2.59911 0.343947 2.62081 0.213373 2.77344 0.0390592C2.88908 -0.0187597 2.99302 -0.00559384 3.12213 -0.00627785ZM5.9375 2.5C5.9375 5.34882 5.9375 8.19765 5.9375 11.1328C7.08477 11.1328 8.23203 11.1328 9.41406 11.1328C9.41507 10.7135 9.41608 10.2941 9.41711 9.86206C9.4181 9.59645 9.41912 9.33085 9.42032 9.06524C9.42221 8.64363 9.42395 8.22202 9.42465 7.8004C9.42516 7.49333 9.42621 7.18627 9.42791 6.8792C9.4288 6.71667 9.42941 6.55414 9.42937 6.39161C9.42934 6.23847 9.43004 6.08536 9.4313 5.93223C9.43163 5.87617 9.4317 5.82011 9.43148 5.76406C9.43021 5.39071 9.45372 5.14604 9.69971 4.85351C9.96691 4.62981 10.2076 4.55388 10.553 4.56024C10.9329 4.59468 11.2426 4.87491 11.5443 5.08836C11.651 5.16358 11.7583 5.23802 11.8657 5.31234C12.479 5.73713 12.479 5.73713 12.775 5.94894C12.8097 5.97377 12.8445 5.99861 12.8803 6.0242C13.0606 6.16598 13.1533 6.33846 13.2031 6.5625C13.237 6.92142 13.1861 7.18111 12.9816 7.47741C12.8668 7.60455 12.7399 7.69156 12.5977 7.78564C12.5725 7.80264 12.5473 7.81963 12.5214 7.83714C12.4954 7.85464 12.4694 7.87214 12.4426 7.89016C12.4168 7.90801 12.391 7.92585 12.3644 7.94423C12.2656 8.00781 12.2656 8.00781 12.1474 8.06826C11.8966 8.20389 11.8966 8.20389 11.7557 8.44028C11.7426 8.58632 11.7441 8.72561 11.7511 8.87207C11.752 8.92504 11.7526 8.97802 11.7531 9.031C11.7549 9.16956 11.7595 9.30788 11.7647 9.44635C11.7695 9.58799 11.7716 9.72967 11.774 9.87137C11.779 10.1487 11.787 10.4259 11.7969 10.7031C11.8394 10.7024 11.882 10.7016 11.9259 10.7008C11.9825 10.7002 12.0391 10.6996 12.0956 10.699C12.1236 10.6984 12.1516 10.6979 12.1804 10.6973C12.4375 10.6953 12.6797 10.7378 12.8758 10.9174C12.8944 10.9361 12.913 10.9548 12.9321 10.9741C13.0345 11.0659 13.0345 11.0659 13.1241 11.0423C13.1837 11.0205 13.2429 10.9975 13.3017 10.9735C13.6349 10.8495 13.9689 10.8972 14.2969 11.0156C14.3613 11.0543 14.4258 11.093 14.4922 11.1328C15.227 11.1328 15.9617 11.1328 16.7188 11.1328C16.7188 8.28398 16.7188 5.43515 16.7188 2.5C13.1609 2.5 9.60312 2.5 5.9375 2.5ZM10.284 5.39946C10.2218 5.48627 10.2202 5.53365 10.2211 5.63983C10.2211 5.67423 10.2211 5.70863 10.2211 5.74407C10.2217 5.78107 10.2223 5.81806 10.2229 5.85617C10.2231 5.89415 10.2233 5.93214 10.2234 5.97127C10.2241 6.09267 10.2256 6.21405 10.2271 6.33545C10.2276 6.41772 10.2282 6.49999 10.2287 6.58226C10.2299 6.78401 10.232 6.98575 10.2344 7.1875C10.3028 7.18635 10.3028 7.18635 10.3726 7.18518C10.433 7.18456 10.4935 7.18396 10.5539 7.18338C10.5839 7.18282 10.6139 7.18227 10.6448 7.1817C11.0147 7.17902 11.2329 7.32821 11.5234 7.53906C11.6666 7.44745 11.8087 7.35426 11.9507 7.26074C11.9913 7.23476 12.032 7.20877 12.0739 7.182C12.1125 7.15632 12.1512 7.13064 12.191 7.10418C12.2268 7.08086 12.2626 7.05754 12.2995 7.03352C12.4058 6.93094 12.4179 6.86463 12.4219 6.71875C12.2731 6.5184 12.0438 6.39191 11.8384 6.25488C11.7616 6.20327 11.6849 6.15165 11.6081 6.1C11.5686 6.07347 11.5291 6.04694 11.4884 6.01961C11.3665 5.93706 11.2457 5.85321 11.125 5.76889C11.0862 5.74192 11.0474 5.71495 11.0075 5.68716C10.9326 5.6351 10.858 5.58278 10.7835 5.53018C10.7327 5.49501 10.7327 5.49501 10.6808 5.45913C10.6511 5.43832 10.6214 5.4175 10.5908 5.39605C10.4783 5.33573 10.3916 5.32318 10.284 5.39946ZM10.2765 8.09029C10.1787 8.26167 10.2084 8.4788 10.2105 8.67018C10.2103 8.72165 10.21 8.77312 10.2098 8.82615C10.2093 8.93791 10.2095 9.04964 10.2101 9.1614C10.211 9.33821 10.2106 9.515 10.2099 9.6918C10.209 10.0034 10.2092 10.3149 10.2097 10.6265C10.2104 11.0954 10.2102 11.5643 10.2089 12.0332C10.2085 12.2089 10.2086 12.3845 10.2093 12.5602C10.2095 12.6432 10.2095 12.7262 10.2094 12.8092C10.2092 12.9103 10.2094 13.0114 10.2102 13.1124C10.2083 13.5307 10.2083 13.5307 10.0781 13.7109C9.95437 13.7934 9.87765 13.802 9.73175 13.8109C9.56087 13.7661 9.46615 13.6159 9.38026 13.4701C9.35798 13.4246 9.3357 13.3792 9.31274 13.3324C9.28757 13.2821 9.26239 13.2318 9.23721 13.1815C9.19852 13.1031 9.15999 13.0246 9.12153 12.946C9.08393 12.8695 9.04577 12.7933 9.00757 12.7171C8.98529 12.6717 8.96301 12.6263 8.94005 12.5796C8.84937 12.4319 8.74824 12.3429 8.59375 12.2656C8.44638 12.2489 8.36919 12.2608 8.23486 12.3267C8.1063 12.4381 8.02342 12.5242 8.00781 12.6953C8.0861 13.0471 8.22873 13.3885 8.35632 13.725C8.38349 13.797 8.38349 13.797 8.41121 13.8704C8.44931 13.9713 8.48754 14.0721 8.52585 14.1729C8.56452 14.2747 8.603 14.3766 8.64128 14.4786C9.11477 15.7393 9.60417 16.8648 10.8203 17.5781C10.8461 17.5781 10.8719 17.5781 10.8984 17.5781C11.0718 18.0983 11.0004 18.6707 11.0156 19.2187C12.0984 19.2187 13.1812 19.2187 14.2969 19.2187C14.3098 18.7418 14.3227 18.2648 14.3359 17.7734C14.4202 17.6049 14.4529 17.5836 14.6094 17.4951C15.1708 17.1508 15.481 16.6769 15.6641 16.0547C15.7028 15.8659 15.7096 15.6811 15.7107 15.489C15.711 15.4566 15.7113 15.4242 15.7116 15.3908C15.7126 15.2841 15.7132 15.1775 15.7138 15.0708C15.7141 15.016 15.7141 15.016 15.7144 14.96C15.7155 14.7668 15.7162 14.5735 15.7168 14.3802C15.7173 14.2205 15.7184 14.0608 15.72 13.901C15.7219 13.7078 15.7229 13.5145 15.7232 13.3212C15.7234 13.2477 15.724 13.1742 15.725 13.1007C15.7262 12.9978 15.7262 12.8951 15.7259 12.7922C15.7266 12.762 15.7272 12.7318 15.7279 12.7007C15.7261 12.5475 15.7061 12.445 15.6124 12.3258C15.5136 12.2178 15.5136 12.2178 15.3735 12.1826C15.203 12.1886 15.1297 12.2356 15 12.3437C14.9423 12.4591 14.954 12.5767 14.9527 12.7032C14.9516 12.7607 14.9505 12.8181 14.9493 12.8755C14.9477 12.9659 14.9463 13.0564 14.9453 13.1468C14.9442 13.2342 14.9424 13.3215 14.9405 13.4088C14.9404 13.4356 14.9402 13.4623 14.9401 13.4898C14.9356 13.6689 14.8976 13.7926 14.8047 13.9453C14.6815 14.0387 14.5651 14.0388 14.4141 14.0234C14.2331 13.935 14.2331 13.935 14.1797 13.8281C14.1757 13.7729 14.174 13.7176 14.1735 13.6622C14.1731 13.6279 14.1727 13.5936 14.1723 13.5583C14.172 13.5211 14.1717 13.4839 14.1714 13.4456C14.1704 13.367 14.1692 13.2884 14.1681 13.2098C14.1665 13.0857 14.165 12.9617 14.164 12.8377C14.1629 12.718 14.1611 12.5984 14.1592 12.4788C14.159 12.4236 14.1591 12.4236 14.1589 12.3673C14.1548 12.1482 14.13 11.9899 14.0234 11.7969C13.8845 11.6927 13.8203 11.6759 13.6475 11.6968C13.4951 11.7673 13.4225 11.8394 13.3594 11.9922C13.338 12.1321 13.3394 12.2699 13.3411 12.4112C13.3408 12.4512 13.3405 12.4913 13.3402 12.5325C13.3394 12.6601 13.3395 12.7875 13.3398 12.915C13.3403 13.0832 13.3398 13.2513 13.3386 13.4195C13.3393 13.4774 13.3393 13.4774 13.34 13.5365C13.338 13.8041 13.338 13.8041 13.2459 13.9293C13.1206 14.0136 13.0619 14.0284 12.9126 14.0137C12.882 14.0111 12.8515 14.0084 12.82 14.0057C12.718 13.9803 12.6812 13.9494 12.6172 13.8672C12.5785 13.7511 12.5731 13.6744 12.5726 13.5533C12.5722 13.4933 12.5722 13.4933 12.5718 13.432C12.5716 13.3888 12.5714 13.3456 12.5713 13.3011C12.5704 13.2098 12.5694 13.1185 12.5684 13.0272C12.5671 12.8831 12.5659 12.739 12.5654 12.5949C12.5648 12.4559 12.5632 12.317 12.5615 12.178C12.5616 12.135 12.5616 12.0919 12.5617 12.0476C12.559 11.8092 12.559 11.8092 12.4642 11.5945C12.3561 11.5001 12.2893 11.4859 12.1498 11.4763C12.0151 11.49 11.9353 11.5534 11.8359 11.6406C11.7977 11.7555 11.7916 11.8312 11.7907 11.9509C11.7901 12.0098 11.7901 12.0098 11.7895 12.0698C11.7892 12.1122 11.7889 12.1545 11.7886 12.1982C11.7875 12.2877 11.7864 12.3772 11.7853 12.4667C11.7837 12.608 11.7822 12.7493 11.7812 12.8905C11.7801 13.0268 11.7783 13.163 11.7764 13.2993C11.7763 13.3415 11.7762 13.3837 11.776 13.4271C11.77 13.7967 11.77 13.7967 11.6406 13.9453C11.496 14.0417 11.421 14.0354 11.25 14.0234C11.1483 13.9905 11.1084 13.9684 11.0512 13.8784C11.0071 13.7675 11.0058 13.6806 11.0058 13.5613C11.0056 13.5147 11.0054 13.4682 11.0052 13.4202C11.0053 13.3693 11.0055 13.3183 11.0056 13.2659C11.0055 13.2116 11.0054 13.1573 11.0052 13.103C11.0049 12.9861 11.0048 12.8693 11.0049 12.7524C11.005 12.5676 11.0045 12.3828 11.0038 12.1979C11.0019 11.6723 11.001 11.1468 11.0007 10.6212C11.0006 10.3308 10.9999 10.0405 10.9986 9.75017C10.9978 9.56651 10.9977 9.38287 10.9981 9.1992C10.9982 9.08481 10.9977 8.97042 10.997 8.85603C10.9967 8.77789 10.9971 8.69976 10.9976 8.62162C10.9967 8.28271 10.9967 8.28271 10.8203 8.00781C10.622 7.90864 10.4286 7.93086 10.2765 8.09029Z"
            fill="currentColor"
          />
          <path
            d="M11.5234 -1.20699e-06C11.6342 0.0666805 11.6542 0.113873 11.7188 0.234374C11.7614 0.413603 11.7323 0.521614 11.6455 0.683593C11.5486 0.784609 11.4661 0.800484 11.3281 0.808104C11.1851 0.798723 11.1358 0.783269 11.0132 0.700682C10.922 0.562487 10.8984 0.477978 10.8984 0.312499C11.0161 0.000741713 11.211 -0.0557247 11.5234 -1.20699e-06Z"
            fill="currentColor"
          />
        </g>
        <defs>
          <clipPath id="clip0_502_372">
            <rect width={20} height={20} fill="currentColor" />
          </clipPath>
        </defs>
      </svg>
    );
  }

  if (name === "cheveron-up") {
    return (
      <svg
        className={className}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.6569 16.2427L19.0711 14.8285L12.0001 7.75739L4.92896 14.8285L6.34317 16.2427L12.0001 10.5858L17.6569 16.2427Z"
          fill="currentColor"
        />
      </svg>
    );
  }

  if (name === "cheveron-down") {
    return (
      <svg
        className={className}
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
      >
        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
      </svg>
    );
  }

  if (name === "cheveron-right") {
    return (
      <svg
        className={className}
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
      >
        <polygon points="12.95 10.707 13.657 10 8 4.343 6.586 5.757 10.828 10 6.586 14.243 8 15.657 12.95 10.707" />
      </svg>
    );
  }

  if (name === "offers") {
    return (
      <svg
        className={className}
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0.625 9.375C0.626034 8.38075 1.02145 7.42752 1.72449 6.72449C2.42753 6.02145 3.38076 5.62603 4.375 5.625H7.48438C7.71172 5.61171 11.6734 5.33281 15.4461 2.16875C15.6283 2.01574 15.8503 1.9179 16.0862 1.88673C16.3221 1.85555 16.5619 1.89233 16.7776 1.99274C16.9933 2.09316 17.1758 2.25304 17.3038 2.4536C17.4317 2.65417 17.4998 2.88709 17.5 3.125V15.625C17.5 15.863 17.432 16.096 17.3041 16.2967C17.1762 16.4974 16.9937 16.6574 16.7779 16.7579C16.5622 16.8584 16.3223 16.8952 16.0864 16.8641C15.8505 16.8329 15.6283 16.7351 15.4461 16.582C12.4953 14.107 9.43047 13.3977 8.125 13.1992V15.6773C8.12526 15.8833 8.07462 16.0862 7.97758 16.2679C7.88053 16.4495 7.74009 16.6044 7.56875 16.7187L6.70938 17.2914C6.54327 17.4023 6.35302 17.4717 6.15455 17.4939C5.95609 17.5162 5.75519 17.4905 5.56867 17.4191C5.38216 17.3478 5.21546 17.2327 5.08254 17.0837C4.94962 16.9347 4.85434 16.7559 4.80469 16.5625L3.88516 13.0969C2.98302 12.9768 2.15517 12.5333 1.55551 11.8487C0.955854 11.1641 0.625191 10.2851 0.625 9.375ZM16.25 15.6195V3.125C12.9055 5.93046 9.48203 6.64062 8.125 6.81562V11.9312C9.48047 12.1094 12.9031 12.818 16.25 15.6195ZM6.01562 16.2445V16.2531L6.875 15.6805V13.125H5.1875L6.01562 16.2445ZM4.375 11.875H6.875V6.875H4.375C3.71196 6.875 3.07607 7.13839 2.60723 7.60723C2.13839 8.07607 1.875 8.71195 1.875 9.375C1.875 10.038 2.13839 10.6739 2.60723 11.1428C3.07607 11.6116 3.71196 11.875 4.375 11.875Z"
          fill="currentColor"
        />
      </svg>
    );
  }

  if (name === "orders") {
    return (
      <svg
        className={className}
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.475 5.16798L10.6 1.40626C10.4163 1.30477 10.2099 1.25153 10 1.25153C9.79013 1.25153 9.58369 1.30477 9.4 1.40626L2.525 5.16955C2.32866 5.27697 2.16477 5.43514 2.05043 5.62753C1.93609 5.81993 1.87551 6.03949 1.875 6.2633V13.7352C1.87551 13.959 1.93609 14.1785 2.05043 14.3709C2.16477 14.5633 2.32866 14.7215 2.525 14.8289L9.4 18.5922C9.58369 18.6937 9.79013 18.7469 10 18.7469C10.2099 18.7469 10.4163 18.6937 10.6 18.5922L17.475 14.8289C17.6713 14.7215 17.8352 14.5633 17.9496 14.3709C18.0639 14.1785 18.1245 13.959 18.125 13.7352V6.26408C18.1249 6.03987 18.0645 5.81982 17.9502 5.62698C17.8358 5.43414 17.6717 5.2756 17.475 5.16798ZM10 2.50001L16.2766 5.93751L13.9508 7.21095L7.67344 3.77345L10 2.50001ZM10 9.37501L3.72344 5.93751L6.37187 4.48751L12.6484 7.92501L10 9.37501ZM3.125 7.03126L9.375 10.4516V17.1539L3.125 13.736V7.03126ZM16.875 13.7328L10.625 17.1539V10.4547L13.125 9.08673V11.875C13.125 12.0408 13.1908 12.1997 13.3081 12.317C13.4253 12.4342 13.5842 12.5 13.75 12.5C13.9158 12.5 14.0747 12.4342 14.1919 12.317C14.3092 12.1997 14.375 12.0408 14.375 11.875V8.40236L16.875 7.03126V13.732V13.7328Z"
          fill="currentColor"
        />
      </svg>
    );
  }
  if (name === "my-favourites") {
    return (
      <svg
        className={className}
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.4359 10.0828L11.4062 8.59375L9.92188 4.56094C9.83397 4.32213 9.67493 4.11604 9.46622 3.97046C9.2575 3.82488 9.00916 3.74682 8.75469 3.74682C8.50022 3.74682 8.25187 3.82488 8.04316 3.97046C7.83444 4.11604 7.6754 4.32213 7.5875 4.56094L6.09375 8.59375L2.06094 10.0781C1.82213 10.166 1.61604 10.3251 1.47046 10.5338C1.32488 10.7425 1.24682 10.9908 1.24682 11.2453C1.24682 11.4998 1.32488 11.7481 1.47046 11.9568C1.61604 12.1656 1.82213 12.3246 2.06094 12.4125L6.09375 13.9062L7.57813 17.9391C7.66603 18.1779 7.82507 18.384 8.03378 18.5295C8.2425 18.6751 8.49084 18.7532 8.74531 18.7532C8.99978 18.7532 9.24813 18.6751 9.45684 18.5295C9.66556 18.384 9.8246 18.1779 9.9125 17.9391L11.4062 13.9062L15.4391 12.4219C15.6779 12.334 15.884 12.1749 16.0295 11.9662C16.1751 11.7575 16.2532 11.5092 16.2532 11.2547C16.2532 11.0002 16.1751 10.7519 16.0295 10.5432C15.884 10.3344 15.6779 10.1754 15.4391 10.0875L15.4359 10.0828ZM10.7031 12.8297C10.6183 12.861 10.5412 12.9103 10.4773 12.9742C10.4134 13.0381 10.3641 13.1152 10.3328 13.2L8.75 17.4883L7.17031 13.2031C7.1391 13.1174 7.08949 13.0395 7.02498 12.975C6.96046 12.9105 6.88261 12.8609 6.79688 12.8297L2.51172 11.25L6.79688 9.67031C6.88261 9.6391 6.96046 9.58949 7.02498 9.52497C7.08949 9.46046 7.1391 9.3826 7.17031 9.29688L8.75 5.01172L10.3297 9.29688C10.361 9.38171 10.4103 9.45875 10.4742 9.52269C10.5381 9.58662 10.6152 9.63592 10.7 9.66719L14.9883 11.25L10.7031 12.8297ZM11.25 3.125C11.25 2.95924 11.3158 2.80027 11.4331 2.68306C11.5503 2.56585 11.7092 2.5 11.875 2.5H13.125V1.25C13.125 1.08424 13.1908 0.925268 13.3081 0.808058C13.4253 0.690848 13.5842 0.625 13.75 0.625C13.9158 0.625 14.0747 0.690848 14.1919 0.808058C14.3092 0.925268 14.375 1.08424 14.375 1.25V2.5H15.625C15.7908 2.5 15.9497 2.56585 16.0669 2.68306C16.1842 2.80027 16.25 2.95924 16.25 3.125C16.25 3.29076 16.1842 3.44973 16.0669 3.56694C15.9497 3.68415 15.7908 3.75 15.625 3.75H14.375V5C14.375 5.16576 14.3092 5.32473 14.1919 5.44194C14.0747 5.55915 13.9158 5.625 13.75 5.625C13.5842 5.625 13.4253 5.55915 13.3081 5.44194C13.1908 5.32473 13.125 5.16576 13.125 5V3.75H11.875C11.7092 3.75 11.5503 3.68415 11.4331 3.56694C11.3158 3.44973 11.25 3.29076 11.25 3.125ZM19.375 6.875C19.375 7.04076 19.3092 7.19973 19.1919 7.31694C19.0747 7.43415 18.9158 7.5 18.75 7.5H18.125V8.125C18.125 8.29076 18.0592 8.44973 17.9419 8.56694C17.8247 8.68415 17.6658 8.75 17.5 8.75C17.3342 8.75 17.1753 8.68415 17.0581 8.56694C16.9408 8.44973 16.875 8.29076 16.875 8.125V7.5H16.25C16.0842 7.5 15.9253 7.43415 15.8081 7.31694C15.6908 7.19973 15.625 7.04076 15.625 6.875C15.625 6.70924 15.6908 6.55027 15.8081 6.43306C15.9253 6.31585 16.0842 6.25 16.25 6.25H16.875V5.625C16.875 5.45924 16.9408 5.30027 17.0581 5.18306C17.1753 5.06585 17.3342 5 17.5 5C17.6658 5 17.8247 5.06585 17.9419 5.18306C18.0592 5.30027 18.125 5.45924 18.125 5.625V6.25H18.75C18.9158 6.25 19.0747 6.31585 19.1919 6.43306C19.3092 6.55027 19.375 6.70924 19.375 6.875Z"
          fill="currentColor"
        />
      </svg>
    );
  }
  if (name === "my-subscriptions") {
    return (
      <svg
        className={className}
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.5 3.75H2.5C2.16848 3.75 1.85054 3.8817 1.61612 4.11612C1.3817 4.35054 1.25 4.66848 1.25 5V15C1.25 15.3315 1.3817 15.6495 1.61612 15.8839C1.85054 16.1183 2.16848 16.25 2.5 16.25H17.5C17.8315 16.25 18.1495 16.1183 18.3839 15.8839C18.6183 15.6495 18.75 15.3315 18.75 15V5C18.75 4.66848 18.6183 4.35054 18.3839 4.11612C18.1495 3.8817 17.8315 3.75 17.5 3.75ZM17.5 5V6.875H2.5V5H17.5ZM17.5 15H2.5V8.125H17.5V15ZM16.25 13.125C16.25 13.2908 16.1842 13.4497 16.0669 13.5669C15.9497 13.6842 15.7908 13.75 15.625 13.75H13.125C12.9592 13.75 12.8003 13.6842 12.6831 13.5669C12.5658 13.4497 12.5 13.2908 12.5 13.125C12.5 12.9592 12.5658 12.8003 12.6831 12.6831C12.8003 12.5658 12.9592 12.5 13.125 12.5H15.625C15.7908 12.5 15.9497 12.5658 16.0669 12.6831C16.1842 12.8003 16.25 12.9592 16.25 13.125ZM11.25 13.125C11.25 13.2908 11.1842 13.4497 11.0669 13.5669C10.9497 13.6842 10.7908 13.75 10.625 13.75H9.375C9.20924 13.75 9.05027 13.6842 8.93306 13.5669C8.81585 13.4497 8.75 13.2908 8.75 13.125C8.75 12.9592 8.81585 12.8003 8.93306 12.6831C9.05027 12.5658 9.20924 12.5 9.375 12.5H10.625C10.7908 12.5 10.9497 12.5658 11.0669 12.6831C11.1842 12.8003 11.25 12.9592 11.25 13.125Z"
          fill="currentColor"
        />
      </svg>
    );
  }

  if (name == "statistic-reports") {
    return (
      <svg
        className={className}
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_502_63)">
          <path
            d="M6.875 11.25V10C6.875 9.83424 6.94085 9.67527 7.05806 9.55806C7.17527 9.44085 7.33424 9.375 7.5 9.375C7.66576 9.375 7.82473 9.44085 7.94194 9.55806C8.05915 9.67527 8.125 9.83424 8.125 10V11.25C8.125 11.4158 8.05915 11.5747 7.94194 11.6919C7.82473 11.8092 7.66576 11.875 7.5 11.875C7.33424 11.875 7.17527 11.8092 7.05806 11.6919C6.94085 11.5747 6.875 11.4158 6.875 11.25ZM10 11.875C10.1658 11.875 10.3247 11.8092 10.4419 11.6919C10.5592 11.5747 10.625 11.4158 10.625 11.25V9.375C10.625 9.20924 10.5592 9.05027 10.4419 8.93306C10.3247 8.81585 10.1658 8.75 10 8.75C9.83424 8.75 9.67527 8.81585 9.55806 8.93306C9.44085 9.05027 9.375 9.20924 9.375 9.375V11.25C9.375 11.4158 9.44085 11.5747 9.55806 11.6919C9.67527 11.8092 9.83424 11.875 10 11.875ZM12.5 11.875C12.6658 11.875 12.8247 11.8092 12.9419 11.6919C13.0592 11.5747 13.125 11.4158 13.125 11.25V8.75C13.125 8.58424 13.0592 8.42527 12.9419 8.30806C12.8247 8.19085 12.6658 8.125 12.5 8.125C12.3342 8.125 12.1753 8.19085 12.0581 8.30806C11.9408 8.42527 11.875 8.58424 11.875 8.75V11.25C11.875 11.4158 11.9408 11.5747 12.0581 11.6919C12.1753 11.8092 12.3342 11.875 12.5 11.875ZM16.875 6.25V13.75H17.5C17.6658 13.75 17.8247 13.8158 17.9419 13.9331C18.0592 14.0503 18.125 14.2092 18.125 14.375C18.125 14.5408 18.0592 14.6997 17.9419 14.8169C17.8247 14.9342 17.6658 15 17.5 15H10.625V16.3578C11.042 16.5052 11.3935 16.7954 11.6173 17.1769C11.8411 17.5584 11.9228 18.0067 11.848 18.4426C11.7732 18.8786 11.5467 19.274 11.2085 19.5591C10.8704 19.8442 10.4423 20.0006 10 20.0006C9.5577 20.0006 9.12963 19.8442 8.79146 19.5591C8.45329 19.274 8.2268 18.8786 8.152 18.4426C8.07721 18.0067 8.15893 17.5584 8.38273 17.1769C8.60653 16.7954 8.95799 16.5052 9.375 16.3578V15H2.5C2.33424 15 2.17527 14.9342 2.05806 14.8169C1.94085 14.6997 1.875 14.5408 1.875 14.375C1.875 14.2092 1.94085 14.0503 2.05806 13.9331C2.17527 13.8158 2.33424 13.75 2.5 13.75H3.125V6.25C2.79348 6.25 2.47554 6.1183 2.24112 5.88388C2.0067 5.64946 1.875 5.33152 1.875 5V3.75C1.875 3.41848 2.0067 3.10054 2.24112 2.86612C2.47554 2.6317 2.79348 2.5 3.125 2.5H16.875C17.2065 2.5 17.5245 2.6317 17.7589 2.86612C17.9933 3.10054 18.125 3.41848 18.125 3.75V5C18.125 5.33152 17.9933 5.64946 17.7589 5.88388C17.5245 6.1183 17.2065 6.25 16.875 6.25ZM10.625 18.125C10.625 18.0014 10.5883 17.8805 10.5197 17.7778C10.451 17.675 10.3534 17.5949 10.2392 17.5476C10.125 17.5003 9.99931 17.4879 9.87807 17.512C9.75683 17.5361 9.64547 17.5956 9.55806 17.6831C9.47065 17.7705 9.41112 17.8818 9.38701 18.0031C9.36289 18.1243 9.37527 18.25 9.42257 18.3642C9.46988 18.4784 9.54999 18.576 9.65277 18.6447C9.75555 18.7133 9.87639 18.75 10 18.75C10.1658 18.75 10.3247 18.6842 10.4419 18.5669C10.5592 18.4497 10.625 18.2908 10.625 18.125ZM3.125 5H16.875V3.75H3.125V5ZM15.625 6.25H4.375V13.75H15.625V6.25Z"
            fill="currentColor"
          />
        </g>
        <defs>
          <clipPath id="clip0_502_63">
            <rect width={20} height={20} fill="currentColor" />
          </clipPath>
        </defs>
      </svg>
    );
  }

  if (name === "notifications") {
    return (
      <svg
        className={className}
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.499 5.55501C17.3518 5.6312 17.1805 5.64585 17.0226 5.59575C16.8646 5.54565 16.7331 5.43489 16.6568 5.28782C16.0602 4.1093 15.1572 3.11321 14.0427 2.40423C13.9732 2.36042 13.9131 2.30336 13.8657 2.23631C13.8183 2.16925 13.7846 2.09352 13.7664 2.01343C13.7483 1.93334 13.7461 1.85046 13.76 1.76953C13.7739 1.68859 13.8036 1.61119 13.8474 1.54173C13.8912 1.47227 13.9483 1.41212 14.0153 1.36472C14.0824 1.31731 14.1581 1.28358 14.2382 1.26544C14.3183 1.24731 14.4012 1.24512 14.4821 1.25902C14.563 1.27291 14.6404 1.30261 14.7099 1.34642C16.0088 2.17809 17.0635 3.33983 17.7661 4.71282C17.8423 4.85995 17.857 5.0313 17.8069 5.18922C17.7568 5.34715 17.646 5.47871 17.499 5.55501ZM2.7888 5.62532C2.9033 5.62528 3.01559 5.59377 3.11341 5.53426C3.21123 5.47474 3.29082 5.38949 3.34349 5.28782C3.94005 4.1093 4.84303 3.11321 5.95755 2.40423C6.09783 2.31576 6.19721 2.17518 6.23384 2.01343C6.27046 1.85168 6.24134 1.682 6.15286 1.54173C6.06439 1.40146 5.92381 1.30207 5.76206 1.26544C5.60031 1.22882 5.43064 1.25794 5.29036 1.34642C3.99146 2.17809 2.93675 3.33983 2.23411 4.71282C2.18477 4.80807 2.16077 4.91442 2.16443 5.02163C2.16808 5.12884 2.19927 5.23331 2.25499 5.32497C2.31072 5.41664 2.38909 5.49242 2.48258 5.54502C2.57607 5.59762 2.68153 5.62528 2.7888 5.62532ZM17.3279 13.7456C17.4386 13.9355 17.4973 14.1512 17.498 14.371C17.4988 14.5908 17.4416 14.8069 17.3322 14.9976C17.2227 15.1882 17.065 15.3466 16.8748 15.4568C16.6846 15.567 16.4688 15.6252 16.249 15.6253H13.0615C12.918 16.3318 12.5348 16.9669 11.9766 17.4231C11.4185 17.8792 10.7198 18.1285 9.99896 18.1285C9.27811 18.1285 8.57943 17.8792 8.0213 17.4231C7.46316 16.9669 7.0799 16.3318 6.93646 15.6253H3.74896C3.52929 15.6249 3.31361 15.5666 3.12365 15.4563C2.93369 15.3459 2.77616 15.1875 2.66693 14.9969C2.55769 14.8063 2.50061 14.5903 2.50143 14.3706C2.50226 14.151 2.56095 13.9354 2.67161 13.7456C3.37552 12.5308 3.74896 10.8034 3.74896 8.75032C3.74896 7.09272 4.40744 5.50301 5.57954 4.33091C6.75164 3.1588 8.34135 2.50032 9.99896 2.50032C11.6566 2.50032 13.2463 3.1588 14.4184 4.33091C15.5905 5.50301 16.249 7.09272 16.249 8.75032C16.249 10.8027 16.6224 12.53 17.3279 13.7456ZM11.7661 15.6253H8.23177C8.36126 15.9905 8.60068 16.3066 8.91713 16.5301C9.23358 16.7537 9.61151 16.8737 9.99896 16.8737C10.3864 16.8737 10.7643 16.7537 11.0808 16.5301C11.3972 16.3066 11.6367 15.9905 11.7661 15.6253ZM16.249 14.3753C15.4177 12.948 14.999 11.0558 14.999 8.75032C14.999 7.42424 14.4722 6.15247 13.5345 5.21479C12.5968 4.27711 11.325 3.75032 9.99896 3.75032C8.67287 3.75032 7.4011 4.27711 6.46342 5.21479C5.52574 6.15247 4.99896 7.42424 4.99896 8.75032C4.99896 11.0566 4.57864 12.9488 3.74896 14.3753H16.249Z"
          fill="currentColor"
        />
      </svg>
    );
  }

  if (name === "my-chats") {
    return (
      <svg
        className={className}
        width={18}
        height={18}
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.1305 13.5906C17.5623 12.6949 17.7742 11.7091 17.7486 10.715C17.7229 9.72096 17.4604 8.74737 16.9829 7.87513C16.5054 7.00288 15.8267 6.25716 15.0031 5.6999C14.1796 5.14265 13.2349 4.78995 12.2477 4.67109C11.9192 3.90732 11.4416 3.21676 10.8429 2.63986C10.2441 2.06296 9.53635 1.61132 8.76091 1.31138C7.98547 1.01144 7.15798 0.869225 6.3269 0.893075C5.49582 0.916925 4.67785 1.10635 3.92089 1.45027C3.16393 1.79419 2.48319 2.28568 1.91855 2.89596C1.35391 3.50625 0.916705 4.22306 0.632545 5.00441C0.348385 5.78577 0.22298 6.61597 0.263673 7.4464C0.304367 8.27683 0.51034 9.09079 0.869532 9.84063L0.301563 11.7711C0.23806 11.9866 0.233813 12.2152 0.289272 12.433C0.34473 12.6507 0.457842 12.8494 0.616708 13.0083C0.775574 13.1672 0.974321 13.2803 1.19204 13.3357C1.40976 13.3912 1.6384 13.3869 1.85391 13.3234L3.78438 12.7555C4.40424 13.0533 5.06882 13.2473 5.75156 13.3297C6.08327 14.1069 6.56912 14.8087 7.17977 15.3928C7.79041 15.9769 8.51316 16.4311 9.30432 16.728C10.0955 17.0248 10.9386 17.1582 11.7828 17.1199C12.6269 17.0817 13.4545 16.8726 14.2156 16.5055L16.1461 17.0734C16.3615 17.1369 16.5901 17.1411 16.8077 17.0857C17.0253 17.0302 17.224 16.9172 17.3828 16.7584C17.5417 16.5997 17.6548 16.4011 17.7103 16.1835C17.7659 15.9659 17.7618 15.7373 17.6984 15.5219L17.1305 13.5906ZM3.84375 11.4609C3.78402 11.4611 3.72461 11.4695 3.66719 11.4859L1.5 12.125L2.13828 9.95625C2.18368 9.79957 2.16601 9.63134 2.08906 9.4875C1.52368 8.43014 1.35709 7.20482 1.61966 6.03489C1.88223 4.86497 2.55648 3.82836 3.51949 3.11402C4.4825 2.39969 5.67015 2.0552 6.86593 2.14336C8.06171 2.23152 9.18599 2.74645 10.0338 3.59429C10.8817 4.44213 11.3966 5.56642 11.4848 6.7622C11.5729 7.95798 11.2284 9.14563 10.5141 10.1086C9.79977 11.0716 8.76316 11.7459 7.59323 12.0085C6.42331 12.271 5.19799 12.1044 4.14063 11.5391C4.0497 11.4887 3.94767 11.4619 3.84375 11.4609ZM15.8586 13.7055L16.5 15.875L14.3313 15.2367C14.1746 15.1913 14.0063 15.209 13.8625 15.2859C12.7116 15.9005 11.3662 16.0412 10.113 15.6781C8.85981 15.315 7.79801 14.4768 7.15391 13.3422C8.00967 13.2529 8.83774 12.9877 9.58618 12.5632C10.3346 12.1388 10.9872 11.5642 11.5032 10.8756C12.0191 10.1871 12.3871 9.39929 12.5842 8.56175C12.7813 7.72421 12.8031 6.85498 12.6484 6.0086C13.3941 6.18435 14.0893 6.52908 14.6806 7.01618C15.2718 7.50327 15.7432 8.11971 16.0584 8.81791C16.3736 9.51611 16.5242 10.2774 16.4985 11.043C16.4728 11.8086 16.2715 12.5581 15.9102 13.2336C15.8324 13.3783 15.8147 13.5478 15.8609 13.7055H15.8586Z"
          fill="currentColor"
        />
      </svg>
    );
  }

  if (name === "shopping-cart") {
    return (
      <svg
        className={className}
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6 16C4.9 16 4.01 16.9 4.01 18C4.01 19.1 4.9 20 6 20C7.1 20 8 19.1 8 18C8 16.9 7.1 16 6 16ZM0 0V2H2L5.6 9.59L4.2 12.04C4.08 12.32 4 12.65 4 13C4 14.1 4.9 15 6 15H18V13H6.42C6.28 13 6.17 12.89 6.17 12.75L6.2 12.63L7.1 11H14.55C15.3 11 15.96 10.59 16.3 9.97L19.88 3.48C19.96 3.34 20 3.17 20 3C20 2.45 19.55 2 19 2H4.21L3.27 0H0ZM16 16C14.9 16 14.01 16.9 14.01 18C14.01 19.1 14.9 20 16 20C17.1 20 18 19.1 18 18C18 16.9 17.1 16 16 16Z"
          fill="currentColor"
        />
      </svg>
    );
  }

  if (name === "support") {
    return (
      <svg
        className={className}
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.7727 4.27031C15.025 3.51514 14.1357 2.91486 13.1558 2.50383C12.1758 2.09281 11.1244 1.87912 10.0617 1.875H10C7.84512 1.875 5.77849 2.73102 4.25476 4.25476C2.73102 5.77849 1.875 7.84512 1.875 10V14.375C1.875 14.8723 2.07254 15.3492 2.42417 15.7008C2.77581 16.0525 3.25272 16.25 3.75 16.25H5C5.49728 16.25 5.9742 16.0525 6.32583 15.7008C6.67746 15.3492 6.875 14.8723 6.875 14.375V11.25C6.875 10.7527 6.67746 10.2758 6.32583 9.92417C5.9742 9.57254 5.49728 9.375 5 9.375H3.15313C3.27366 8.07182 3.76315 6.83 4.56424 5.79508C5.36532 4.76016 6.44481 3.97502 7.67617 3.53169C8.90753 3.08836 10.2398 3.0052 11.5167 3.29196C12.7936 3.57872 13.9624 4.22352 14.8859 5.15078C16.0148 6.28539 16.7091 7.78052 16.8477 9.375H15C14.5027 9.375 14.0258 9.57254 13.6742 9.92417C13.3225 10.2758 13.125 10.7527 13.125 11.25V14.375C13.125 14.8723 13.3225 15.3492 13.6742 15.7008C14.0258 16.0525 14.5027 16.25 15 16.25H16.875C16.875 16.7473 16.6775 17.2242 16.3258 17.5758C15.9742 17.9275 15.4973 18.125 15 18.125H10.625C10.4592 18.125 10.3003 18.1908 10.1831 18.3081C10.0658 18.4253 10 18.5842 10 18.75C10 18.9158 10.0658 19.0747 10.1831 19.1919C10.3003 19.3092 10.4592 19.375 10.625 19.375H15C15.8288 19.375 16.6237 19.0458 17.2097 18.4597C17.7958 17.8737 18.125 17.0788 18.125 16.25V10C18.1291 8.93717 17.9234 7.88398 17.5197 6.90077C17.1161 5.91757 16.5224 5.02368 15.7727 4.27031ZM5 10.625C5.16576 10.625 5.32473 10.6908 5.44194 10.8081C5.55915 10.9253 5.625 11.0842 5.625 11.25V14.375C5.625 14.5408 5.55915 14.6997 5.44194 14.8169C5.32473 14.9342 5.16576 15 5 15H3.75C3.58424 15 3.42527 14.9342 3.30806 14.8169C3.19085 14.6997 3.125 14.5408 3.125 14.375V10.625H5ZM15 15C14.8342 15 14.6753 14.9342 14.5581 14.8169C14.4408 14.6997 14.375 14.5408 14.375 14.375V11.25C14.375 11.0842 14.4408 10.9253 14.5581 10.8081C14.6753 10.6908 14.8342 10.625 15 10.625H16.875V15H15Z"
          fill="currentColor"
        />
      </svg>
    );
  }

  if (name === "users") {
    return (
      <svg
        className={className}
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
      >
        <path d="M7 8a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0 1c2.15 0 4.2.4 6.1 1.09L12 16h-1.25L10 20H4l-.75-4H2L.9 10.09A17.93 17.93 0 0 1 7 9zm8.31.17c1.32.18 2.59.48 3.8.92L18 16h-1.25L16 20h-3.96l.37-2h1.25l1.65-8.83zM13 0a4 4 0 1 1-1.33 7.76 5.96 5.96 0 0 0 0-7.52C12.1.1 12.53 0 13 0z" />
      </svg>
    );
  }

  if (name === "admin") {
    return (
      <svg
        className={className}
        viewBox="0 0 41 46"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.63148 14.1654C9.74945 14.8845 10.0275 15.3367 10.3 15.626C10.9488 19.9516 14.5722 23.9683 17.8782 23.9683C21.7348 23.9683 25.2458 19.623 25.9256 15.6344C26.2008 15.3507 26.4845 14.8929 26.6053 14.1682C26.8244 13.3565 27.1109 11.9464 26.6109 11.2891C26.5828 11.2554 26.5548 11.2245 26.5295 11.1936C26.9957 9.49149 27.5884 5.97482 25.4762 3.57888C25.2908 3.33732 24.1111 1.92728 21.5803 1.18013L20.3809 0.761608C18.3923 0.146471 17.1395 0.0116471 17.089 0.00322053C17.0019 -0.005206 16.9064 0.00322052 16.8193 0.0256913C16.7491 0.0453532 16.516 0.107148 16.3306 0.0846769C15.8559 0.0172647 15.1424 0.261634 15.016 0.309384C14.8475 0.373988 10.9994 1.91885 9.83091 5.50293C9.72137 5.79505 9.2551 7.31744 9.87585 11.056C9.78316 11.1178 9.6989 11.1936 9.62867 11.2891C9.1287 11.9464 9.40958 13.3536 9.63148 14.1654ZM11.1426 6.01414C11.1511 5.99729 11.1567 5.97763 11.1651 5.95516C12.1173 3.00025 15.4935 1.63516 15.516 1.62392C15.6705 1.56494 15.971 1.48067 16.1311 1.48067C16.4626 1.52561 16.8446 1.46663 17.0524 1.41888C17.3923 1.46663 18.4484 1.63797 19.9399 2.09862L21.1533 2.51432C23.4032 3.18002 24.3582 4.42715 24.3639 4.43557C24.3807 4.45523 24.3976 4.47209 24.4088 4.48894C26.3609 6.68826 25.3582 10.3285 25.0015 11.3903C24.92 11.6403 24.979 11.9155 25.1616 12.1009C25.2543 12.1964 25.3638 12.261 25.4846 12.2947C25.4902 12.6402 25.3919 13.2722 25.2374 13.8255C25.2318 13.8508 25.2262 13.8789 25.2206 13.9042C25.1616 14.2946 25.0296 14.5783 24.8526 14.7075C24.7037 14.8199 24.5998 14.9912 24.5717 15.1766C24.1054 18.5191 21.0101 22.561 17.8811 22.561C15.2464 22.561 12.1089 18.8393 11.6651 15.1906C11.6426 14.9996 11.5443 14.8283 11.387 14.7131C11.2101 14.5783 11.078 14.2918 11.0191 13.907C11.0162 13.8789 11.0106 13.8508 11.0022 13.8255C10.8589 13.3115 10.7663 12.7245 10.7578 12.3706C10.8898 12.3565 11.0219 12.3116 11.1342 12.2217C11.3421 12.0588 11.4432 11.7947 11.3926 11.5335C10.6202 7.47755 11.1426 6.01414 11.1426 6.01414Z"
          fill="white"
        />
        <path
          d="M31.8017 26.2996C29.7288 24.9317 26.5913 23.9402 26.5913 23.9402C23.4426 22.8279 23.4201 21.7212 23.4201 21.7212C17.2322 33.9228 12.5331 21.7605 12.5331 21.7605C12.1005 23.4065 5.72723 25.339 5.72723 25.339C3.86497 26.0524 3.07849 27.1254 3.07849 27.1254C0.325825 31.2095 0 40.2961 0 40.2961C0.0365149 42.3718 0.932535 42.5881 0.932535 42.5881C7.26366 45.4138 17.2013 45.9166 17.2013 45.9166C19.9681 45.9756 22.5213 45.7846 24.788 45.484C22.6168 43.4532 21.2433 40.5742 21.2433 37.3749C21.2405 31.437 25.9312 26.5861 31.8017 26.2996Z"
          fill="white"
        />
        <path
          d="M31.9279 27.9849C26.9618 27.9849 22.9199 32.024 22.9199 36.9928C22.9199 41.9673 26.9618 46.0008 31.9279 46.0008C36.8995 46.0008 40.9358 41.9645 40.9358 36.9928C40.9358 32.0268 36.8967 27.9849 31.9279 27.9849ZM31.3352 41.4224C31.1442 41.6106 30.8352 41.6106 30.6442 41.4224L26.3102 37.0546C26.1192 36.8692 26.1192 36.5547 26.3102 36.3665L27.3467 35.33C27.5377 35.1418 27.8466 35.1418 28.0376 35.33L30.9953 38.3102L36.7394 32.5689C36.9248 32.3779 37.2394 32.3779 37.4304 32.5689L38.4612 33.5998C38.6522 33.7908 38.6522 34.1053 38.4612 34.2963L31.3352 41.4224Z"
          fill="white"
        />
      </svg>
    );
  }

  if (name === "moderator") {
    return (
      <svg
        className={className}
        viewBox="0 0 46 41"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.38131 18.1307C6.8883 17.9633 5.73279 17.5026 4.5803 16.5356C3.06818 15.267 1.5 13.085 1.5 9.30464V3.96595L7.38 1.58589L13.26 3.96595V9.30464C13.26 13.0851 11.6918 15.2673 10.1796 16.5362C9.02919 17.5015 7.87578 17.9625 7.38131 18.1307Z"
          stroke="currentColor"
          strokeWidth={3}
        />
        <path
          d="M28.4266 17.6333C24.4312 17.6333 21.1799 14.382 21.1799 10.3867C21.1799 6.3913 24.4312 3.14 28.4266 3.14C32.4219 3.14 35.6733 6.3913 35.6733 10.3867C35.6733 14.382 32.4219 17.6333 28.4266 17.6333Z"
          stroke="currentColor"
          strokeWidth={3}
        />
        <path
          d="M42.6402 39.5H14.2136C13.2315 39.5 12.4336 38.7021 12.4336 37.72V35.5333C12.4336 29.7274 17.1543 25.0066 22.9603 25.0066H33.8936C39.6995 25.0066 44.4202 29.7274 44.4202 35.5333V37.72C44.4202 38.7021 43.6224 39.5 42.6402 39.5Z"
          stroke="currentColor"
          strokeWidth={3}
        />
      </svg>
    );
  }

  if (name === "clients") {
    return (
      <svg
        className={className}
        viewBox="0 0 53 46"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M26.4038 17.9462C31.3515 17.9462 35.3768 13.9209 35.3768 8.97309C35.3768 4.02531 31.3515 0 26.4038 0C21.456 0 17.4307 4.02531 17.4307 8.97309C17.4307 13.9209 21.456 17.9462 26.4038 17.9462ZM26.4038 3.09417C29.6454 3.09417 32.2827 5.73143 32.2827 8.97309C32.2827 12.2147 29.6454 14.852 26.4038 14.852C23.1621 14.852 20.5248 12.2147 20.5248 8.97309C20.5248 5.73143 23.1621 3.09417 26.4038 3.09417Z"
          fill="white"
        />
        <path
          d="M44.5559 17.9462C47.6838 17.9462 50.2286 15.4014 50.2286 12.2735C50.2286 9.14562 47.6838 6.60088 44.5559 6.60088C41.428 6.60088 38.8833 9.14562 38.8833 12.2735C38.8833 15.4014 41.428 17.9462 44.5559 17.9462ZM44.5559 9.69504C45.9777 9.69504 47.1344 10.8517 47.1344 12.2735C47.1344 13.6953 45.9777 14.852 44.5559 14.852C43.1342 14.852 41.9775 13.6953 41.9775 12.2735C41.9775 10.8517 43.1342 9.69504 44.5559 9.69504Z"
          fill="white"
        />
        <path
          d="M45.804 21.4529H43.4112C40.5915 21.4529 38.1574 23.1188 37.0482 25.5116C34.7646 23.0188 31.4845 21.4529 27.8454 21.4529H24.9619C21.3227 21.4529 18.0426 23.0188 15.759 25.5116C14.6498 23.1188 12.2157 21.4529 9.39596 21.4529H7.00314C3.14161 21.4529 0 24.5755 0 28.4138V39.6776C0 41.3439 1.36082 42.6995 3.03352 42.6995H12.4942C12.6528 44.5457 14.2052 46 16.0919 46H36.7153C38.6019 46 40.1544 44.5457 40.3131 42.6995H49.6656C51.398 42.6995 52.8072 41.2958 52.8072 39.5703V28.4138C52.8071 24.5755 49.6655 21.4529 45.804 21.4529ZM3.09417 28.4138C3.09417 26.2817 4.84774 24.5471 7.00314 24.5471H9.39596C11.5514 24.5471 13.3049 26.2817 13.3049 28.4138V29.4731C12.2646 32.1819 12.4798 33.8053 12.4798 39.6054H3.09417V28.4138ZM37.2332 42.3879C37.2332 42.6735 37.0009 42.9058 36.7153 42.9058H16.0918C15.8063 42.9058 15.574 42.6734 15.574 42.3879V33.935C15.574 28.7584 19.7854 24.5471 24.9618 24.5471H27.8452C33.0218 24.5471 37.2332 28.7584 37.2332 33.9349V42.3879ZM49.713 39.5703C49.713 39.6236 50.2897 39.6054 40.3273 39.6054C40.3273 33.7624 40.5409 32.1775 39.5022 29.4731V28.4138C39.5022 26.2817 41.2558 24.5471 43.4112 24.5471H45.804C47.9594 24.5471 49.713 26.2817 49.713 28.4138V39.5703Z"
          fill="white"
        />
        <path
          d="M8.25126 17.9462C11.3792 17.9462 13.9239 15.4014 13.9239 12.2735C13.9239 9.14562 11.3792 6.60088 8.25126 6.60088C5.12336 6.60088 2.57861 9.14562 2.57861 12.2735C2.57861 15.4014 5.12336 17.9462 8.25126 17.9462ZM8.25126 9.69504C9.67303 9.69504 10.8297 10.8517 10.8297 12.2735C10.8297 13.6953 9.67303 14.852 8.25126 14.852C6.82948 14.852 5.67278 13.6953 5.67278 12.2735C5.67278 10.8517 6.82948 9.69504 8.25126 9.69504Z"
          fill="white"
        />
      </svg>
    );
  }

  if (name === "play") {
    return (
      <svg
        className={className}
        viewBox="0 0 25 28"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M23.5382 12.331L2.57807 0.230371C2.04561 -0.0767904 1.39123 -0.0767904 0.859355 0.230371C0.327484 0.536952 0 1.10424 0 1.71857V25.9222C0 26.5365 0.327484 27.1038 0.859355 27.4104C1.12529 27.566 1.422 27.6409 1.71871 27.6409C2.01542 27.6409 2.31213 27.5637 2.57807 27.4104L23.5382 15.3097C24.0712 15.0026 24.397 14.4353 24.397 13.821C24.397 13.2066 24.0701 12.6388 23.5382 12.331Z"
          fill="white"
        />
      </svg>
    );
  }

  if (name === "paused") {
    return (
      <svg
        className={className}
        viewBox="0 0 24 30"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.24769 25.4831C8.24769 27.7605 6.40126 29.607 4.12385 29.607C1.84643 29.607 0 27.7605 0 25.4831V4.12385C0 1.84643 1.84643 0 4.12385 0C6.40126 0 8.24769 1.84643 8.24769 4.12385V25.4831Z"
          fill="white"
        />
        <path
          d="M23.7247 25.4831C23.7247 27.7606 21.8783 29.607 19.6009 29.607C17.3235 29.607 15.4771 27.7606 15.4771 25.4831V4.12385C15.4777 1.84644 17.3241 9.53674e-06 19.6009 9.53674e-06C21.8783 9.53674e-06 23.7247 1.84644 23.7247 4.12385V25.4831Z"
          fill="white"
        />
      </svg>
    );
  }

  if (name === "checked") {
    return (
      <svg
        className={className}
        viewBox="0 0 41 27"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M38.8622 0L14.7363 22.9353L2.13781 10.9587L0 12.991L14.7363 27L41 2.03233L38.8622 0Z"
          fill="white"
        />
      </svg>
    );
  }

  if (name === "imo") {
    return (
      <svg
        className={className}
        viewBox="0 0 131 128"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M23.093 25.2806L39.1295 9.33316L60.5827 4.25494L76.1916 6.39314L96.2443 13.1641L125.014 44.396V77.6664L105.414 110.808L74.0534 122.212L43.1671 116.51L28.549 112.59L15.9657 114.016L5.98743 111.521L9.55109 105.107L17.0348 97.6229L5.98743 70.5391L11.9958 45.9498L23.093 25.2806Z"
          fill="white"
        />
        <path
          d="M66.7978 127.536C53.4661 127.536 41.0895 123.442 30.8582 116.439C26.033 118.98 15.1781 119.543 10.442 118.98C3.557 118.164 -3.66298 112.462 3.28973 106.913C14.0591 98.325 11.169 94.9217 11.169 94.9217C6.00168 85.7097 3.05809 75.0828 3.05809 63.7682C3.05809 28.5485 31.5959 0 66.7978 0C102 0 130.537 28.5485 130.537 63.7682C130.537 98.9879 102 127.536 66.7978 127.536ZM66.7978 9.33323C36.5316 9.33323 11.9958 33.8797 11.9958 64.1602C11.9958 74.5803 14.9037 84.3234 19.9463 92.6196C19.9463 92.6196 23.5313 101.939 14.3264 108.877C20.0924 110.823 32.2766 107.245 32.9038 107.245C42.2299 114.597 54.0007 118.987 66.7978 118.987C97.064 118.987 121.596 94.4406 121.596 64.1602C121.596 33.8797 97.064 9.33323 66.7978 9.33323ZM96.1267 79.8403C89.4092 79.8403 84.2455 75.4249 84.2455 68.0018C84.2455 60.5787 89.1313 55.8319 96.5045 55.8319C103.55 55.8319 108.247 60.6714 108.247 67.5778C108.243 75.988 102.278 79.8403 96.1267 79.8403ZM96.312 60.903C92.8838 60.903 91.6151 64.4275 91.6151 67.8094C91.6151 71.7544 93.258 74.7158 96.312 74.7158C99.0846 74.7158 100.866 71.9432 100.866 67.8094C100.87 64.3811 99.555 60.903 96.312 60.903ZM76.3555 79.3236C76.3555 79.3236 73.6329 79.3236 72.5103 79.3236C70.6822 79.3236 71.0492 76.6829 71.0492 76.6829V66.8223C71.0492 63.4867 69.9694 61.5587 67.6673 61.5587C66.0245 61.5587 64.8484 62.6848 64.378 64.0497C64.1892 64.5665 64.0965 65.3184 64.0965 65.8814V77.0642C64.0965 77.0642 64.1179 79.32 62.1187 79.32C60.6683 79.32 58.9435 79.32 58.9435 79.32C58.9435 79.32 57.1438 79.181 57.1438 76.8718C57.1438 73.1371 57.1438 66.4445 57.1438 66.4445C57.1438 63.533 56.1104 61.5587 53.8546 61.5587C52.0228 61.5587 50.943 62.9699 50.519 64.1424C50.2838 64.7054 50.2374 65.3647 50.2374 65.9278V76.8718C50.2374 76.8718 50.4869 79.32 48.6516 79.32C47.7393 79.32 46.5811 79.32 45.5726 79.32C43.2883 79.32 43.2883 76.8718 43.2883 76.8718V63.672C43.2883 61.4304 43.16 57.7278 43.16 57.7278C43.16 57.7278 43.1137 56.3415 44.6104 56.3415C45.5227 56.3415 46.6773 56.3415 47.5932 56.3415C49.0044 56.3415 49.2574 57.7207 49.2574 57.7207L49.4392 59.4455H49.5782C50.5653 57.9879 52.5859 55.8284 56.5273 55.8284C59.4851 55.8284 61.8336 57.3322 62.8207 59.727H62.9134C63.758 58.551 64.7914 57.6137 65.8712 56.9545C67.1399 56.2025 68.5475 55.8284 70.2403 55.8284C74.6557 55.8284 77.9877 58.9287 77.9877 65.7888C77.9877 65.7888 77.9877 72.9767 77.9877 76.8753C77.9984 79.1597 76.3555 79.3236 76.3555 79.3236ZM32.0699 51.584C29.8176 51.584 28.3138 49.9875 28.3601 48.0132C28.3138 45.9463 29.8141 44.3961 32.1162 44.3961C34.4183 44.3961 35.8723 45.9463 35.9186 48.0132C35.9222 49.9875 34.4183 51.584 32.0699 51.584ZM29.9994 56.3451C31.6529 56.3451 34.1332 56.3451 34.1332 56.3451C34.1332 56.3451 35.687 56.3593 35.687 58.1091C35.687 62.2928 35.687 77.2567 35.687 77.2567C35.687 77.2567 35.7832 79.32 34.037 79.32C32.2979 79.32 29.8034 79.32 29.8034 79.32C29.8034 79.32 28.5454 79.2452 28.5454 77.5453C28.5454 72.3994 28.5454 57.8204 28.5454 57.8204C28.5454 57.8204 28.5525 56.3451 29.9994 56.3451Z"
          fill="#339FF8"
        />
      </svg>
    );
  }

  if (name === "shareit") {
    return (
      <svg
        className={className}
        viewBox="0 0 124 124"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M95.925 124H28.075C12.5699 124 0 111.43 0 95.925V28.075C0 12.5699 12.5699 0 28.075 0H95.925C111.43 0 124 12.5699 124 28.075V95.925C124 111.43 111.43 124 95.925 124Z"
          fill="#329FFF"
        />
        <path
          d="M62.3906 43.6683C70.0645 43.6683 76.2854 37.4474 76.2854 29.7735C76.2854 22.0996 70.0645 15.8787 62.3906 15.8787C54.7168 15.8787 48.4958 22.0996 48.4958 29.7735C48.4958 37.4474 54.7168 43.6683 62.3906 43.6683Z"
          fill="white"
        />
        <path
          d="M106.56 84.7286C107.774 77.1514 102.616 70.0247 95.0386 68.8107C87.4614 67.5967 80.3348 72.7551 79.1208 80.3323C77.9068 87.9095 83.0651 95.0361 90.6423 96.2502C98.2195 97.4642 105.346 92.3058 106.56 84.7286Z"
          fill="white"
        />
        <path
          d="M45.9099 85.664C47.6427 78.1881 42.9869 70.723 35.511 68.9902C28.0351 67.2575 20.57 71.9132 18.8373 79.3891C17.1045 86.865 21.7602 94.3301 29.2361 96.0629C36.712 97.7956 44.1771 93.1399 45.9099 85.664Z"
          fill="white"
        />
        <path
          d="M78.9249 27.5382C79.0235 28.2686 79.0778 29.016 79.0778 29.7736C79.0778 32.291 78.5173 34.6759 77.5185 36.8161C77.3214 37.2849 77.0904 37.7334 76.8254 38.1411C76.7575 38.2566 76.6929 38.3721 76.625 38.4842C77.1889 38.7526 97.1682 48.3974 101.88 68.6349C101.88 68.6349 106.837 40.7977 78.9249 27.5382Z"
          fill="white"
        />
        <path
          d="M32.3588 65.7675C32.5321 65.7675 32.7019 65.7743 32.8718 65.7811C32.5049 61.9728 31.5061 43.1248 45.6489 27.8915C45.6489 27.8915 20.9236 35.4164 22.3097 68.9949C25.1396 66.9668 28.6082 65.7675 32.3588 65.7675Z"
          fill="white"
        />
        <path
          d="M80.1109 93.6352C79.085 92.5447 78.1439 91.2639 77.6309 89.9016C77.5596 89.7521 77.4916 89.5992 77.4237 89.4498C76.6899 89.9967 59.5881 102.519 40.4343 97.063C40.4343 97.063 60.7975 112.667 84.3405 97.1446C84.3405 97.1446 84.2726 97.1004 84.1741 97.0393C82.6487 96.122 81.2796 94.9703 80.1109 93.6352Z"
          fill="white"
        />
      </svg>
    );
  }

  if (name === "menu") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z"
        />
      </svg>
    );
  }

  if (name === "brands") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M4 6V20H18V22H3C2.4 22 2 21.6 2 21V6H4M12.1 10.6L10.2 8.8L12.8 8.4L14 6L15.2 8.4L17.8 8.8L15.9 10.7L16.3 13.3L14 12L11.6 13.2L12.1 10.6M8 2H20C21.11 2 22 2.9 22 4V16C22 17.11 21.11 18 20 18H8C6.9 18 6 17.11 6 16V4C6 2.9 6.9 2 8 2M8 4V16H20V4H8Z"
        />
      </svg>
    );
  }

  if (name === "campaign") {
    return (
      <svg
        className={className}
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9 4.09091C10.302 4.09091 11.5506 4.60812 12.4713 5.52875C13.3919 6.44938 13.9091 7.69803 13.9091 9C13.9091 10.8164 12.9191 12.4036 11.4545 13.2545V14.7273C11.4545 14.9443 11.3683 15.1524 11.2149 15.3058C11.0615 15.4593 10.8534 15.5455 10.6364 15.5455H7.36364C7.14664 15.5455 6.93853 15.4593 6.78509 15.3058C6.63166 15.1524 6.54545 14.9443 6.54545 14.7273V13.2545C5.08091 12.4036 4.09091 10.8164 4.09091 9C4.09091 7.69803 4.60812 6.44938 5.52875 5.52875C6.44938 4.60812 7.69803 4.09091 9 4.09091ZM10.6364 16.3636V17.1818C10.6364 17.3988 10.5502 17.6069 10.3967 17.7604C10.2433 17.9138 10.0352 18 9.81818 18H8.18182C7.96482 18 7.75672 17.9138 7.60328 17.7604C7.44984 17.6069 7.36364 17.3988 7.36364 17.1818V16.3636H10.6364ZM15.5455 8.18182H18V9.81818H15.5455V8.18182ZM0 8.18182H2.45455V9.81818H0V8.18182ZM9.81818 0V2.45455H8.18182V0H9.81818ZM3.20727 2.04545L4.95 3.79636L3.78818 4.95L2.04545 3.21545L3.20727 2.04545ZM13.05 3.78818L14.7845 2.04545L15.9545 3.21545L14.2118 4.95L13.05 3.78818Z"
          fill="white"
          fillOpacity="0.7"
        />
      </svg>
    );
  }

  if (name === "subject") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M4,5H20V7H4V5M4,9H20V11H4V9M4,13H20V15H4V13M4,17H14V19H4V17Z"
        />
      </svg>
    );
  }

  if (name === "company") {
    return (
      <svg
        className={className}
        viewBox="0 0 46 46"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M26.8333 17.4219L43.7192 20.776C45.08 21.0635 46 22.2135 46 23.5744V43.1244C46 44.7152 44.7158 45.9994 43.125 45.9994H25.875C26.4117 45.9994 26.8333 45.5777 26.8333 45.041V44.0827H43.125C43.6425 44.0827 44.0833 43.661 44.0833 43.1244V23.5744C44.0833 23.1335 43.7767 22.731 43.3358 22.6352L26.8333 19.3769V17.4219Z"
          fill="white"
        />
        <path
          d="M37.3752 26.833C37.9118 26.833 38.3335 27.2547 38.3335 27.7913C38.3335 28.328 37.9118 28.7497 37.3752 28.7497H33.5418C33.0052 28.7497 32.5835 28.328 32.5835 27.7913C32.5835 27.2547 33.0052 26.833 33.5418 26.833H37.3752Z"
          fill="white"
        />
        <path
          d="M37.3752 32.583C37.9118 32.583 38.3335 33.0047 38.3335 33.5413C38.3335 34.078 37.9118 34.4997 37.3752 34.4997H33.5418C33.0052 34.4997 32.5835 34.078 32.5835 33.5413C32.5835 33.0047 33.0052 32.583 33.5418 32.583H37.3752Z"
          fill="white"
        />
        <path
          d="M37.3752 38.333C37.9118 38.333 38.3335 38.7547 38.3335 39.2913C38.3335 39.828 37.9118 40.2497 37.3752 40.2497H33.5418C33.0052 40.2497 32.5835 39.828 32.5835 39.2913C32.5835 38.7547 33.0052 38.333 33.5418 38.333H37.3752Z"
          fill="white"
        />
        <path
          d="M26.8332 45.0414C26.8332 45.578 26.4115 45.9997 25.8748 45.9997C25.3382 45.9997 24.9165 45.578 24.9165 45.0414V44.083V18.208C24.9165 17.9205 25.0507 17.6522 25.2615 17.4605C25.4915 17.288 25.779 17.2114 26.0665 17.2689L26.8332 17.4222V19.3772V44.083V45.0414Z"
          fill="white"
        />
        <path
          d="M24.9168 44.083V45.0414C24.9168 45.578 25.3385 45.9997 25.8752 45.9997H18.2085C18.7452 45.9997 19.1668 45.578 19.1668 45.0414V44.083H24.9168Z"
          fill="white"
        />
        <path
          d="M20.1252 9.58301C20.6618 9.58301 21.0835 10.0047 21.0835 10.5413C21.0835 11.078 20.6618 11.4997 20.1252 11.4997H16.2918C15.7552 11.4997 15.3335 11.078 15.3335 10.5413C15.3335 10.0047 15.7552 9.58301 16.2918 9.58301H20.1252Z"
          fill="white"
        />
        <path
          d="M21.0835 16.2913C21.0835 16.828 20.6618 17.2497 20.1252 17.2497H16.2918C15.7552 17.2497 15.3335 16.828 15.3335 16.2913C15.3335 15.7547 15.7552 15.333 16.2918 15.333H20.1252C20.6618 15.333 21.0835 15.7547 21.0835 16.2913Z"
          fill="white"
        />
        <path
          d="M20.1252 21.083C20.6618 21.083 21.0835 21.5047 21.0835 22.0413C21.0835 22.578 20.6618 22.9997 20.1252 22.9997H16.2918C15.7552 22.9997 15.3335 22.578 15.3335 22.0413C15.3335 21.5047 15.7552 21.083 16.2918 21.083H20.1252Z"
          fill="white"
        />
        <path
          d="M20.1252 26.833C20.6618 26.833 21.0835 27.2547 21.0835 27.7913C21.0835 28.328 20.6618 28.7497 20.1252 28.7497H16.2918C15.7552 28.7497 15.3335 28.328 15.3335 27.7913C15.3335 27.2547 15.7552 26.833 16.2918 26.833H20.1252Z"
          fill="white"
        />
        <path
          d="M11.5 27.7913C11.5 28.328 11.0783 28.7497 10.5417 28.7497H6.70833C6.17167 28.7497 5.75 28.328 5.75 27.7913C5.75 27.2547 6.17167 26.833 6.70833 26.833H10.5417C11.0783 26.833 11.5 27.2547 11.5 27.7913Z"
          fill="white"
        />
        <path
          d="M10.5417 9.58301C11.0783 9.58301 11.5 10.0047 11.5 10.5413C11.5 11.078 11.0783 11.4997 10.5417 11.4997H6.70833C6.17167 11.4997 5.75 11.078 5.75 10.5413C5.75 10.0047 6.17167 9.58301 6.70833 9.58301H10.5417Z"
          fill="white"
        />
        <path
          d="M10.5417 15.333C11.0783 15.333 11.5 15.7547 11.5 16.2913C11.5 16.828 11.0783 17.2497 10.5417 17.2497H6.70833C6.17167 17.2497 5.75 16.828 5.75 16.2913C5.75 15.7547 6.17167 15.333 6.70833 15.333H10.5417Z"
          fill="white"
        />
        <path
          d="M10.5417 21.083C11.0783 21.083 11.5 21.5047 11.5 22.0413C11.5 22.578 11.0783 22.9997 10.5417 22.9997H6.70833C6.17167 22.9997 5.75 22.578 5.75 22.0413C5.75 21.5047 6.17167 21.083 6.70833 21.083H10.5417Z"
          fill="white"
        />
        <path
          d="M17.2498 35.458C17.2498 34.9213 16.809 34.4997 16.2915 34.4997H10.5415C10.0048 34.4997 9.58317 34.9213 9.58317 35.458V44.083H7.6665V35.458C7.6665 33.8672 8.95067 32.583 10.5415 32.583H16.2915C17.8823 32.583 19.1665 33.8672 19.1665 35.458V44.083H17.2498V35.458Z"
          fill="white"
        />
        <path
          d="M9.58317 44.083H17.2498H19.1665V45.0414C19.1665 45.578 18.7448 45.9997 18.2082 45.9997H8.62484C8.08817 45.9997 7.6665 45.578 7.6665 45.0414V44.083H9.58317Z"
          fill="white"
        />
        <path
          d="M3.35417 0.383019L24.4183 3.58385C25.8175 3.81385 26.8333 5.00218 26.8333 6.42052V17.4222L26.0667 17.2689C25.7792 17.2113 25.4917 17.288 25.2617 17.4605C25.0508 17.6522 24.9167 17.9205 24.9167 18.208V6.42052C24.9167 5.94135 24.5717 5.53885 24.1117 5.46218L3.0475 2.28052C2.99 2.26135 2.9325 2.26135 2.875 2.26135C2.645 2.26135 2.43417 2.33802 2.26167 2.49135C2.03167 2.68302 1.91667 2.93218 1.91667 3.21969V43.1247C1.91667 43.6614 2.3575 44.083 2.875 44.083H7.66667V45.0413C7.66667 45.578 8.08833 45.9997 8.625 45.9997H2.875C1.28417 45.9997 0 44.7155 0 43.1247V3.21969C0 2.37635 0.364167 1.57135 1.01583 1.03469C1.6675 0.478852 2.51083 0.248852 3.35417 0.383019Z"
          fill="white"
        />
      </svg>
    );
  }

  if (name === "analytics") {
    return (
      <svg
        className={className}
        viewBox="0 0 56 56"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M54.8332 53.667H1.16766C0.52367 53.667 0.000976562 54.1896 0.000976562 54.8336C0.000976562 55.4776 0.52367 56.0002 1.16766 56.0002H54.8333C55.4773 56.0002 56 55.4776 56 54.8336C55.9999 54.1896 55.4772 53.667 54.8332 53.667Z"
          fill="white"
        />
        <path
          d="M10.5008 39.667H3.50091C2.85692 39.667 2.33423 40.1897 2.33423 40.8337V54.8334C2.33423 55.4773 2.85692 56 3.50091 56H10.5008C11.1448 56 11.6675 55.4773 11.6675 54.8333V40.8336C11.6675 40.1896 11.1448 39.667 10.5008 39.667ZM9.33409 53.6667H4.66759V42.0002H9.3342V53.6667H9.33409Z"
          fill="white"
        />
        <path
          d="M24.5005 28H17.5007C16.8567 28 16.334 28.5227 16.334 29.1667V54.8329C16.334 55.4769 16.8567 55.9996 17.5007 55.9996H24.5005C25.1445 55.9996 25.6672 55.4769 25.6672 54.8329V29.1667C25.6672 28.5227 25.1445 28 24.5005 28ZM23.3338 53.6662H18.6672V30.3333H23.3338V53.6662Z"
          fill="white"
        />
        <path
          d="M38.5003 32.667H31.5004C30.8564 32.667 30.3337 33.1897 30.3337 33.8337V54.8333C30.3337 55.4773 30.8564 55.9999 31.5004 55.9999H38.5003C39.1443 55.9999 39.667 55.4773 39.667 54.8333V33.8337C39.667 33.1896 39.1443 32.667 38.5003 32.667ZM37.3336 53.6666H32.667V35.0002H37.3336V53.6666Z"
          fill="white"
        />
        <path
          d="M52.5 18.667H45.5002C44.8562 18.667 44.3335 19.1897 44.3335 19.8337V54.833C44.3335 55.477 44.8562 55.9997 45.5002 55.9997H52.5C53.144 55.9997 53.6667 55.477 53.6667 54.833V19.8337C53.6667 19.1896 53.144 18.667 52.5 18.667ZM51.3334 53.6663H46.6667V21.0002H51.3334V53.6663Z"
          fill="white"
        />
        <path
          d="M7.00084 21.001C4.42719 21.001 2.33423 23.0939 2.33423 25.6676C2.33423 28.2412 4.42719 30.3342 7.00084 30.3342C9.57449 30.3342 11.6675 28.2412 11.6675 25.6676C11.6675 23.0939 9.57449 21.001 7.00084 21.001ZM7.00084 28.0008C5.71287 28.0008 4.66759 26.9532 4.66759 25.6676C4.66759 24.3819 5.71287 23.3343 7.00084 23.3343C8.28882 23.3343 9.33409 24.382 9.33409 25.6676C9.33409 26.9532 8.28882 28.0008 7.00084 28.0008Z"
          fill="white"
        />
        <path
          d="M21.0006 9.33398C18.4269 9.33398 16.334 11.4269 16.334 14.0006C16.334 16.5743 18.4269 18.6672 21.0006 18.6672C23.5743 18.6672 25.6672 16.5743 25.6672 14.0006C25.6672 11.4269 23.5743 9.33398 21.0006 9.33398ZM21.0006 16.3338C19.7126 16.3338 18.6673 15.2862 18.6673 14.0006C18.6673 12.7149 19.7126 11.6673 21.0006 11.6673C22.2886 11.6673 23.3338 12.715 23.3338 14.0006C23.3338 15.2863 22.2886 16.3338 21.0006 16.3338Z"
          fill="white"
        />
        <path
          d="M35.0004 14.001C32.4267 14.001 30.3337 16.0939 30.3337 18.6676C30.3337 21.2412 32.4267 23.3342 35.0004 23.3342C37.574 23.3342 39.667 21.2412 39.667 18.6676C39.667 16.0939 37.574 14.001 35.0004 14.001ZM35.0004 21.0008C33.7124 21.0008 32.6671 19.9532 32.6671 18.6676C32.6671 17.3819 33.7124 16.3343 35.0004 16.3343C36.2883 16.3343 37.3336 17.382 37.3336 18.6676C37.3336 19.9532 36.2883 21.0008 35.0004 21.0008Z"
          fill="white"
        />
        <path
          d="M49.0001 0.000976562C46.4265 0.000976562 44.3335 2.09394 44.3335 4.66759C44.3335 7.24124 46.4265 9.3342 49.0001 9.3342C51.5738 9.3342 53.6667 7.24124 53.6667 4.66759C53.6667 2.09394 51.5738 0.000976562 49.0001 0.000976562ZM49.0001 7.00084C47.7121 7.00084 46.6669 5.95316 46.6669 4.66759C46.6669 3.38191 47.7121 2.33434 49.0001 2.33434C50.2881 2.33434 51.3334 3.38202 51.3334 4.66759C51.3334 5.95316 50.2881 7.00084 49.0001 7.00084Z"
          fill="white"
        />
        <path
          d="M47.3526 6.31488C46.8976 5.85989 46.1579 5.85989 45.703 6.31488L36.6498 15.368C36.1948 15.823 36.1948 16.5627 36.6498 17.0177C36.8785 17.244 37.1771 17.3584 37.4758 17.3584C37.7721 17.3584 38.0708 17.2464 38.2995 17.0177L47.3526 7.96455C47.8076 7.50956 47.8076 6.76987 47.3526 6.31488Z"
          fill="white"
        />
        <path
          d="M32.1795 15.9742L24.6849 13.837C24.0572 13.655 23.4202 14.019 23.2452 14.6397C23.0679 15.2604 23.4272 15.9043 24.0455 16.0816L31.5401 18.2189C31.6474 18.2492 31.7548 18.2632 31.8598 18.2632C32.3685 18.2632 32.835 17.9295 32.982 17.4162C33.1594 16.7979 32.8002 16.1515 32.1795 15.9742Z"
          fill="white"
        />
        <path
          d="M19.1806 15.4541C18.7793 14.9501 18.0466 14.8684 17.5426 15.2674L8.74609 22.2812C8.2421 22.6849 8.1581 23.4175 8.5618 23.9215C8.79279 24.2108 9.13349 24.3602 9.47407 24.3602C9.72837 24.3602 9.98506 24.2762 10.1998 24.1082L18.9963 17.0943C19.5003 16.6906 19.5843 15.9581 19.1806 15.4541Z"
          fill="white"
        />
      </svg>
    );
  }

  if (name === "stall") {
    return (
      <svg
        className={className}
        viewBox="0 0 44 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M43.9049 12.4415L39.5207 0.910032C39.3118 0.361343 38.7966 0 38.2234 0H5.76551C5.19133 0 4.67609 0.36182 4.46814 0.910986L0.0951557 12.4425C0.0324922 12.6079 0 12.7838 0 12.9616V15.5954C0 17.9728 1.66082 19.9845 3.93248 20.6356L5.30737 31.0564V44.1344C5.30737 44.174 5.31572 44.2107 5.31851 44.2493H5.30783V48H8.09288V45.5645H35.896V48H38.681V44.2493H38.6699C38.6731 44.2107 38.681 44.174 38.681 44.1344V31.0564L40.0555 20.639C42.3332 19.9921 44 17.9775 44 15.5959V12.9621C44 12.7838 43.968 12.6079 43.9049 12.4415ZM40.5688 11.532H35.4114L34.6617 2.86071H37.2723L40.5688 11.532ZM16.6021 11.532H11.3848L12.134 2.86071H16.8518L16.6021 11.532ZM19.6387 2.86024H24.3613L24.6114 11.5315H19.389L19.6387 2.86024ZM11.1379 14.3922H16.52L16.4865 15.554H16.4903C16.4898 15.5683 16.4861 15.5821 16.4861 15.5964C16.4861 16.9216 15.2616 17.9995 13.7572 17.9995C12.2672 17.9995 11.0538 16.9421 11.0306 15.6345L11.1379 14.3922ZM19.3064 14.3922H24.6941L24.7289 15.605C24.7233 16.9259 23.5011 17.999 22.0005 17.999C20.4984 17.999 19.2767 16.9259 19.2711 15.605L19.3064 14.3922ZM27.4805 14.3922H32.8626L32.9698 15.634C32.9462 16.9417 31.7333 17.999 30.2433 17.999C28.7389 17.999 27.5144 16.9212 27.5144 15.5959C27.5144 15.5816 27.5107 15.5678 27.5102 15.5535H27.5139L27.4805 14.3922ZM27.3979 11.532L27.1477 2.86071H31.8656L32.6152 11.532H27.3979ZM6.71799 2.86024H9.33826L8.58908 11.5315H3.42932L6.71799 2.86024ZM2.78551 15.5959V14.3922H8.34214L8.24884 15.4696L8.2558 15.4701C8.25209 15.512 8.24373 15.5525 8.24373 15.5959C8.24373 16.9212 7.01971 17.999 5.51578 17.999C4.01 17.999 2.78551 16.9212 2.78551 15.5959ZM35.896 42.7043H8.09288V31.0402H35.896V42.7043ZM7.73871 28.18L6.75466 20.7229C7.88725 20.4736 8.88847 19.892 9.63579 19.0892C10.6468 20.1747 12.1201 20.8592 13.7572 20.8592C15.3948 20.8592 16.8676 20.1742 17.8786 19.0887C18.8896 20.1742 20.3633 20.8592 22.0009 20.8592C23.6381 20.8592 25.1114 20.1742 26.1223 19.0887C27.1333 20.1742 28.6066 20.8592 30.2437 20.8592C31.8809 20.8592 33.3542 20.1742 34.3651 19.0887C35.1097 19.8886 36.1072 20.4683 37.2351 20.7195L36.2511 28.1795H7.73871V28.18ZM38.4861 17.999C36.9817 17.999 35.7572 16.9212 35.7572 15.5959C35.7572 15.553 35.7484 15.5125 35.7451 15.4701L35.7521 15.4696L35.6593 14.3922H41.2154V15.5959C41.215 16.9212 39.9905 17.999 38.4861 17.999Z"
          fill="white"
        />
      </svg>
    );
  }

  if (name === "messenger") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M15,4V11H5.17L4,12.17V4H15M16,2H3A1,1 0 0,0 2,3V17L6,13H16A1,1 0 0,0 17,12V3A1,1 0 0,0 16,2M21,6H19V15H6V17A1,1 0 0,0 7,18H18L22,22V7A1,1 0 0,0 21,6Z"
        />
      </svg>
    );
  }

  if (name === "cloud-upload") {
    return (
      <svg
        className={className}
        viewBox="0 0 24 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M16.0054 14L12.0054 10L8.00537 14"
          stroke="#2D3748"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.0054 10V19"
          stroke="#2D3748"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M20.3953 16.3895C21.3707 15.8578 22.1412 15.0164 22.5852 13.9981C23.0292 12.9799 23.1215 11.8427 22.8475 10.7662C22.5735 9.68964 21.9488 8.73501 21.072 8.05294C20.1952 7.37088 19.1162 7.00023 18.0053 6.9995H16.7453C16.4426 5.82874 15.8785 4.74183 15.0953 3.82049C14.3121 2.89915 13.3302 2.16735 12.2234 1.6801C11.1167 1.19286 9.91388 0.962854 8.70545 1.00738C7.49701 1.0519 6.3144 1.3698 5.24651 1.93716C4.17862 2.50453 3.25324 3.3066 2.53995 4.28308C1.82666 5.25956 1.34402 6.38503 1.12831 7.57489C0.912601 8.76475 0.969437 9.98803 1.29454 11.1528C1.61965 12.3175 2.20457 13.3934 3.00533 14.2995"
          stroke="#2D3748"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.0054 14L12.0054 10L8.00537 14"
          stroke="#2D3748"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }

  if (name === "visitors") {
    return (
      <svg
        className={className}
        viewBox="0 0 28 30"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.2 9.08889C18.2 11.5373 16.3196 13.5222 14 13.5222C11.6804 13.5222 9.79999 11.5373 9.79999 9.08889C9.79999 6.64043 11.6804 4.65556 14 4.65556C16.3196 4.65556 18.2 6.64043 18.2 9.08889Z"
          fill="currentColor"
        />
        <path
          d="M25.2 12.0444C25.2 13.6768 23.9464 15 22.4 15C20.8536 15 19.6 13.6768 19.6 12.0444C19.6 10.4121 20.8536 9.08889 22.4 9.08889C23.9464 9.08889 25.2 10.4121 25.2 12.0444Z"
          fill="currentColor"
        />
        <path
          d="M19.6 22.3889C19.6 19.1243 17.0927 16.4778 14 16.4778C10.9072 16.4778 8.39999 19.1243 8.39999 22.3889V26.8222H19.6V22.3889Z"
          fill="currentColor"
        />
        <path
          d="M8.39999 12.0444C8.39999 13.6768 7.14639 15 5.59999 15C4.05359 15 2.79999 13.6768 2.79999 12.0444C2.79999 10.4121 4.05359 9.08889 5.59999 9.08889C7.14639 9.08889 8.39999 10.4121 8.39999 12.0444Z"
          fill="currentColor"
        />
        <path
          d="M22.4 26.8222V22.3889C22.4 20.8312 22.0195 19.3671 21.351 18.0949C21.6863 18.0039 22.0378 17.9556 22.4 17.9556C24.7197 17.9556 26.6 19.9404 26.6 22.3889V26.8222H22.4Z"
          fill="currentColor"
        />
        <path
          d="M6.64896 18.0949C5.98058 19.3671 5.59999 20.8312 5.59999 22.3889V26.8222H1.39999V22.3889C1.39999 19.9404 3.2804 17.9556 5.59999 17.9556C5.96219 17.9556 6.31367 18.0039 6.64896 18.0949Z"
          fill="currentColor"
        />
      </svg>
    );
  }

  if (name === "products") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M22,12V20A2,2 0 0,1 20,22H4A2,2 0 0,1 2,20V12A1,1 0 0,1 1,11V8A2,2 0 0,1 3,6H6.17C6.06,5.69 6,5.35 6,5A3,3 0 0,1 9,2C10,2 10.88,2.5 11.43,3.24V3.23L12,4L12.57,3.23V3.24C13.12,2.5 14,2 15,2A3,3 0 0,1 18,5C18,5.35 17.94,5.69 17.83,6H21A2,2 0 0,1 23,8V11A1,1 0 0,1 22,12M4,20H11V12H4V20M20,20V12H13V20H20M9,4A1,1 0 0,0 8,5A1,1 0 0,0 9,6A1,1 0 0,0 10,5A1,1 0 0,0 9,4M15,4A1,1 0 0,0 14,5A1,1 0 0,0 15,6A1,1 0 0,0 16,5A1,1 0 0,0 15,4M3,8V10H11V8H3M13,8V10H21V8H13Z"
        />
      </svg>
    );
  }

  if (name === "clip-file") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M19,3H14.82C14.25,1.44 12.53,0.64 11,1.2C10.14,1.5 9.5,2.16 9.18,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M12,3A1,1 0 0,1 13,4A1,1 0 0,1 12,5A1,1 0 0,1 11,4A1,1 0 0,1 12,3M7,7H17V5H19V19H5V5H7V7M17,11H7V9H17V11M15,15H7V13H15V15Z"
        />
      </svg>
    );
  }

  if (name === "clip-user") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M19,3A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21H5A2,2 0 0,1 3,19V5A2,2 0 0,1 5,3H9.18C9.6,1.84 10.7,1 12,1C13.3,1 14.4,1.84 14.82,3H19M12,3A1,1 0 0,0 11,4A1,1 0 0,0 12,5A1,1 0 0,0 13,4A1,1 0 0,0 12,3M7,7V5H5V19H19V5H17V7H7M12,9A2,2 0 0,1 14,11A2,2 0 0,1 12,13A2,2 0 0,1 10,11A2,2 0 0,1 12,9M8,17V16C8,14.9 9.79,14 12,14C14.21,14 16,14.9 16,16V17H8Z"
        />
      </svg>
    );
  }

  if (name === "images") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M21,17H7V3H21M21,1H7A2,2 0 0,0 5,3V17A2,2 0 0,0 7,19H21A2,2 0 0,0 23,17V3A2,2 0 0,0 21,1M3,5H1V21A2,2 0 0,0 3,23H19V21H3M15.96,10.29L13.21,13.83L11.25,11.47L8.5,15H19.5L15.96,10.29Z"
        />
      </svg>
    );
  }

  if (name === "image") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M19,19H5V5H19M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M13.96,12.29L11.21,15.83L9.25,13.47L6.5,17H17.5L13.96,12.29Z"
        />
      </svg>
    );
  }

  if (name === "tags") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M6.5 10C7.3 10 8 9.3 8 8.5S7.3 7 6.5 7 5 7.7 5 8.5 5.7 10 6.5 10M9 6L16 13L11 18L4 11V6H9M9 4H4C2.9 4 2 4.9 2 6V11C2 11.6 2.2 12.1 2.6 12.4L9.6 19.4C9.9 19.8 10.4 20 11 20S12.1 19.8 12.4 19.4L17.4 14.4C17.8 14 18 13.5 18 13C18 12.4 17.8 11.9 17.4 11.6L10.4 4.6C10.1 4.2 9.6 4 9 4M13.5 5.7L14.5 4.7L21.4 11.6C21.8 12 22 12.5 22 13S21.8 14.1 21.4 14.4L16 19.8L15 18.8L20.7 13L13.5 5.7Z"
        />
      </svg>
    );
  }

  if (name == "stock-in") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M20 21H4V10H6V19H18V10H20V21M3 3H21V9H3V3M5 5V7H19V5M10.5 11V14H8L12 18L16 14H13.5V11"
        />
      </svg>
    );
  }

  if (name == "stock-out") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M20 21H4V10H6V19H18V10H20V21M3 3H21V9H3V3M5 5V7H19V5M10.5 17V14H8L12 10L16 14H13.5V17"
        />
      </svg>
    );
  }

  if (name == "info") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z"
        />
      </svg>
    );
  }

  if (name == "checked-rounded-outline") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20M16.59 7.58L10 14.17L7.41 11.59L6 13L10 17L18 9L16.59 7.58Z"
        />
      </svg>
    );
  }

  if (name == "cash") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M5,6H23V18H5V6M14,9A3,3 0 0,1 17,12A3,3 0 0,1 14,15A3,3 0 0,1 11,12A3,3 0 0,1 14,9M9,8A2,2 0 0,1 7,10V14A2,2 0 0,1 9,16H19A2,2 0 0,1 21,14V10A2,2 0 0,1 19,8H9M1,10H3V20H19V22H1V10Z"
        />
      </svg>
    );
  }

  if (name == "revenue") {
    return (
      <svg
        className={className}
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill="currentColor"
          d="m17 24c-3.394 0-7-1.052-7-3 0-.276.224-.5.5-.5s.5.224.5.5c0 .835 2.282 2 6 2s6-1.165 6-2c0-.276.224-.5.5-.5s.5.224.5.5c0 1.948-3.606 3-7 3z"
        />
        <path
          fill="currentColor"
          d="m17 21c-3.394 0-7-1.052-7-3 0-.276.224-.5.5-.5s.5.224.5.5c0 .835 2.282 2 6 2s6-1.165 6-2c0-.276.224-.5.5-.5s.5.224.5.5c0 1.948-3.606 3-7 3z"
        />
        <path
          fill="currentColor"
          d="m17 18c-3.394 0-7-1.052-7-3s3.606-3 7-3 7 1.052 7 3-3.606 3-7 3zm0-5c-3.718 0-6 1.165-6 2s2.282 2 6 2 6-1.165 6-2-2.282-2-6-2z"
        />
        <path
          fill="currentColor"
          d="m23.5 21.5c-.276 0-.5-.224-.5-.5v-6c0-.276.224-.5.5-.5s.5.224.5.5v6c0 .276-.224.5-.5.5z"
        />
        <path
          fill="currentColor"
          d="m10.5 21.5c-.276 0-.5-.224-.5-.5v-6c0-.276.224-.5.5-.5s.5.224.5.5v6c0 .276-.224.5-.5.5z"
        />
        <path
          fill="currentColor"
          d="m7 12c-3.394 0-7-1.052-7-3 0-.276.224-.5.5-.5s.5.224.5.5c0 .835 2.282 2 6 2s6-1.165 6-2c0-.276.224-.5.5-.5s.5.224.5.5c0 1.948-3.606 3-7 3z"
        />
        <path
          fill="currentColor"
          d="m7 9c-3.394 0-7-1.052-7-3 0-.276.224-.5.5-.5s.5.224.5.5c0 .835 2.282 2 6 2s6-1.165 6-2c0-.276.224-.5.5-.5s.5.224.5.5c0 1.948-3.606 3-7 3z"
        />
        <path
          fill="currentColor"
          d="m7 18c-3.394 0-7-1.052-7-3 0-.276.224-.5.5-.5s.5.224.5.5c0 .835 2.282 2 6 2 .544 0 1.018-.022 1.447-.067.271-.03.521.17.55.444s-.17.521-.444.55c-.466.05-.974.073-1.553.073z"
        />
        <path
          fill="currentColor"
          d="m7 21c-3.394 0-7-1.052-7-3 0-.276.224-.5.5-.5s.5.224.5.5c0 .835 2.282 2 6 2 .544 0 1.018-.022 1.447-.067.271-.035.521.17.55.444s-.17.521-.444.55c-.466.05-.974.073-1.553.073z"
        />
        <path
          fill="currentColor"
          d="m7 15c-3.394 0-7-1.052-7-3 0-.276.224-.5.5-.5s.5.224.5.5c0 .835 2.282 2 6 2 .544 0 1.018-.022 1.447-.067.271-.032.521.169.55.444.029.274-.17.521-.444.55-.466.05-.974.073-1.553.073z"
        />
        <path
          fill="currentColor"
          d="m7 6c-3.394 0-7-1.052-7-3s3.606-3 7-3 7 1.052 7 3-3.606 3-7 3zm0-5c-3.718 0-6 1.165-6 2s2.282 2 6 2 6-1.165 6-2-2.282-2-6-2z"
        />
        <path
          fill="currentColor"
          d="m.5 18.5c-.276 0-.5-.224-.5-.5v-15c0-.276.224-.5.5-.5s.5.224.5.5v15c0 .276-.224.5-.5.5z"
        />
        <path
          fill="currentColor"
          d="m13.5 9.5c-.276 0-.5-.224-.5-.5v-6c0-.276.224-.5.5-.5s.5.224.5.5v6c0 .276-.224.5-.5.5z"
        />
      </svg>
    );
  }

  if (name == "calendar") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M7,12H9V14H7V12M21,6V20A2,2 0 0,1 19,22H5C3.89,22 3,21.1 3,20V6A2,2 0 0,1 5,4H6V2H8V4H16V2H18V4H19A2,2 0 0,1 21,6M5,8H19V6H5V8M19,20V10H5V20H19M15,14V12H17V14H15M11,14V12H13V14H11M7,16H9V18H7V16M15,18V16H17V18H15M11,18V16H13V18H11Z"
        />
      </svg>
    );
  }

  if (name == "wand") {
    return (
      <svg className={className} viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M7.5,5.6L5,7L6.4,4.5L5,2L7.5,3.4L10,2L8.6,4.5L10,7L7.5,5.6M19.5,15.4L22,14L20.6,16.5L22,19L19.5,17.6L17,19L18.4,16.5L17,14L19.5,15.4M22,2L20.6,4.5L22,7L19.5,5.6L17,7L18.4,4.5L17,2L19.5,3.4L22,2M13.34,12.78L15.78,10.34L13.66,8.22L11.22,10.66L13.34,12.78M14.37,7.29L16.71,9.63C17.1,10 17.1,10.65 16.71,11.04L5.04,22.71C4.65,23.1 4,23.1 3.63,22.71L1.29,20.37C0.9,20 0.9,19.35 1.29,18.96L12.96,7.29C13.35,6.9 14,6.9 14.37,7.29Z"
        />
      </svg>
    );
  }
  if (name == "credit") {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
        />
      </svg>
    );
  }

  if (name == "copy") {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="w-5 h-5"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
        />
      </svg>
    );
  }

  if (name == "edit") {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="w-6 h-6"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
        />
      </svg>
    );
  }

  return null;
};
