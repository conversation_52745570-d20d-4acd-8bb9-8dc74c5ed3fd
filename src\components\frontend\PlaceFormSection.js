import React from "react";
import CustomDeadlineSelector from "./CustomDeadlineSelector";
import FileInput from "./FileInput";

const PlaceFormSection = () => {
  return (
    <>
      <section className="py-10 overflow-hidden">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center pb-10">
            <h3 className="archivo text-[32px] font-bold text-black">
              Place
              <span className="ml-1 text-transparent bg-clip-text bg-linear-to-r from-[#FD2692]   to-[#0A67F2]">
                Request
              </span>
            </h3>
            <p className="text-[#C4C4C4] archivo font-normal text-base max-w-xl mx-auto">
              You can also track and trace your Current Request
            </p>
          </div>
          <div className="">
            <div className="flex">
              <button
                type="button"
                className="text-white flex items-center justify-center space-x-4 bg-[#0A67F2] archivo w-full text-lg font-light px-5 py-2.5 mb-2"
              >
                <svg
                  width={51}
                  height={57}
                  viewBox="0 0 51 57"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M49.9838 42.2357C49.0642 39.0502 46.8016 36.4238 43.7872 35.043V14.068C43.7874 13.7324 43.6101 13.4217 43.3212 13.2509L22.4412 0.912649C22.1431 0.736474 21.7729 0.736474 21.475 0.912649L0.594912 13.2509C0.574981 13.2623 0.560744 13.2793 0.541762 13.2926C0.52278 13.3059 0.504748 13.3211 0.485766 13.3363C0.403313 13.4012 0.332487 13.4795 0.276016 13.5679C0.276016 13.5745 0.264627 13.5783 0.26083 13.585V13.5916C0.207325 13.6881 0.171022 13.793 0.153583 13.902C0.153583 13.9304 0.145041 13.957 0.142194 13.9855C0.139346 14.0139 0.128906 14.0396 0.128906 14.068V38.7445C0.128788 39.0801 0.306031 39.3908 0.594912 39.5617L21.475 51.8999C21.5 51.912 21.5256 51.9228 21.5519 51.9322C21.5777 51.9451 21.6044 51.9565 21.6316 51.9663C21.8405 52.0536 22.0756 52.0536 22.2846 51.9663C22.3121 51.9565 22.339 51.9451 22.3652 51.9322C22.3899 51.9208 22.4165 51.9141 22.4412 51.8999L28.1082 48.5505C29.852 54.5913 36.1626 58.0747 42.2034 56.3309C48.2442 54.5871 51.7276 48.2765 49.9838 42.2357ZM21.9581 2.83172L26.8934 5.74828L19.5758 10.0724C19.1243 10.3392 18.9745 10.9216 19.2413 11.3731C19.5081 11.8246 20.0905 11.9745 20.542 11.7076L28.7602 6.85493L33.6955 9.77149L14.6814 21.004L9.74608 18.0875L17.6966 13.3894C18.1482 13.1226 18.298 12.5402 18.0312 12.0887C17.7644 11.6372 17.182 11.4873 16.7305 11.7542L7.88016 16.9846L2.94392 14.068L21.9581 2.83172ZM13.7323 22.6488V28.9859L8.82925 26.0883V19.7512L13.7323 22.6488ZM28.0277 42.4543C27.7752 43.4117 27.6491 44.398 27.6526 45.3882C27.6529 45.7854 27.6738 46.1823 27.7153 46.5774L22.9072 49.4199V46.3372C22.9072 45.8131 22.4822 45.3882 21.9581 45.3882C21.4339 45.3882 21.009 45.8131 21.009 46.3372V49.4199L2.02709 38.2035V15.7318L6.93106 18.6294V26.6293C6.93071 26.9653 7.10783 27.2765 7.39707 27.4474L14.1983 31.4668C14.3447 31.553 14.5115 31.5985 14.6814 31.5988C15.2055 31.5988 15.6305 31.1738 15.6305 30.6497V23.7697L21.009 26.9482V42.5409C21.009 43.065 21.4339 43.49 21.9581 43.49C22.4822 43.49 22.9072 43.065 22.9072 42.5409V26.9482L40.1086 16.7834C40.5598 16.5166 40.7094 15.9344 40.4426 15.4831C40.1758 15.0318 39.5937 14.8822 39.1424 15.1491L21.9581 25.3044L16.5482 22.1069L35.5624 10.8705L41.8891 14.609V34.3787C35.8313 32.781 29.6253 36.3964 28.0277 42.4543ZM39.0418 54.8791C33.8 54.8791 29.5508 50.6299 29.5508 45.3882C29.5508 40.1464 33.8 35.8972 39.0418 35.8972C44.2835 35.8972 48.5327 40.1464 48.5327 45.3882C48.527 50.6275 44.2811 54.8733 39.0418 54.8791Z"
                    fill="white"
                  />
                </svg>

                <span className="">Place Request</span>
              </button>
              <button
                type="button"
                className="py-2.5 px-5 flex items-center justify-center space-x-4 mb-2 archivo w-full text-lg font-light text-[#FFFFFF] bg-[#1B1A1AE5]"
              >
                <svg
                  width={44}
                  height={57}
                  viewBox="0 0 44 57"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M43.8996 19.366C43.8978 19.3389 43.8931 19.3119 43.8884 19.2839C43.8713 19.1768 43.8356 19.0735 43.783 18.9787V18.9721C43.783 18.9656 43.7718 18.9619 43.768 18.9553C43.713 18.8698 43.6442 18.7937 43.5646 18.7304C43.5454 18.7135 43.5256 18.6976 43.5048 18.6828C43.4871 18.6707 43.4731 18.6548 43.4544 18.6436L33.6106 12.828C33.6311 12.548 33.6432 12.268 33.6432 11.9805C33.6432 5.79497 28.6288 0.780518 22.4432 0.780518C16.2577 0.780518 11.2432 5.79497 11.2432 11.9805C11.2432 12.2605 11.2554 12.548 11.2759 12.828L1.43483 18.6436C1.15075 18.8116 0.976446 19.1171 0.976563 19.4472V43.7139C0.976446 44.0439 1.15075 44.3495 1.43483 44.5175L21.9682 56.6508C21.9928 56.6627 22.018 56.6733 22.0438 56.6825C22.0692 56.6952 22.0954 56.7064 22.1222 56.7161C22.3276 56.802 22.5588 56.802 22.7643 56.7161C22.7914 56.7064 22.8178 56.6952 22.8436 56.6825C22.8679 56.6713 22.894 56.6648 22.9183 56.6508L43.4516 44.5175C43.7357 44.3495 43.91 44.0439 43.9099 43.7139V19.4472C43.9099 19.4192 43.9015 19.3931 43.8996 19.366ZM41.1416 19.4472L36.2883 22.3153L31.3099 19.3744C32.208 17.9668 32.8761 16.4253 33.2895 14.8076L41.1416 19.4472ZM30.5287 27.8855L35.3503 25.036V31.2679L30.5287 34.1173V27.8855ZM29.6 26.268L26.8411 24.6375C28.0867 23.5022 29.2272 22.2568 30.2487 20.9163L34.4534 23.3999L29.6 26.268ZM22.4432 2.64718C27.5956 2.6529 31.771 6.82817 31.7766 11.9805C31.7766 18.7313 24.449 24.3677 22.4432 25.7845C20.4375 24.3677 13.1099 18.7313 13.1099 11.9805C13.1156 6.82817 17.2909 2.6529 22.4432 2.64718ZM42.0432 43.1819L23.3766 54.212V51.1805C23.3766 50.6651 22.9587 50.2472 22.4432 50.2472C21.9278 50.2472 21.5099 50.6651 21.5099 51.1805V54.212L2.84323 43.1819V19.9792L11.596 14.8076C13.3927 22.1744 21.5538 27.4543 21.942 27.7016C22.2478 27.8962 22.6386 27.8962 22.9444 27.7016C23.7778 27.1542 24.581 26.5622 25.3506 25.9283L27.7632 27.3525L22.4432 30.4969L5.5443 20.5103C5.1005 20.2479 4.52801 20.395 4.26563 20.8388C4.00325 21.2826 4.15036 21.8551 4.59416 22.1175L21.5099 32.1135V47.4472C21.5099 47.9626 21.9278 48.3805 22.4432 48.3805C22.9587 48.3805 23.3766 47.9626 23.3766 47.4472V32.1135L28.6658 28.9877V35.7535C28.6656 36.2689 29.0834 36.6869 29.5989 36.687C29.766 36.687 29.9302 36.6422 30.0742 36.5571L36.7624 32.6072C37.0469 32.4391 37.221 32.1331 37.2207 31.8027V23.9328L42.0432 21.0833V43.1819Z"
                    fill="white"
                  />
                </svg>

                <span className="">Alternative</span>
              </button>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 px-4 py-5">
              <div className="col-span-1 lg:col-span-2">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <input
                      type="text"
                      id="first_name"
                      className="border archivo font-normal border-[#EAEAEA] text-[#9B9B9B] text-sm rounded-lg placeholder:text-[#9B9B9B] outline-0 block w-full p-3 h-[50px]"
                      placeholder="Request Title *"
                      required
                    />
                  </div>
                  <div>
                    <input
                      type="text"
                      id="last_name"
                      className="border archivo font-normal border-[#EAEAEA] text-[#9B9B9B] text-sm rounded-lg outline-0 placeholder:text-[#9B9B9B] block w-full p-3 h-[50px]"
                      placeholder="Quantity (Unit)"
                      required
                    />
                  </div>
                  <div>
                    <select
                      id="countries"
                      className="border archivo font-normal border-[#EAEAEA] text-[#9B9B9B] text-sm rounded-lg outline-0 placeholder:text-[#9B9B9B] block w-full p-3 h-[50px]"
                    >
                      <option>Select Category</option>
                      <option value="US">United States</option>
                      <option value="CA">Canada</option>
                      <option value="FR">France</option>
                      <option value="DE">Germany</option>
                    </select>
                  </div>
                  <div>
                    <select
                      id="countries"
                      className="border archivo font-normal border-[#EAEAEA] text-[#9B9B9B] text-sm rounded-lg outline-0 placeholder:text-[#9B9B9B] block w-full p-3 h-[50px]"
                    >
                      <option>Select Sub Category</option>
                      <option value="US">United States</option>
                      <option value="CA">Canada</option>
                      <option value="FR">France</option>
                      <option value="DE">Germany</option>
                    </select>
                  </div>
                  <div className="col-span-1 lg:col-span-2">
                    <input
                      type="tel"
                      id="phone"
                      className="border archivo font-normal border-[#EAEAEA] text-[#9B9B9B] text-sm rounded-lg placeholder:text-[#9B9B9B] outline-0 block w-full p-3 h-[50px]"
                      placeholder="Detailed Descriptions "
                      required
                    />
                  </div>
                  <div className="col-span-1 lg:col-span-2">
                    <input
                      type="tel"
                      id="phone"
                      className="border archivo font-normal border-[#EAEAEA] text-[#9B9B9B] text-sm rounded-lg placeholder:text-[#9B9B9B] outline-0 block w-full p-3 h-[50px]"
                      placeholder="Detailed Descriptions "
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="col-span-1">
                <div className="mb-4">
                  <input
                    type="tel"
                    id="phone"
                    className="border archivo font-normal border-[#EAEAEA] text-[#9B9B9B] text-sm rounded-lg placeholder:text-[#9B9B9B] outline-0 block w-full p-3 h-[50px]"
                    placeholder="Suggest a category"
                    required
                  />
                </div>
                <div className="mb-4">
                  <CustomDeadlineSelector />
                </div>

                <div className="mb-4">
                  <input
                    type="tel"
                    id="phone"
                    className="border archivo font-normal border-[#EAEAEA] text-[#9B9B9B] text-sm rounded-lg placeholder:text-[#9B9B9B] outline-0 block w-full p-3 h-[50px]"
                    placeholder="Detailed Descriptions "
                    required
                  />
                </div>

                <div className="mb-4">
                  <FileInput />
                </div>
                <div className="">
                  <button
                    type="button"
                    className="text-white bg-[#1B1A1A] font-bold archivo rounded-lg text-sm px-5 h-[50px] me-2 mb-2 w-full"
                  >
                    Submit Request
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default PlaceFormSection;
