const BlogPostModel = require('../models/blogPostModel');
const BlogCategoryModel = require('../models/blogCategoryModel');

/**
 * BlogService - Business logic for blog operations
 */
class BlogService {
  /**
   * Generate unique slug from title
   * @param {string} title - Blog post title
   * @param {string} excludeId - ID to exclude from uniqueness check
   * @returns {Promise<string>} Unique slug
   */
  static async generateSlug(title, excludeId = null) {
    let baseSlug = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim('-'); // Remove leading/trailing hyphens

    let slug = baseSlug;
    let counter = 1;

    // Check for uniqueness
    while (await this.slugExists(slug, excludeId)) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  /**
   * Check if slug exists
   * @param {string} slug - Slug to check
   * @param {string} excludeId - ID to exclude from check
   * @returns {Promise<boolean>} Whether slug exists
   */
  static async slugExists(slug, excludeId = null) {
    const { prisma } = require('../config/dbConfig');
    const whereClause = { slug, is_deleted: false };
    if (excludeId) {
      whereClause.id = { not: excludeId };
    }

    const post = await prisma.blog_posts.findFirst({
      where: whereClause
    });

    return !!post;
  }

  /**
   * Calculate reading time based on content
   * @param {string} content - Blog post content
   * @returns {number} Reading time in minutes
   */
  static calculateReadingTime(content) {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  }

  /**
   * Parse tags from string
   * @param {string} tagsString - Comma-separated tags
   * @returns {string} JSON string of tags array
   */
  static parseTags(tagsString) {
    if (!tagsString) return null;
    
    const tags = tagsString
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
    
    return JSON.stringify(tags);
  }

  /**
   * Format tags for display
   * @param {string} tagsJson - JSON string of tags
   * @returns {Array} Array of tags
   */
  static formatTags(tagsJson) {
    if (!tagsJson) return [];
    
    try {
      return JSON.parse(tagsJson);
    } catch (error) {
      return [];
    }
  }

  /**
   * Validate blog post data
   * @param {Object} data - Blog post data
   * @param {boolean} isUpdate - Whether this is an update operation
   * @returns {Object} Validation result
   */
  static validateBlogPostData(data, isUpdate = false) {
    const errors = {};

    // Required fields for creation
    if (!isUpdate) {
      if (!data.title) errors.title = 'Title is required';
      if (!data.content) errors.content = 'Content is required';
      if (!data.author_id) errors.author_id = 'Author is required';
    }

    // Validate title length
    if (data.title && (data.title.length < 3 || data.title.length > 200)) {
      errors.title = 'Title must be between 3 and 200 characters';
    }

    // Validate content length
    if (data.content && data.content.length < 10) {
      errors.content = 'Content must be at least 10 characters';
    }

    // Validate excerpt length
    if (data.excerpt && data.excerpt.length > 500) {
      errors.excerpt = 'Excerpt must not exceed 500 characters';
    }

    // Validate meta fields
    if (data.meta_title && data.meta_title.length > 60) {
      errors.meta_title = 'Meta title must not exceed 60 characters';
    }

    if (data.meta_description && data.meta_description.length > 160) {
      errors.meta_description = 'Meta description must not exceed 160 characters';
    }

    // Validate status
    const validStatuses = ['draft', 'published', 'scheduled', 'archived'];
    if (data.status && !validStatuses.includes(data.status)) {
      errors.status = 'Invalid status';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Validate blog category data
   * @param {Object} data - Category data
   * @param {boolean} isUpdate - Whether this is an update operation
   * @returns {Object} Validation result
   */
  static validateBlogCategoryData(data, isUpdate = false) {
    const errors = {};

    // Required fields for creation
    if (!isUpdate) {
      if (!data.name) errors.name = 'Name is required';
      if (!data.slug) errors.slug = 'Slug is required';
    }

    // Validate name length
    if (data.name && (data.name.length < 2 || data.name.length > 100)) {
      errors.name = 'Name must be between 2 and 100 characters';
    }

    // Validate slug format
    if (data.slug) {
      const slugRegex = /^[a-z0-9-]+$/;
      if (!slugRegex.test(data.slug)) {
        errors.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
      }
      if (data.slug.length < 2 || data.slug.length > 100) {
        errors.slug = 'Slug must be between 2 and 100 characters';
      }
    }

    // Validate description length
    if (data.description && data.description.length > 500) {
      errors.description = 'Description must not exceed 500 characters';
    }

    // Validate color format (hex color)
    if (data.color) {
      const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
      if (!colorRegex.test(data.color)) {
        errors.color = 'Color must be a valid hex color code';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Process blog post data before saving
   * @param {Object} data - Raw blog post data
   * @param {boolean} isUpdate - Whether this is an update
   * @returns {Promise<Object>} Processed data
   */
  static async processBlogPostData(data, isUpdate = false) {
    const processedData = { ...data };

    // Generate slug if not provided or if title changed
    if (!isUpdate || (data.title && !data.slug)) {
      processedData.slug = await this.generateSlug(
        data.title, 
        isUpdate ? data.id : null
      );
    }

    // Calculate reading time
    if (data.content) {
      processedData.reading_time = this.calculateReadingTime(data.content);
    }

    // Process tags
    if (data.tags && typeof data.tags === 'string') {
      processedData.tags = this.parseTags(data.tags);
    }

    // Generate excerpt from content if not provided
    if (data.content && !data.excerpt) {
      processedData.excerpt = data.content
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .substring(0, 200) + '...';
    }

    // Set meta title from title if not provided
    if (data.title && !data.meta_title) {
      processedData.meta_title = data.title.substring(0, 60);
    }

    // Set meta description from excerpt if not provided
    if (processedData.excerpt && !data.meta_description) {
      processedData.meta_description = processedData.excerpt
        .substring(0, 160);
    }

    return processedData;
  }

  /**
   * Get blog statistics (Admin)
   * @returns {Promise<Object>} Blog statistics
   */
  static async getBlogStatistics() {
    const { prisma } = require('../config/dbConfig');

    const [
      totalPosts,
      publishedPosts,
      draftPosts,
      totalCategories,
      totalViews,
      recentPosts
    ] = await Promise.all([
      prisma.blog_posts.count({ where: { is_deleted: false } }),
      prisma.blog_posts.count({ 
        where: { is_deleted: false, status: 'published' } 
      }),
      prisma.blog_posts.count({ 
        where: { is_deleted: false, status: 'draft' } 
      }),
      prisma.blog_categories.count({ where: { is_active: true } }),
      prisma.blog_posts.aggregate({
        where: { is_deleted: false, status: 'published' },
        _sum: { view_count: true }
      }),
      prisma.blog_posts.findMany({
        where: { is_deleted: false },
        take: 5,
        orderBy: { created_at: 'desc' },
        select: {
          id: true,
          title: true,
          status: true,
          created_at: true,
          author: {
            select: {
              first_name: true,
              last_name: true
            }
          }
        }
      })
    ]);

    return {
      totalPosts,
      publishedPosts,
      draftPosts,
      totalCategories,
      totalViews: totalViews._sum.view_count || 0,
      recentPosts
    };
  }
}

module.exports = BlogService;
