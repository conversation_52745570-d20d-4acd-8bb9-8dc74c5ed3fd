"use client";
import Link from "next/link";
import React, { useState, useRef, useEffect } from "react";

const ImageSlicerSlider = () => {
  const images = [
    {
      image: "/assets/frontend_assets/1.png",
      title: "Connect with Nearby Top-rated Professional",
      text: "We can connect you to the right Service, first time and every time.",
    },
    {
      image: "/assets/frontend_assets/2.png",
      title: "Connect with Nearby Top-rated Professional",
      text: "We can connect you to the right Service, first time and every time.",
    },
    {
      image: "/assets/frontend_assets/3.png",
      title: "Connect with Nearby Top-rated Professional",
      text: "We can connect you to the right Service, first time and every time.",
    },
    {
      image: "/assets/frontend_assets/4.png",
      title: "Connect with Nearby Top-rated Professional",
      text: "We can connect you to the right Service, first time and every time.",
    },
  ];

  const [activeIndex, setActiveIndex] = useState(0);
  const [prevActiveIndex, setPrevActiveIndex] = useState(null);
  const contentRefs = useRef([]);
  const autoplayInterval = useRef(null);
  const AUTOPLAY_DELAY = 5000; // 5 seconds

  const handlePrev = () => {
    resetAutoplay();
    setPrevActiveIndex(activeIndex);
    setActiveIndex((prevIndex) =>
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    resetAutoplay();
    setPrevActiveIndex(activeIndex);
    setActiveIndex((prevIndex) => (prevIndex + 1) % images.length);
  };

  // Setup autoplay
  const startAutoplay = () => {
    autoplayInterval.current = setInterval(() => {
      handleNext();
    }, AUTOPLAY_DELAY);
  };

  // Reset autoplay timer
  const resetAutoplay = () => {
    if (autoplayInterval.current) {
      clearInterval(autoplayInterval.current);
    }
    startAutoplay();
  };

  // Initialize autoplay on component mount
  useEffect(() => {
    startAutoplay();
    return () => {
      if (autoplayInterval.current) {
        clearInterval(autoplayInterval.current);
      }
    };
  }, []);

  // Reset all content animations when slide changes
  const resetContentAnimations = (index) => {
    if (contentRefs.current[index]) {
      const contentElements = contentRefs.current[index].children;
      Array.from(contentElements).forEach((el) => {
        el.style.opacity = "0";
        el.style.transform = "translateX(-50px)";
        el.style.transition = "none";
      });
    }
  };

  // Animate content in with fadeInLeft effect
  const animateContentIn = (index) => {
    if (contentRefs.current[index]) {
      const contentElements = contentRefs.current[index].children;
      Array.from(contentElements).forEach((el, i) => {
        setTimeout(() => {
          el.style.transition = `
            opacity 0.8s cubic-bezier(0.215, 0.610, 0.355, 1) ${i * 200}ms,
            transform 0.8s cubic-bezier(0.215, 0.610, 0.355, 1) ${i * 200}ms
          `;
          el.style.opacity = "1";
          el.style.transform = "translateX(0)";
        }, 50);
      });
    }
  };

  useEffect(() => {
    // Reset animation for previous slide
    if (prevActiveIndex !== null) {
      resetContentAnimations(prevActiveIndex);
    }

    // Reset and animate new slide content
    resetContentAnimations(activeIndex);
    setTimeout(() => {
      animateContentIn(activeIndex);
    }, 50);
  }, [activeIndex, prevActiveIndex]);

  // Initialize first slide animation
  useEffect(() => {
    resetContentAnimations(0);
    animateContentIn(0);
  }, []);

  return (
    <div className="h-[calc(100vh-70px)] w-full overflow-hidden relative">
      {/* Image Slices Container */}
      <div className="absolute inset-0">
        {images.map((item, index) => (
          <div
            key={index}
            className="absolute inset-0 transition-opacity duration-500"
            style={{
              opacity: index === activeIndex ? 1 : 0,
              zIndex: index === activeIndex ? 20 : 10,
              pointerEvents: index === activeIndex ? "auto" : "none",
            }}
          >
            <CanvasImageSlicer
              imageUrl={item.image}
              isActive={index === activeIndex}
              isPrevActive={index === prevActiveIndex}
            />

            {/* Content Overlay */}
            <div className="absolute inset-0 flex items-center justify-center z-30">
              <div
                className="max-w-7xl px-4 mx-auto w-full"
                ref={(el) => (contentRefs.current[index] = el)}
              >
                <h2 className="text-[43px] text-[#242B3A] font-bold mb-4 opacity-0 transform -translate-x-5 archivo max-w-3xl">
                  {item.title}
                </h2>
                <p className="text-xl font-normal text-[#656B76] mb-8 opacity-0 transform -translate-x-5">
                  {item.text}
                </p>
                <div className="opacity-0 transform -translate-x-5 flex items-center space-x-4">
                  <Link
                    href=""
                    className="relative p-0.5 overflow-hidden rounded-lg w-[200px] cursor-pointer group"
                  >
                    <div className="absolute inset-0 bg-[#0A67F2] group-hover:bg-gradient-to-r group-hover:from-[#FD2692] group-hover:to-[#0A67F2] rounded-lg"></div>
                    <div className="relative dm_sans px-5 py-2.5 bg-[#0A67F2] rounded-lg group-hover:bg-transparent transition-all duration-300 ease-linear text-center">
                      <p className=" archivo bg-clip-text text-center text-white group-hover:text-white  text-lg font-semibold transition-all duration-300 ease-in-out inline-flex items-center">
                        <span className="transition-all duration-300 ease-linear">
                          Post a request
                        </span>
                      </p>
                    </div>
                  </Link>

                  <Link
                    href=""
                    className="relative p-0.5 overflow-hidden rounded-lg w-[200px] cursor-pointer group"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-[#FD2692] to-[#0A67F2] rounded-lg"></div>
                    <div className="relative dm_sans px-5 py-2.5 bg-white rounded-lg group-hover:bg-transparent transition-all duration-300 ease-linear text-center">
                      <p className=" archivo bg-clip-text text-center text-[#0A67F2] group-hover:text-white text-lg font-semibold transition-all duration-300 ease-in-out inline-flex items-center">
                        <span className="transition-all duration-300 ease-linear">
                          Join as seller{" "}
                        </span>
                        <svg
                          width={21}
                          height={20}
                          className="ml-2 inline-block transition-all duration-300 ease-linear"
                          viewBox="0 0 21 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g clipPath="url(#clip0_829_18584)">
                            <path
                              d="M1.74911 9.99938C1.74911 10.248 1.86764 10.4865 2.07862 10.6623C2.2896 10.8381 2.57574 10.9369 2.87411 10.9369H16.6554L11.8254 14.9611C11.614 15.1372 11.4953 15.3761 11.4953 15.6252C11.4953 15.8742 11.614 16.1131 11.8254 16.2892C12.0367 16.4653 12.3234 16.5643 12.6222 16.5643C12.9211 16.5643 13.2078 16.4653 13.4191 16.2892L20.1691 10.6642C20.274 10.5771 20.3572 10.4736 20.414 10.3597C20.4708 10.2457 20.5 10.1235 20.5 10.0002C20.5 9.87677 20.4708 9.7546 20.414 9.64064C20.3572 9.52669 20.274 9.4232 20.1691 9.3361L13.4191 3.7111C13.3145 3.62389 13.1902 3.55472 13.0535 3.50752C12.9168 3.46033 12.7702 3.43604 12.6222 3.43604C12.4742 3.43604 12.3277 3.46033 12.191 3.50752C12.0542 3.55472 11.93 3.62389 11.8254 3.7111C11.7207 3.7983 11.6377 3.90183 11.5811 4.01577C11.5244 4.12971 11.4953 4.25183 11.4953 4.37516C11.4953 4.49849 11.5244 4.62061 11.5811 4.73455C11.6377 4.84849 11.7207 4.95202 11.8254 5.03922L16.6554 9.06188H2.87411C2.57574 9.06188 2.2896 9.16065 2.07862 9.33647C1.86764 9.51228 1.74911 9.75074 1.74911 9.99938Z"
                              fill="currentColor"
                            />
                          </g>
                          <defs>
                            <clipPath id="clip0_829_18584">
                              <rect
                                width={20}
                                height={20}
                                fill="white"
                                transform="matrix(-1 0 0 1 20.5 0)"
                              />
                            </clipPath>
                          </defs>
                        </svg>
                      </p>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Controls */}
      <div className="">
        <button
          onClick={handlePrev}
          className="py-2 bg-white text-[#0A67F2] rounded-full backdrop-blur-sm absolute left-4 top-1/2 -translate-y-1/2 z-40 w-12 h-12 border border-dotted border-[#0A67F2B8] hover:bg-[#0A67F2] hover:text-white transition-all duration-300 ease-linear cursor-pointer flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
            />
          </svg>
        </button>
        <button
          onClick={handleNext}
          className="py-2 bg-white text-[#0A67F2] rounded-full backdrop-blur-sm absolute right-4 top-1/2 -translate-y-1/2 z-40 w-12 h-12 border border-dotted border-[#0A67F2B8] hover:bg-[#0A67F2] hover:text-white transition-all duration-300 ease-linear cursor-pointer flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

const CanvasImageSlicer = ({ imageUrl, isActive, isPrevActive }) => {
  const containerRef = useRef(null);
  const canvasRefs = useRef([]);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const SLICE_COUNT = 5;

  // Set full viewport dimensions
  useEffect(() => {
    const updateDimensions = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateDimensions();
    window.addEventListener("resize", updateDimensions);
    return () => window.removeEventListener("resize", updateDimensions);
  }, []);

  // Draw image slices
  useEffect(() => {
    if (dimensions.width === 0 || dimensions.height === 0) return;

    const img = new Image();
    img.src = imageUrl;
    img.onload = () => {
      const sliceWidth = dimensions.width / SLICE_COUNT;

      canvasRefs.current.forEach((canvas, i) => {
        if (!canvas) return;

        canvas.width = sliceWidth;
        canvas.height = dimensions.height;
        const ctx = canvas.getContext("2d");
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Calculate source rectangle
        const sourceX = (i / SLICE_COUNT) * img.width;
        const sourceWidth = img.width / SLICE_COUNT;

        // Maintain aspect ratio while filling the screen
        const drawHeight = (sliceWidth * img.height) / sourceWidth;
        const offsetY = (dimensions.height - drawHeight) / 2;

        ctx.drawImage(
          img,
          sourceX,
          0,
          sourceWidth,
          img.height,
          0,
          offsetY,
          sliceWidth,
          drawHeight
        );
      });
    };
  }, [imageUrl, dimensions]);

  // Animation effect when slide changes
  useEffect(() => {
    if (!containerRef.current) return;

    const sliceElements = Array.from(containerRef.current.children);
    const ANIMATION_DURATION = 0.7;
    const STAGGER_DELAY = 0.3;

    sliceElements.forEach((sliceEl, i) => {
      // Reset styles
      sliceEl.style.transform = "";
      sliceEl.style.opacity = "";
      sliceEl.style.transition = "";

      if (isActive) {
        // Enter animation
        const direction = i % 2 === 0 ? "-100px" : "100px";
        sliceEl.style.transform = `translateY(${direction})`;
        sliceEl.style.opacity = "0";

        setTimeout(() => {
          sliceEl.style.transition = `transform ${ANIMATION_DURATION}s ease ${
            i * STAGGER_DELAY
          }s, opacity ${ANIMATION_DURATION}s ease ${i * STAGGER_DELAY}s`;
          sliceEl.style.transform = "translateY(0)";
          sliceEl.style.opacity = "1";
        }, 50);
      } else if (isPrevActive) {
        // Exit animation
        setTimeout(() => {
          sliceEl.style.transition = `transform ${ANIMATION_DURATION}s ease ${
            (SLICE_COUNT - i - 1) * STAGGER_DELAY
          }s, opacity ${ANIMATION_DURATION}s ease ${
            (SLICE_COUNT - i - 1) * STAGGER_DELAY
          }s`;
          const direction = i % 2 === 0 ? "100px" : "-100px";
          sliceEl.style.transform = `translateY(${direction})`;
          sliceEl.style.opacity = "0";
        }, 50);
      }
    });
  }, [isActive, isPrevActive]);

  return (
    <div ref={containerRef} className="flex absolute inset-0">
      {Array.from({ length: SLICE_COUNT }).map((_, i) => (
        <div
          key={i}
          className="relative h-full overflow-hidden"
          style={{ width: `${100 / SLICE_COUNT}%` }}
        >
          <canvas
            ref={(el) => (canvasRefs.current[i] = el)}
            className="block h-full w-full object-cover"
          />
        </div>
      ))}
    </div>
  );
};

export default ImageSlicerSlider;

// "use client";
// import Link from "next/link";
// import React, { useState, useRef, useEffect } from "react";

// // --- CanvasImageSlicer Component (Modified) ---
// const CanvasImageSlicer = ({
//   imageUrl,
//   isActive,
//   isPrevActive,
//   transitionDirection, // New prop
// }) => {
//   const containerRef = useRef(null);
//   const canvasRefs = useRef([]);
//   const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
//   const SLICE_COUNT = 5; // Keep the number of slices

//   // Set initial dimensions (no change needed here)
//   useEffect(() => {
//     const updateDimensions = () => {
//       // Use container dimensions if available, otherwise fallback to window
//       const containerWidth =
//         containerRef.current?.offsetWidth || window.innerWidth;
//       const containerHeight =
//         containerRef.current?.offsetHeight || window.innerHeight - 70; // Adjust based on your layout
//       setDimensions({
//         width: containerWidth,
//         height: containerHeight,
//       });
//     };

//     // Need a slight delay to ensure containerRef is set for initial dimensions
//     const timer = setTimeout(updateDimensions, 0);
//     window.addEventListener("resize", updateDimensions);

//     return () => {
//       clearTimeout(timer);
//       window.removeEventListener("resize", updateDimensions);
//     };
//   }, []);

//   // Draw image slices (no change needed here, but ensure container dimensions are used)
//   useEffect(() => {
//     if (dimensions.width === 0 || dimensions.height === 0) return;

//     const img = new Image();
//     img.src = imageUrl;
//     img.onload = () => {
//       const sliceWidth = dimensions.width / SLICE_COUNT;

//       canvasRefs.current.forEach((canvas, i) => {
//         if (!canvas) return;

//         canvas.width = sliceWidth;
//         canvas.height = dimensions.height;
//         const ctx = canvas.getContext("2d");
//         ctx.clearRect(0, 0, canvas.width, canvas.height);

//         const sourceX = (i / SLICE_COUNT) * img.width;
//         const sourceWidth = img.width / SLICE_COUNT;

//         // Calculate scaling to cover the canvas slice vertically
//         const scale = dimensions.height / img.height;
//         const scaledSourceWidth = sliceWidth / scale;
//         const scaledSourceX =
//           (i / SLICE_COUNT) *
//           (img.width -
//             ((SLICE_COUNT - 1) * (scaledSourceWidth - sourceWidth)) /
//               SLICE_COUNT); // Adjust source X for cover

//         // Draw image to cover the slice area
//         ctx.drawImage(
//           img,
//           sourceX, // Use original sourceX for slicing
//           0,
//           sourceWidth, // Use original sourceWidth
//           img.height,
//           0,
//           0, // Start drawing at top
//           sliceWidth,
//           dimensions.height // Fill height
//         );
//       });
//     };
//     img.onerror = () => {
//       console.error("Failed to load image:", imageUrl);
//     };
//   }, [imageUrl, dimensions]);

//   // *** Animation effect modified for horizontal movement ***
//   useEffect(() => {
//     if (!containerRef.current) return;

//     const sliceElements = Array.from(containerRef.current.children);
//     const ANIMATION_DURATION = 0.7; // Adjust as needed
//     const STAGGER_DELAY_BASE = 0.1; // Adjust stagger speed

//     sliceElements.forEach((sliceEl, i) => {
//       // Reset styles first
//       sliceEl.style.transform = "";
//       sliceEl.style.opacity = "0"; // Default to hidden unless active
//       sliceEl.style.transition = "none"; // Disable transitions initially

//       // Determine animation parameters based on direction
//       let enterX, exitX, staggerDelay;

//       if (transitionDirection === "next") {
//         enterX = "50%"; // Enter from right
//         exitX = "-50%"; // Exit to left
//         staggerDelay = i * STAGGER_DELAY_BASE; // Stagger from left to right
//       } else {
//         // transitionDirection === 'prev'
//         enterX = "-50%"; // Enter from left
//         exitX = "50%"; // Exit to right
//         // Reverse stagger for previous: rightmost slice moves first
//         staggerDelay = (SLICE_COUNT - 1 - i) * STAGGER_DELAY_BASE;
//       }

//       if (isActive) {
//         // --- Enter Animation ---
//         // Set initial state (off-screen)
//         sliceEl.style.transform = `translateX(${enterX})`;
//         sliceEl.style.opacity = "0";

//         // Apply animation after a tiny delay to ensure initial state is registered
//         setTimeout(() => {
//           sliceEl.style.transition = `transform ${ANIMATION_DURATION}s ease-out ${staggerDelay}s, opacity ${ANIMATION_DURATION}s ease-out ${staggerDelay}s`;
//           sliceEl.style.transform = "translateX(0)";
//           sliceEl.style.opacity = "1";
//         }, 20); // Small delay (20ms)
//       } else if (isPrevActive) {
//         // --- Exit Animation ---
//         // Initial state is already visible (translateX(0), opacity 1)
//         // Apply animation after a tiny delay
//         setTimeout(() => {
//           sliceEl.style.transition = `transform ${ANIMATION_DURATION}s ease-in ${staggerDelay}s, opacity ${ANIMATION_DURATION}s ease-in ${staggerDelay}s`;
//           sliceEl.style.transform = `translateX(${exitX})`;
//           sliceEl.style.opacity = "0";
//         }, 20); // Small delay (20ms)
//       }
//       // Slices that are neither active nor previously active remain hidden (opacity 0)
//     });
//   }, [isActive, isPrevActive, transitionDirection, dimensions]); // Add dimensions dependency

//   return (
//     // Ensure containerRef takes full space for dimension calculation
//     <div ref={containerRef} className="flex absolute inset-0 w-full h-full">
//       {Array.from({ length: SLICE_COUNT }).map((_, i) => (
//         <div
//           key={i}
//           className="relative h-full overflow-hidden" // Make sure overflow is hidden
//           style={{ width: `${100 / SLICE_COUNT}%` }}
//         >
//           <canvas
//             ref={(el) => (canvasRefs.current[i] = el)}
//             className="block h-full w-full object-cover" // object-cover might not apply directly to canvas, drawing handles it
//           />
//         </div>
//       ))}
//     </div>
//   );
// };

// // --- ImageSlicerSlider Component (Modified) ---
// const ImageSlicerSlider = () => {
//   const images = [
//     {
//       image: "/assets/frontend_assets/1.png",
//       title: "Connect with Nearby Top-rated Professional 1",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//     {
//       image: "/assets/frontend_assets/2.png",
//       title: "Connect with Nearby Top-rated Professional 2",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//     {
//       image: "/assets/frontend_assets/3.png",
//       title: "Connect with Nearby Top-rated Professional 3",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//     {
//       image: "/assets/frontend_assets/4.png",
//       title: "Connect with Nearby Top-rated Professional 4",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//   ];

//   const [activeIndex, setActiveIndex] = useState(0);
//   const [prevActiveIndex, setPrevActiveIndex] = useState(null);
//   const [transitionDirection, setTransitionDirection] = useState("next"); // Track direction
//   const contentRefs = useRef([]);

//   const handlePrev = () => {
//     setTransitionDirection("prev"); // Set direction before state change
//     setPrevActiveIndex(activeIndex);
//     setActiveIndex((prevIndex) =>
//       prevIndex === 0 ? images.length - 1 : prevIndex - 1
//     );
//   };

//   const handleNext = () => {
//     setTransitionDirection("next"); // Set direction before state change
//     setPrevActiveIndex(activeIndex);
//     setActiveIndex((prevIndex) => (prevIndex + 1) % images.length);
//   };

//   // Content animation logic (no change needed here, but ensure refs are correctly assigned)
//   const resetContentAnimations = (index) => {
//     if (contentRefs.current[index]) {
//       const contentElements = contentRefs.current[index].children;
//       Array.from(contentElements).forEach((el) => {
//         // Reset styles immediately without transition
//         el.style.transition = "none";
//         el.style.opacity = "0";
//         el.style.transform = "translateX(-20px)";
//       });
//     }
//   };

//   const animateContentIn = (index) => {
//     // Ensure previous animations are reset before starting new ones
//     if (prevActiveIndex !== null && prevActiveIndex !== index) {
//       resetContentAnimations(prevActiveIndex);
//     }

//     if (contentRefs.current[index]) {
//       const contentElements = contentRefs.current[index].children;
//       // Ensure initial state is set before animation starts
//       Array.from(contentElements).forEach((el) => {
//         el.style.transition = "none";
//         el.style.opacity = "0";
//         el.style.transform = "translateX(-20px)";
//       });

//       // Start animation with delay
//       Array.from(contentElements).forEach((el, i) => {
//         setTimeout(() => {
//           // Check if this is still the active slide before animating
//           if (contentRefs.current[index] === el.parentElement) {
//             el.style.transition =
//               "opacity 0.5s ease-out 0.3s, transform 0.5s ease-out 0.3s"; // Add slight delay if needed
//             el.style.opacity = "1";
//             el.style.transform = "translateX(0)";
//           }
//         }, i * 150); // Stagger content animation slightly later
//       });
//     }
//   };

//   useEffect(() => {
//     // Reset previous content immediately when index changes
//     if (prevActiveIndex !== null && prevActiveIndex !== activeIndex) {
//       resetContentAnimations(prevActiveIndex);
//     }
//     // Animate new slide content in
//     animateContentIn(activeIndex);
//   }, [activeIndex, prevActiveIndex]); // Dependency array is correct

//   return (
//     // Ensure the container has position relative if children use absolute positioning
//     <div className="h-[calc(100vh-70px)] w-full overflow-hidden relative">
//       {/* Image Slices Container */}
//       <div className="absolute inset-0">
//         {images.map((item, index) => (
//           <div
//             key={index}
//             className="absolute inset-0 transition-opacity duration-500" // Keep base opacity transition if needed, but animation handles visibility
//             style={{
//               // Opacity is now mainly controlled by the slice animations
//               opacity: 1, // Keep parent div visible
//               zIndex:
//                 index === activeIndex
//                   ? 20
//                   : index === prevActiveIndex
//                   ? 15
//                   : 10, // Ensure active/prev are layered correctly
//               pointerEvents: index === activeIndex ? "auto" : "none",
//             }}
//           >
//             <CanvasImageSlicer
//               imageUrl={item.image}
//               isActive={index === activeIndex}
//               isPrevActive={index === prevActiveIndex}
//               transitionDirection={transitionDirection} // Pass direction prop
//             />

//             {/* Content Overlay - No changes needed here except ref handling */}
//             <div
//               className={`absolute inset-0 flex items-center justify-center z-30 ${
//                 index === activeIndex ? "opacity-100" : "opacity-0"
//               } transition-opacity duration-300 ease-in-out`}
//               style={{ pointerEvents: index === activeIndex ? "auto" : "none" }} // Control pointer events for content
//             >
//               <div
//                 className="max-w-7xl px-4 mx-auto w-full"
//                 // Ensure refs are stable and correctly assigned
//                 ref={(el) => {
//                   if (el && !contentRefs.current.includes(el)) {
//                     contentRefs.current[index] = el;
//                   }
//                 }}
//               >
//                 {/* Content elements with initial styles for animation */}
//                 <h2 className="text-[43px] text-[#242B3A] font-bold mb-4 opacity-0 transform -translate-x-5 archivo max-w-3xl">
//                   {item.title}
//                 </h2>
//                 <p className="text-xl font-normal text-[#656B76] mb-8 opacity-0 transform -translate-x-5">
//                   {item.text}
//                 </p>
//                 <div className="opacity-0 transform -translate-x-5 flex items-center space-x-4">
//                   {/* Button 1 */}
//                   <Link
//                     href="#" // Use # or actual link
//                     className="relative p-0.5 overflow-hidden rounded-lg w-[200px] cursor-pointer group"
//                   >
//                     <div className="absolute inset-0 bg-[#0A67F2] group-hover:bg-gradient-to-r group-hover:from-[#FD2692] group-hover:to-[#0A67F2] rounded-lg transition-all duration-300 ease-linear"></div>
//                     <div className="relative dm_sans px-5 py-2.5 bg-[#0A67F2] rounded-lg group-hover:bg-transparent transition-all duration-300 ease-linear text-center">
//                       <p className="archivo text-center text-white text-lg font-semibold transition-all duration-300 ease-in-out inline-flex items-center">
//                         Post a request
//                       </p>
//                     </div>
//                   </Link>

//                   {/* Button 2 */}
//                   <Link
//                     href="#" // Use # or actual link
//                     className="relative p-0.5 overflow-hidden rounded-lg w-[200px] cursor-pointer group"
//                   >
//                     <div className="absolute inset-0 bg-gradient-to-r from-[#FD2692] to-[#0A67F2] rounded-lg"></div>
//                     <div className="relative dm_sans px-5 py-2.5 bg-white rounded-lg group-hover:bg-transparent transition-all duration-300 ease-linear text-center">
//                       <p className="archivo bg-clip-text text-center text-[#0A67F2] group-hover:text-white text-lg font-semibold transition-all duration-300 ease-in-out inline-flex items-center">
//                         <span className="transition-all duration-300 ease-linear">
//                           Join as seller{" "}
//                         </span>
//                         {/* SVG Arrow */}
//                         <svg
//                           width={21}
//                           height={20}
//                           className="ml-2 inline-block transition-all duration-300 ease-linear"
//                           viewBox="0 0 21 20"
//                           fill="none"
//                           xmlns="http://www.w3.org/2000/svg"
//                         >
//                           <g clipPath="url(#clip0_829_18584)">
//                             <path
//                               d="M1.74911 9.99938C1.74911 10.248 1.86764 10.4865 2.07862 10.6623C2.2896 10.8381 2.57574 10.9369 2.87411 10.9369H16.6554L11.8254 14.9611C11.614 15.1372 11.4953 15.3761 11.4953 15.6252C11.4953 15.8742 11.614 16.1131 11.8254 16.2892C12.0367 16.4653 12.3234 16.5643 12.6222 16.5643C12.9211 16.5643 13.2078 16.4653 13.4191 16.2892L20.1691 10.6642C20.274 10.5771 20.3572 10.4736 20.414 10.3597C20.4708 10.2457 20.5 10.1235 20.5 10.0002C20.5 9.87677 20.4708 9.7546 20.414 9.64064C20.3572 9.52669 20.274 9.4232 20.1691 9.3361L13.4191 3.7111C13.3145 3.62389 13.1902 3.55472 13.0535 3.50752C12.9168 3.46033 12.7702 3.43604 12.6222 3.43604C12.4742 3.43604 12.3277 3.46033 12.191 3.50752C12.0542 3.55472 11.93 3.62389 11.8254 3.7111C11.7207 3.7983 11.6377 3.90183 11.5811 4.01577C11.5244 4.12971 11.4953 4.25183 11.4953 4.37516C11.4953 4.49849 11.5244 4.62061 11.5811 4.73455C11.6377 4.84849 11.7207 4.95202 11.8254 5.03922L16.6554 9.06188H2.87411C2.57574 9.06188 2.2896 9.16065 2.07862 9.33647C1.86764 9.51228 1.74911 9.75074 1.74911 9.99938Z"
//                               fill="currentColor"
//                             />
//                           </g>
//                           <defs>
//                             <clipPath id="clip0_829_18584">
//                               <rect
//                                 width={20}
//                                 height={20}
//                                 fill="white"
//                                 transform="matrix(-1 0 0 1 20.5 0)"
//                               />
//                             </clipPath>
//                           </defs>
//                         </svg>
//                       </p>
//                     </div>
//                   </Link>
//                 </div>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       {/* Navigation Controls - No changes needed here */}
//       <div className="">
//         <button
//           onClick={handlePrev}
//           className="py-2 bg-white text-[#0A67F2] rounded-full backdrop-blur-sm absolute left-4 top-1/2 -translate-y-1/2 z-40 w-12 h-12 border border-dotted border-[#0A67F2B8] hover:bg-[#0A67F2] hover:text-white transition-all duration-300 ease-linear cursor-pointer flex items-center justify-center"
//           aria-label="Previous Slide"
//         >
//           <svg
//             xmlns="http://www.w3.org/2000/svg"
//             fill="none"
//             viewBox="0 0 24 24"
//             strokeWidth={1.5}
//             stroke="currentColor"
//             className="size-5"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
//             />
//           </svg>
//         </button>
//         <button
//           onClick={handleNext}
//           className="py-2 bg-white text-[#0A67F2] rounded-full backdrop-blur-sm absolute right-4 top-1/2 -translate-y-1/2 z-40 w-12 h-12 border border-dotted border-[#0A67F2B8] hover:bg-[#0A67F2] hover:text-white transition-all duration-300 ease-linear cursor-pointer flex items-center justify-center"
//           aria-label="Next Slide"
//         >
//           <svg
//             xmlns="http://www.w3.org/2000/svg"
//             fill="none"
//             viewBox="0 0 24 24"
//             strokeWidth={1.5}
//             stroke="currentColor"
//             className="size-5"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
//             />
//           </svg>
//         </button>
//       </div>
//     </div>
//   );
// };

// export default ImageSlicerSlider;

// "use client";
// import Link from "next/link";
// import React, { useState, useRef, useEffect } from "react";

// const ImageSlicerSlider = () => {
//   const images = [
//     {
//       image: "/assets/frontend_assets/1.png",
//       title: "Connect with Nearby Top-rated Professional 1",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//     {
//       image: "/assets/frontend_assets/2.png",
//       title: "Connect with Nearby Top-rated Professional 2",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//     {
//       image: "/assets/frontend_assets/3.png",
//       title: "Connect with Nearby Top-rated Professional 3",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//     {
//       image: "/assets/frontend_assets/4.png",
//       title: "Connect with Nearby Top-rated Professional 4",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//   ];

//   const [activeIndex, setActiveIndex] = useState(0);
//   const [prevActiveIndex, setPrevActiveIndex] = useState(null);
//   const [isInitialLoad, setIsInitialLoad] = useState(true);
//   const [isHovering, setIsHovering] = useState(false);
//   const contentRefs = useRef(new Array(images.length).fill(null));
//   const autoplayTimerRef = useRef(null);
//   const initialAnimationRef = useRef(null);
//   const autoplayInterval = 5000; // 5 seconds per slide

//   // Animation configuration
//   const animationConfig = {
//     initialDelay: 1000, // 1s delay on first load
//     sequenceDelays: {
//       title: 0,
//       description: 500,
//       buttons: 500,
//     },
//     duration: 800, // 0.8s
//   };

//   const handlePrev = () => {
//     resetAutoplayTimer();
//     setPrevActiveIndex(activeIndex);
//     setActiveIndex((prevIndex) =>
//       prevIndex === 0 ? images.length - 1 : prevIndex - 1
//     );
//   };

//   const handleNext = () => {
//     resetAutoplayTimer();
//     setPrevActiveIndex(activeIndex);
//     setActiveIndex((prevIndex) => (prevIndex + 1) % images.length);
//   };

//   const resetAutoplayTimer = () => {
//     if (autoplayTimerRef.current) {
//       clearInterval(autoplayTimerRef.current);
//     }
//     if (!isHovering) {
//       autoplayTimerRef.current = setInterval(handleNext, autoplayInterval);
//     }
//   };

//   const resetContentAnimations = (index) => {
//     if (contentRefs.current[index]) {
//       const contentElements = contentRefs.current[index].children;
//       Array.from(contentElements).forEach((el) => {
//         el.style.opacity = "0";
//         el.style.transform = "translateX(-20px)";
//         el.style.transition = "none";
//       });
//     }
//   };

//   const animateContentIn = (index) => {
//     if (!contentRefs.current[index]) {
//       return;
//     }

//     const contentElements = contentRefs.current[index].children;
//     if (!contentElements || contentElements.length < 3) {
//       return;
//     }

//     const [title, description, buttons] = contentElements;
//     if (!title || !description || !buttons) {
//       return;
//     }

//     const { initialDelay, sequenceDelays, duration } = animationConfig;

//     // Reset all
//     [title, description, buttons].forEach((el) => {
//       el.style.opacity = "0";
//       el.style.transform = "translateX(-20px)";
//       el.style.transition = "none";
//     });

//     const baseDelay = isInitialLoad ? initialDelay : 0;
//     const transition = `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`;

//     // Force reflow to ensure styles are applied before animation starts
//     void contentElements[0].offsetHeight;

//     setTimeout(() => {
//       title.style.transition = transition;
//       title.style.opacity = "1";
//       title.style.transform = "translateX(0)";

//       setTimeout(() => {
//         description.style.transition = transition;
//         description.style.opacity = "1";
//         description.style.transform = "translateX(0)";

//         setTimeout(() => {
//           buttons.style.transition = transition;
//           buttons.style.opacity = "1";
//           buttons.style.transform = "translateX(0)";
//         }, sequenceDelays.buttons);
//       }, sequenceDelays.description);
//     }, baseDelay);
//   };

//   // Initialize autoplay and animations
//   useEffect(() => {
//     // Initial setup - animate content only
//     if (isInitialLoad) {
//       initialAnimationRef.current = setTimeout(() => {
//         animateContentIn(activeIndex);

//         // Start autoplay after initial content animation
//         setTimeout(() => {
//           setIsInitialLoad(false);
//           if (!isHovering) {
//             autoplayTimerRef.current = setInterval(
//               handleNext,
//               autoplayInterval
//             );
//           }
//         }, 2000);
//       }, 500);
//     }

//     return () => {
//       if (autoplayTimerRef.current) {
//         clearInterval(autoplayTimerRef.current);
//       }
//       if (initialAnimationRef.current) {
//         clearTimeout(initialAnimationRef.current);
//       }
//     };
//   }, []);

//   // Handle hover state changes
//   useEffect(() => {
//     if (isHovering) {
//       if (autoplayTimerRef.current) {
//         clearInterval(autoplayTimerRef.current);
//       }
//     } else {
//       resetAutoplayTimer();
//     }
//   }, [isHovering]);

//   // Handle slide changes
//   useEffect(() => {
//     if (prevActiveIndex !== null) {
//       resetContentAnimations(prevActiveIndex);
//     }

//     animateContentIn(activeIndex);
//   }, [activeIndex, prevActiveIndex]);

//   return (
//     <div
//       className="h-[calc(100vh-70px)] w-full overflow-hidden relative"
//       onMouseEnter={() => setIsHovering(true)}
//       onMouseLeave={() => setIsHovering(false)}
//     >
//       {/* Image Slices Container */}
//       <div className="absolute inset-0">
//         {images.map((item, index) => (
//           <div
//             key={index}
//             className="absolute inset-0 transition-opacity duration-500"
//             style={{
//               opacity: index === activeIndex ? 1 : 0,
//               zIndex: index === activeIndex ? 20 : 10,
//               pointerEvents: index === activeIndex ? "auto" : "none",
//             }}
//           >
//             <CanvasImageSlicer
//               imageUrl={item.image}
//               isActive={index === activeIndex}
//               isPrevActive={index === prevActiveIndex}
//               isInitialLoad={isInitialLoad && index === 0}
//             />

//             {/* Content Overlay */}
//             <div className="absolute inset-0 flex items-center justify-center z-30">
//               <div
//                 className="max-w-7xl px-4 mx-auto w-full"
//                 ref={(el) => (contentRefs.current[index] = el)}
//               >
//                 <h2
//                   className="text-3xl lg:text-[43px] text-[#242B3A] font-bold mb-4 opacity-0 transform -translate-x-5 archivo max-w-3xl"
//                   style={{
//                     transition:
//                       "opacity 800ms ease-out, transform 800ms ease-out",
//                   }}
//                 >
//                   {item.title}
//                 </h2>
//                 <p
//                   className="text-lg lg:text-xl font-normal text-[#656B76] mb-8 opacity-0 transform -translate-x-5"
//                   style={{
//                     transition:
//                       "opacity 800ms ease-out 500ms, transform 800ms ease-out 500ms",
//                   }}
//                 >
//                   {item.text}
//                 </p>
//                 <div
//                   className="opacity-0 transform -translate-x-5 flex items-center space-x-4"
//                   style={{
//                     transition:
//                       "opacity 800ms ease-out 500ms, transform 800ms ease-out 500ms",
//                   }}
//                 >
//                   <Link
//                     href=""
//                     className="relative p-0.5 overflow-hidden rounded-lg w-[150px] lg:w-[200px] cursor-pointer group"
//                   >
//                     <div className="absolute inset-0 bg-[#0A67F2] group-hover:bg-gradient-to-r group-hover:from-[#FD2692] group-hover:to-[#0A67F2] rounded-lg"></div>
//                     <div className="relative dm_sans px-2 lg:px-5 py-2 lg:py-2.5 bg-[#0A67F2] rounded-lg group-hover:bg-transparent transition-all duration-300 ease-linear text-center">
//                       <p className="archivo bg-clip-text text-center text-white group-hover:text-white text-sm lg:text-lg font-semibold transition-all duration-300 ease-in-out inline-flex items-center">
//                         <span>Post a request</span>
//                       </p>
//                     </div>
//                   </Link>

//                   <Link
//                     href=""
//                     className="relative p-0.5 overflow-hidden rounded-lg w-[150px] lg:w-[200px] cursor-pointer group"
//                   >
//                     <div className="absolute inset-0 bg-gradient-to-r from-[#FD2692] to-[#0A67F2] rounded-lg"></div>
//                     <div className="relative dm_sans px-2 lg:px-5 py-2 lg:py-2.5 bg-white rounded-lg group-hover:bg-transparent transition-all duration-300 ease-linear text-center">
//                       <p className="archivo bg-clip-text text-center text-[#0A67F2] group-hover:text-white text-sm lg:text-lg font-semibold transition-all duration-300 ease-in-out inline-flex items-center">
//                         <span>Join as seller</span>
//                         <svg
//                           className="lg:ml-2 ml-1 w-5 h-5 lg:w-4 lg:h-4 inline-block transition-all duration-300 ease-linear"
//                           viewBox="0 0 21 20"
//                           fill="none"
//                           xmlns="http://www.w3.org/2000/svg"
//                         >
//                           <g clipPath="url(#clip0_829_18584)">
//                             <path
//                               d="M1.74911 9.99938C1.74911 10.248 1.86764 10.4865 2.07862 10.6623C2.2896 10.8381 2.57574 10.9369 2.87411 10.9369H16.6554L11.8254 14.9611C11.614 15.1372 11.4953 15.3761 11.4953 15.6252C11.4953 15.8742 11.614 16.1131 11.8254 16.2892C12.0367 16.4653 12.3234 16.5643 12.6222 16.5643C12.9211 16.5643 13.2078 16.4653 13.4191 16.2892L20.1691 10.6642C20.274 10.5771 20.3572 10.4736 20.414 10.3597C20.4708 10.2457 20.5 10.1235 20.5 10.0002C20.5 9.87677 20.4708 9.7546 20.414 9.64064C20.3572 9.52669 20.274 9.4232 20.1691 9.3361L13.4191 3.7111C13.3145 3.62389 13.1902 3.55472 13.0535 3.50752C12.9168 3.46033 12.7702 3.43604 12.6222 3.43604C12.4742 3.43604 12.3277 3.46033 12.191 3.50752C12.0542 3.55472 11.93 3.62389 11.8254 3.7111C11.7207 3.7983 11.6377 3.90183 11.5811 4.01577C11.5244 4.12971 11.4953 4.25183 11.4953 4.37516C11.4953 4.49849 11.5244 4.62061 11.5811 4.73455C11.6377 4.84849 11.7207 4.95202 11.8254 5.03922L16.6554 9.06188H2.87411C2.57574 9.06188 2.2896 9.16065 2.07862 9.33647C1.86764 9.51228 1.74911 9.75074 1.74911 9.99938Z"
//                               fill="currentColor"
//                             />
//                           </g>
//                         </svg>
//                       </p>
//                     </div>
//                   </Link>
//                 </div>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       {/* Navigation Controls */}
//       <div className="">
//         <button
//           onClick={handlePrev}
//           className="py-2 bg-white text-[#0A67F2] rounded-full backdrop-blur-sm absolute left-4 top-1/2 -translate-y-1/2 z-40 w-12 h-12 border border-dotted border-[#0A67F2B8] hover:bg-[#0A67F2] hover:text-white transition-all duration-300 ease-linear cursor-pointer flex items-center justify-center"
//         >
//           <svg
//             xmlns="http://www.w3.org/2000/svg"
//             fill="none"
//             viewBox="0 0 24 24"
//             strokeWidth={1.5}
//             stroke="currentColor"
//             className="size-5"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
//             />
//           </svg>
//         </button>
//         <button
//           onClick={handleNext}
//           className="py-2 bg-white text-[#0A67F2] rounded-full backdrop-blur-sm absolute right-4 top-1/2 -translate-y-1/2 z-40 w-12 h-12 border border-dotted border-[#0A67F2B8] hover:bg-[#0A67F2] hover:text-white transition-all duration-300 ease-linear cursor-pointer flex items-center justify-center"
//         >
//           <svg
//             xmlns="http://www.w3.org/2000/svg"
//             fill="none"
//             viewBox="0 0 24 24"
//             strokeWidth={1.5}
//             stroke="currentColor"
//             className="size-5"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
//             />
//           </svg>
//         </button>
//       </div>
//     </div>
//   );
// };

// const CanvasImageSlicer = ({
//   imageUrl,
//   isActive,
//   isPrevActive,
//   isInitialLoad,
// }) => {
//   const containerRef = useRef(null);
//   const canvasRefs = useRef([]);
//   const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
//   const [isImageLoaded, setIsImageLoaded] = useState(false);
//   const SLICE_COUNT = 5;

//   useEffect(() => {
//     const updateDimensions = () => {
//       if (typeof window !== "undefined") {
//         setDimensions({
//           width: window.innerWidth,
//           height: window.innerHeight,
//         });
//       }
//     };

//     updateDimensions();
//     window.addEventListener("resize", updateDimensions);
//     return () => window.removeEventListener("resize", updateDimensions);
//   }, []);

//   useEffect(() => {
//     if (dimensions.width === 0 || dimensions.height === 0) return;

//     const img = new Image();
//     img.onload = () => {
//       const sliceWidth = dimensions.width / SLICE_COUNT;

//       canvasRefs.current.forEach((canvas, i) => {
//         if (!canvas) return;

//         canvas.width = sliceWidth;
//         canvas.height = dimensions.height;
//         const ctx = canvas.getContext("2d");
//         ctx.clearRect(0, 0, canvas.width, canvas.height);

//         const sourceX = (i / SLICE_COUNT) * img.width;
//         const sourceWidth = img.width / SLICE_COUNT;
//         const drawHeight = (sliceWidth * img.height) / sourceWidth;
//         const offsetY = (dimensions.height - drawHeight) / 2;

//         ctx.drawImage(
//           img,
//           sourceX,
//           0,
//           sourceWidth,
//           img.height,
//           0,
//           offsetY,
//           sliceWidth,
//           drawHeight
//         );
//       });

//       setIsImageLoaded(true);
//     };
//     img.src = imageUrl;
//   }, [imageUrl, dimensions]);

//   // Handle all slide animations (initial and subsequent)
//   useEffect(() => {
//     if (!containerRef.current || !isImageLoaded) return;

//     const sliceElements = Array.from(containerRef.current.children);
//     const ANIMATION_DURATION = 0.7;
//     const STAGGER_DELAY = 0.1;

//     if (isActive) {
//       // Active slide coming in (from right)
//       sliceElements.forEach((sliceEl, i) => {
//         sliceEl.style.transition = "none";
//         sliceEl.style.transform = "translateX(100px)";
//         sliceEl.style.opacity = "0";

//         // Apply transition after initial state is set
//         setTimeout(() => {
//           sliceEl.style.transition = `transform ${ANIMATION_DURATION}s ease ${
//             i * STAGGER_DELAY
//           }s, opacity ${ANIMATION_DURATION}s ease ${i * STAGGER_DELAY}s`;
//           sliceEl.style.transform = "translateX(0)";
//           sliceEl.style.opacity = "1";
//         }, 50);
//       });
//     } else if (isPrevActive) {
//       // Previous slide going out (to left)
//       sliceElements.forEach((sliceEl, i) => {
//         const delay = (SLICE_COUNT - i - 1) * STAGGER_DELAY;
//         sliceEl.style.transition = `transform ${ANIMATION_DURATION}s ease ${delay}s, opacity ${ANIMATION_DURATION}s ease ${delay}s`;
//         sliceEl.style.transform = "translateX(-100px)";
//         sliceEl.style.opacity = "0";
//       });
//     }
//   }, [isActive, isPrevActive, isImageLoaded]);

//   return (
//     <div ref={containerRef} className="flex absolute inset-0 h-full">
//       {Array.from({ length: SLICE_COUNT }).map((_, i) => (
//         <div
//           key={i}
//           className="relative h-full overflow-hidden"
//           style={{
//             width: `${100 / SLICE_COUNT}%`,
//             opacity: isActive ? 1 : 0,
//           }}
//         >
//           <canvas
//             ref={(el) => (canvasRefs.current[i] = el)}
//             className="block h-full w-full object-cover"
//           />
//         </div>
//       ))}
//     </div>
//   );
// };

// export default ImageSlicerSlider;

// "use client";
// import Link from "next/link";
// import React, { useState, useRef, useEffect } from "react";

// const ImageSlicerSlider = () => {
//   const images = [
//     {
//       image: "/assets/frontend_assets/1.png",
//       title: "Connect with Nearby Top-rated Professional",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//     {
//       image: "/assets/frontend_assets/2.png",
//       title: "Connect with Nearby Top-rated Professional",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//     {
//       image: "/assets/frontend_assets/3.png",
//       title: "Connect with Nearby Top-rated Professional",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//     {
//       image: "/assets/frontend_assets/4.png",
//       title: "Connect with Nearby Top-rated Professional",
//       text: "We can connect you to the right Service, first time and every time.",
//     },
//   ];

//   const [activeIndex, setActiveIndex] = useState(0);
//   const [prevActiveIndex, setPrevActiveIndex] = useState(null);
//   const [animationDirection, setAnimationDirection] = useState("next"); // 'next' or 'prev'
//   const contentRefs = useRef([]);

//   const handlePrev = () => {
//     setPrevActiveIndex(activeIndex);
//     setAnimationDirection("prev");
//     setActiveIndex((prevIndex) =>
//       prevIndex === 0 ? images.length - 1 : prevIndex - 1
//     );
//   };

//   const handleNext = () => {
//     setPrevActiveIndex(activeIndex);
//     setAnimationDirection("next");
//     setActiveIndex((prevIndex) => (prevIndex + 1) % images.length);
//   };

//   // Reset all content animations when slide changes
//   const resetContentAnimations = (index) => {
//     if (contentRefs.current[index]) {
//       const contentElements = contentRefs.current[index].children;
//       Array.from(contentElements).forEach((el) => {
//         el.style.opacity = "0";
//         el.style.transform = "translateX(-20px)";
//         el.style.transition = "none";
//       });
//     }
//   };

//   // Animate content in when slide becomes active
//   const animateContentIn = (index) => {
//     if (contentRefs.current[index]) {
//       const contentElements = contentRefs.current[index].children;
//       Array.from(contentElements).forEach((el, i) => {
//         setTimeout(() => {
//           el.style.transition =
//             "opacity 0.5s ease-out, transform 0.5s ease-out";
//           el.style.opacity = "1";
//           el.style.transform = "translateX(0)";
//         }, i * 200);
//       });
//     }
//   };

//   useEffect(() => {
//     // Reset animation for previous slide
//     if (prevActiveIndex !== null) {
//       resetContentAnimations(prevActiveIndex);
//     }

//     // Animate new slide content
//     animateContentIn(activeIndex);
//   }, [activeIndex, prevActiveIndex]);

//   return (
//     <div className="h-[calc(100vh-70px)] w-full overflow-hidden relative">
//       {/* Image Slices Container */}
//       <div className="absolute inset-0">
//         {images.map((item, index) => (
//           <div
//             key={index}
//             className="absolute inset-0 transition-opacity duration-500"
//             style={{
//               opacity: index === activeIndex ? 1 : 0,
//               zIndex: index === activeIndex ? 20 : 10,
//               pointerEvents: index === activeIndex ? "auto" : "none",
//             }}
//           >
//             <CanvasImageSlicer
//               imageUrl={item.image}
//               isActive={index === activeIndex}
//               isPrevActive={index === prevActiveIndex}
//               animationDirection={animationDirection}
//             />

//             {/* Content Overlay */}
//             <div className="absolute inset-0 flex items-center justify-center z-30">
//               <div
//                 className="max-w-7xl px-4 mx-auto w-full"
//                 ref={(el) => (contentRefs.current[index] = el)}
//               >
//                 <h2 className="text-[43px] text-[#242B3A] font-bold mb-4 opacity-0 transform -translate-x-5 archivo max-w-3xl">
//                   {item.title}
//                 </h2>
//                 <p className="text-xl font-normal text-[#656B76] mb-8 opacity-0 transform -translate-x-5">
//                   {item.text}
//                 </p>
//                 <div className="opacity-0 transform -translate-x-5 flex items-center space-x-4">
//                   <Link
//                     href=""
//                     className="relative p-0.5 overflow-hidden rounded-lg w-[200px] cursor-pointer group"
//                   >
//                     {/* Gradient border */}
//                     <div className="absolute inset-0 bg-[#0A67F2] group-hover:bg-gradient-to-r group-hover:from-[#FD2692] group-hover:to-[#0A67F2] rounded-lg"></div>

//                     {/* Button content with solid background */}
//                     <div className="relative dm_sans px-5 py-2.5 bg-[#0A67F2] rounded-lg group-hover:bg-transparent transition-all duration-300 ease-linear text-center">
//                       <p className=" archivo bg-clip-text text-center text-white group-hover:text-white  text-lg font-semibold transition-all duration-300 ease-in-out inline-flex items-center">
//                         <span className="transition-all duration-300 ease-linear">
//                           Post a request
//                         </span>
//                       </p>
//                     </div>
//                   </Link>

//                   <Link
//                     href=""
//                     className="relative p-0.5 overflow-hidden rounded-lg w-[200px] cursor-pointer group"
//                   >
//                     {/* Gradient border */}
//                     <div className="absolute inset-0 bg-gradient-to-r from-[#FD2692] to-[#0A67F2] rounded-lg"></div>

//                     {/* Button content with solid background */}
//                     <div className="relative dm_sans px-5 py-2.5 bg-white rounded-lg group-hover:bg-transparent transition-all duration-300 ease-linear text-center">
//                       <p className=" archivo bg-clip-text text-center text-[#0A67F2] group-hover:text-white text-lg font-semibold transition-all duration-300 ease-in-out inline-flex items-center">
//                         <span className="transition-all duration-300 ease-linear">
//                           Join as seller{" "}
//                         </span>
//                         <svg
//                           width={21}
//                           height={20}
//                           className="ml-2 inline-block transition-all duration-300 ease-linear"
//                           viewBox="0 0 21 20"
//                           fill="none"
//                           xmlns="http://www.w3.org/2000/svg"
//                         >
//                           <g clipPath="url(#clip0_829_18584)">
//                             <path
//                               d="M1.74911 9.99938C1.74911 10.248 1.86764 10.4865 2.07862 10.6623C2.2896 10.8381 2.57574 10.9369 2.87411 10.9369H16.6554L11.8254 14.9611C11.614 15.1372 11.4953 15.3761 11.4953 15.6252C11.4953 15.8742 11.614 16.1131 11.8254 16.2892C12.0367 16.4653 12.3234 16.5643 12.6222 16.5643C12.9211 16.5643 13.2078 16.4653 13.4191 16.2892L20.1691 10.6642C20.274 10.5771 20.3572 10.4736 20.414 10.3597C20.4708 10.2457 20.5 10.1235 20.5 10.0002C20.5 9.87677 20.4708 9.7546 20.414 9.64064C20.3572 9.52669 20.274 9.4232 20.1691 9.3361L13.4191 3.7111C13.3145 3.62389 13.1902 3.55472 13.0535 3.50752C12.9168 3.46033 12.7702 3.43604 12.6222 3.43604C12.4742 3.43604 12.3277 3.46033 12.191 3.50752C12.0542 3.55472 11.93 3.62389 11.8254 3.7111C11.7207 3.7983 11.6377 3.90183 11.5811 4.01577C11.5244 4.12971 11.4953 4.25183 11.4953 4.37516C11.4953 4.49849 11.5244 4.62061 11.5811 4.73455C11.6377 4.84849 11.7207 4.95202 11.8254 5.03922L16.6554 9.06188H2.87411C2.57574 9.06188 2.2896 9.16065 2.07862 9.33647C1.86764 9.51228 1.74911 9.75074 1.74911 9.99938Z"
//                               fill="currentColor"
//                             />
//                           </g>
//                           <defs>
//                             <clipPath id="clip0_829_18584">
//                               <rect
//                                 width={20}
//                                 height={20}
//                                 fill="white"
//                                 transform="matrix(-1 0 0 1 20.5 0)"
//                               />
//                             </clipPath>
//                           </defs>
//                         </svg>
//                       </p>
//                     </div>
//                   </Link>
//                 </div>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       {/* Navigation Controls */}
//       <div className="">
//         <button
//           onClick={handlePrev}
//           className="py-2 bg-white text-[#0A67F2] rounded-full backdrop-blur-sm absolute left-4 top-1/2 -translate-y-1/2 z-40 w-12 h-12 border border-dotted border-[#0A67F2B8] hover:bg-[#0A67F2] hover:text-white transition-all duration-300 ease-linear cursor-pointer flex items-center justify-center"
//         >
//           <svg
//             xmlns="http://www.w3.org/2000/svg"
//             fill="none"
//             viewBox="0 0 24 24"
//             strokeWidth={1.5}
//             stroke="currentColor"
//             className="size-5"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
//             />
//           </svg>
//         </button>
//         <button
//           onClick={handleNext}
//           className="py-2 bg-white text-[#0A67F2] rounded-full backdrop-blur-sm absolute right-4 top-1/2 -translate-y-1/2 z-40 w-12 h-12 border border-dotted border-[#0A67F2B8] hover:bg-[#0A67F2] hover:text-white transition-all duration-300 ease-linear cursor-pointer flex items-center justify-center"
//         >
//           <svg
//             xmlns="http://www.w3.org/2000/svg"
//             fill="none"
//             viewBox="0 0 24 24"
//             strokeWidth={1.5}
//             stroke="currentColor"
//             className="size-5"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
//             />
//           </svg>
//         </button>
//       </div>
//     </div>
//   );
// };

// const CanvasImageSlicer = ({
//   imageUrl,
//   isActive,
//   isPrevActive,
//   animationDirection,
// }) => {
//   const containerRef = useRef(null);
//   const canvasRefs = useRef([]);
//   const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
//   const SLICE_COUNT = 5;

//   // Set full viewport dimensions
//   useEffect(() => {
//     const updateDimensions = () => {
//       setDimensions({
//         width: window.innerWidth,
//         height: window.innerHeight,
//       });
//     };

//     updateDimensions();
//     window.addEventListener("resize", updateDimensions);
//     return () => window.removeEventListener("resize", updateDimensions);
//   }, []);

//   // Draw image slices
//   useEffect(() => {
//     if (dimensions.width === 0 || dimensions.height === 0) return;

//     const img = new Image();
//     img.src = imageUrl;
//     img.onload = () => {
//       const sliceWidth = dimensions.width / SLICE_COUNT;

//       canvasRefs.current.forEach((canvas, i) => {
//         if (!canvas) return;

//         canvas.width = sliceWidth;
//         canvas.height = dimensions.height;
//         const ctx = canvas.getContext("2d");
//         ctx.clearRect(0, 0, canvas.width, canvas.height);

//         // Calculate source rectangle
//         const sourceX = (i / SLICE_COUNT) * img.width;
//         const sourceWidth = img.width / SLICE_COUNT;

//         // Maintain aspect ratio while filling the screen
//         const drawHeight = (sliceWidth * img.height) / sourceWidth;
//         const offsetY = (dimensions.height - drawHeight) / 2;

//         ctx.drawImage(
//           img,
//           sourceX,
//           0,
//           sourceWidth,
//           img.height,
//           0,
//           offsetY,
//           sliceWidth,
//           drawHeight
//         );
//       });
//     };
//   }, [imageUrl, dimensions]);

//   // Animation effect when slide changes - now with horizontal movement
//   useEffect(() => {
//     if (!containerRef.current) return;

//     const sliceElements = Array.from(containerRef.current.children);
//     const ANIMATION_DURATION = 0.7;
//     const STAGGER_DELAY = 0.1;

//     sliceElements.forEach((sliceEl, i) => {
//       // Reset styles
//       sliceEl.style.transform = "";
//       sliceEl.style.opacity = "";
//       sliceEl.style.transition = "";

//       if (isActive) {
//         // Enter animation - horizontal direction
//         // If next direction, slices come from right
//         // If prev direction, slices come from left
//         const distance = dimensions.width / SLICE_COUNT;
//         const direction =
//           animationDirection === "next" ? `${distance}px` : `-${distance}px`;

//         sliceEl.style.transform = `translateX(${direction})`;
//         sliceEl.style.opacity = "0";

//         setTimeout(() => {
//           sliceEl.style.transition = `transform ${ANIMATION_DURATION}s ease ${
//             i * STAGGER_DELAY
//           }s, opacity ${ANIMATION_DURATION}s ease ${i * STAGGER_DELAY}s`;
//           sliceEl.style.transform = "translateX(0)";
//           sliceEl.style.opacity = "1";
//         }, 50);
//       } else if (isPrevActive) {
//         // Exit animation - horizontal direction in reverse order
//         setTimeout(() => {
//           sliceEl.style.transition = `transform ${ANIMATION_DURATION}s ease ${
//             (SLICE_COUNT - i - 1) * STAGGER_DELAY
//           }s, opacity ${ANIMATION_DURATION}s ease ${
//             (SLICE_COUNT - i - 1) * STAGGER_DELAY
//           }s`;

//           // If next was clicked, exit to left
//           // If prev was clicked, exit to right
//           const distance = dimensions.width / SLICE_COUNT;
//           const direction =
//             animationDirection === "next" ? `-${distance}px` : `${distance}px`;

//           sliceEl.style.transform = `translateX(${direction})`;
//           sliceEl.style.opacity = "0";
//         }, 50);
//       }
//     });
//   }, [isActive, isPrevActive, animationDirection, dimensions.width]);

//   return (
//     <div ref={containerRef} className="flex absolute inset-0">
//       {Array.from({ length: SLICE_COUNT }).map((_, i) => (
//         <div
//           key={i}
//           className="relative h-full overflow-hidden"
//           style={{ width: `${100 / SLICE_COUNT}%` }}
//         >
//           <canvas
//             ref={(el) => (canvasRefs.current[i] = el)}
//             className="block h-full w-full object-cover"
//           />
//         </div>
//       ))}
//     </div>
//   );
// };

// export default ImageSlicerSlider;
