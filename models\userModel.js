const { prisma } = require('../config/dbConfig');
const bcrypt = require('bcrypt');
const crypto = require('crypto');



const UserModel = {
  async createUser(data) {
    const password_hash = await bcrypt.hash(data.password, 10);
    delete data.password;

    // Remove password_confirmation if it exists
    if (data.password_confirmation) {
      delete data.password_confirmation;
    }

    // Extract role from payload before spreading data
    const { role, ...userData } = data;

    // Convert date_of_birth to Date object if it's a string
    if (userData.date_of_birth && typeof userData.date_of_birth === 'string') {
      userData.date_of_birth = new Date(userData.date_of_birth);
    }

    return await prisma.users.create({
      data: {
        ...userData,  // Spread all other user data
        password_hash,
        is_email_verified: false,
        roles: {
          create: {
            role: {
              connect: {
                name: role  // Use the extracted role
              }
            }
          }
        }
      },
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    });
},

  async findById (id) {
    const user = await prisma.users.findUnique({
      where: { id },
      select: {
        // Basic info
        id: true,
        username: true,
        first_name: true,
        last_name: true,
        father_name: true,
        mother_name: true,
        date_of_birth: true,
        gender: true,
        email: true,
        phone_number: true,
        business_name: true,

        // Address fields
        address: true,
        street: true,
        city: true,
        state: true,
        zip: true,
        country: true,

        // Profile fields
        user_type: true,
        age_confirm: true,
        occupation: true,
        interests: true,
        bio: true,

        // Business fields
        business_type: true,
        business_website: true,
        business_registration_number: true,
        business_address: true,
        tax_id: true,

        // Document URLs
        profile_picture_url: true,
        business_document_url: true,
        nid_document_url: true,

        // Status fields
        is_email_verified: true,
        is_approved: true,
        status: true,
        is_deleted: true,
        is_completed_profile: true,

        // ID numbers
        national_id_number: true,
        passport_number: true,

        // Timestamps
        created_at: true,
        updated_at: true,
        last_login_at: true,

        // Relations
        roles: {
          include: {
            role: true
          }
        },
        business_informations: {
          where: {
            is_deleted: false,
            is_active: true
          }
        },

        // Exclude sensitive fields
        password_hash: false,
        refresh_token: false,
        email_verification_token: false,
        password_reset_token: false
      }
    });

    if (!user) return null;

    // Transform roles to simple array
    user.roles = user.roles.map(userRole => userRole.role.name);
    return user;
  },
  async findUserByEmail(email) {

    const user = await prisma.users.findUnique({
      where: { email },
      select: {
        id: true,
        first_name: true,
        last_name: true,
        username: true,
        email: true,
        phone_number: true,
        user_type: true,
        is_email_verified: true,
        is_approved: true,
        status: true,
        is_deleted: true,
        is_completed_profile: true,
        created_at: true,
        updated_at: true,
        gender: true,
        address: true,
        roles: {
          include: {
            role: true
          }
        },
        password_hash: true
      }
    });

    if (!user) return null;

    // Transform roles to simple array
    user.roles = user.roles.map(userRole => userRole.role.name);
    return user;
  },

  async findUserByUsername(username) {
    const user = await prisma.users.findUnique({
      where: { username },
      select: {
        id: true,
        first_name: true,
        last_name: true,
        username: true,
        email: true,
        phone_number: true,
        user_type: true,
        is_email_verified: true,
        is_approved: true,
        status: true,
        is_deleted: true,
        is_completed_profile: true,
        created_at: true,
        updated_at: true,
        gender: true,
        address: true,
        roles: {
          include: {
            role: true
          }
        },
        password_hash: true
      }
    });

    if (!user) return null;

    // Transform roles to simple array
    user.roles = user.roles.map(userRole => userRole.role.name);
    return user;
  },

  async findUserByPhone(phone_number) {
    return await prisma.users.findUnique({ where: { phone_number } });
  },
  async findUserById(userId) {
    const user = await prisma.users.findUnique({
      where: { id: userId },
      include: {
        roles: {
          include: {
            role: true
          }
        },
        business_informations: {
          where: {
            is_deleted: false
          },
          orderBy: {
            created_at: 'desc'
          },
          select: {
            id: true,
            company_name: true,
            short_description: true,
            long_description: true,
            logo_url: true,
            banner_url: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postal_code: true,
            phone_number: true,
            email: true,
            website_url: true,
            business_type: true,
            business_category: true,
            established_year: true,
            employee_count: true,
            annual_revenue: true,
            business_license: true,
            tax_id: true,
            social_media_links: true,
            operating_hours: true,
            services_offered: true,
            certifications: true,
            is_verified: true,
            verification_status: true,
            verification_date: true,
            is_active: true,
            created_at: true,
            updated_at: true
          }
        }
      }
    });

    if (!user) return null;

    // Transform roles to simple array
    const userRoles = user.roles.map(userRole => userRole.role.name);
    user.roles = userRoles;

    // Only include business information if user is a seller
    if (!userRoles.includes('Seller')) {
      delete user.business_informations;
    }

    return user;
  },

  async findUserByIdWithRoles(userId) {
    const user = await prisma.users.findUnique({
      where: { id: userId },
      include: {
        roles: {
          include: {
            role: true
          }
        },
        business_informations: {
          where: {
            is_deleted: false
          },
          orderBy: {
            created_at: 'desc'
          },
          select: {
            id: true,
            company_name: true,
            short_description: true,
            long_description: true,
            logo_url: true,
            banner_url: true,
            address: true,
            city: true,
            state: true,
            country: true,
            postal_code: true,
            phone_number: true,
            email: true,
            website_url: true,
            business_type: true,
            business_category: true,
            established_year: true,
            employee_count: true,
            annual_revenue: true,
            business_license: true,
            tax_id: true,
            social_media_links: true,
            operating_hours: true,
            services_offered: true,
            certifications: true,
            is_verified: true,
            verification_status: true,
            verification_date: true,
            is_active: true,
            created_at: true,
            updated_at: true
          }
        }
      }
    });

    if (!user) return null;

    // Transform roles to simple array
    const userRoles = user.roles.map(userRole => userRole.role.name);
    user.roles = userRoles;

    // Only include business information if user is a seller
    if (!userRoles.includes('Seller')) {
      delete user.business_informations;
    }

    return user;
  },

  async comparePassword(candidatePassword, hashedPassword) {
    return await bcrypt.compare(candidatePassword, hashedPassword);
  },

  async updateUser(userId, data) {
    // Create a copy of data to avoid modifying the original object
    const userData = { ...data };

    // Remove non-database fields
    delete userData._method;

    // Set is_email_verified to false if email is being updated
    if (userData.email) {
      userData.is_email_verified = false;
    }

    console.log('Updating user with data:', userData);

    return await prisma.users.update({
      where: { id: userId },
      data: userData,
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    });
  },

  async storeRefreshToken(userId, token) {
    return await prisma.users.update({
      where: { id: userId },
      data: { 
        refresh_token: token 
      },
    });
  },

  async generateEmailVerificationToken(userId) {
    const token = crypto.randomBytes(32).toString('hex');
    await prisma.users.update({
      where: { id: userId },
      data: {
        email_verification_token: token,
        email_verification_expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      },
    });
    return token;
  },

  async verifyEmail(userId, token) {
    const user = await prisma.users.findUnique({ where: { id: userId } });
    if (user && user.email_verification_token === token && user.email_verification_expires > new Date()) {
      await prisma.users.update({
        where: { id: userId },
        data: {
          is_email_verified: true,
          email_verification_token: null,
          email_verification_expires: null,
        },
      });
      return true;
    }
    return false;
  },

  async generatePasswordResetToken(userId) {
    try {
      const token = crypto.randomBytes(32).toString('hex');
      const expiresIn = process.env.PASSWORD_RESET_EXPIRES_IN || '3600000'; // default 1 hour
      await prisma.users.update({
        where: { id: userId },
        data: {
          password_reset_token: token,
          password_reset_expires_at: new Date(Date.now() + parseInt(expiresIn)),
        },
      });
      return token;
    } catch (error) {
      console.error('Error generating password reset token:', error);
      throw new Error('Failed to generate password reset token');
    }
  },

  async findUserByResetToken(token) {
    return await prisma.users.findFirst({
      where: {
        password_reset_token: token,
        password_reset_expires_at: {
          gt: new Date()
        }
      }
    });
  },

  async resetPassword(userId, newPassword) {
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await prisma.users.update({
      where: { id: userId },
      data: {
        password_hash: hashedPassword,  // Make sure this matches your schema too
        password_reset_token: null,
        password_reset_expires_at: null,
      },
    });
  },

  async updateUserRole(userId, roles, adminId) {
    // First, get the existing user to check their email
    const existingUser = await prisma.users.findUnique({
      where: { id: userId },
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    });

    if (!existingUser) {
      throw new Error('User not found');
    }

    // Delete existing roles
    await prisma.user_roles.deleteMany({
      where: { user_id: userId }
    });

    // Create new role assignments
    const rolePromises = roles.map(async (roleName) => {
      // Find the role ID
      const role = await prisma.roles.findUnique({
        where: { name: roleName }
      });

      if (!role) {
        throw new Error(`Role '${roleName}' not found`);
      }

      // Create the user-role relationship
      return prisma.user_roles.create({
        data: {
          user_id: userId,
          role_id: role.id
        }
      });
    });

    await Promise.all(rolePromises);

    // Get the updated user with new roles
    const updatedUser = await prisma.users.findUnique({
      where: { id: userId },
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    });

    // Transform roles to simple array for the log message
    const roleNames = updatedUser.roles.map(userRole => userRole.role.name);

    // Only create activity log if adminId is provided
    if (adminId) {
      await this.createActivityLog(adminId, userId, 'User Role Updated', `Admin updated roles for user: ${existingUser.email}. New roles: ${roleNames.join(', ')}`);
    }

    // Transform roles to simple array for the response
    updatedUser.roles = roleNames;

    return updatedUser;
  },

  async deleteUser(userId, adminId) {
    const user = await this.findUserById(userId);
    if (!user) throw new Error('User not found');

    await prisma.users.delete({ where: { id: userId } });
    await this.createActivityLog(adminId, userId, 'User Deleted', `Admin deleted user: ${user.email}`);
  },

  async getAllUsers (
    page = 1,
    limit = 10,
    role = 'Buyer',
    search = '',
    status = null,
    isApproved = null,
    includeRelations = false
  ) {
    const skip = (page - 1) * limit;

    // Build the where clause dynamically
    const whereClause = {};

    // Filter by role if specified
    if (role) {
      whereClause.roles = {
        some: {
          role: {
            name: role
          }
        }
      };
    }

    // Add search functionality
    if (search) {
      whereClause.OR = [
        { first_name: { contains: search, mode: 'insensitive' } },
        { last_name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone_number: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Filter by status if specified
    if (status) {
      whereClause.status = status;
    }

    // Filter by approval status if specified
    if (isApproved !== null) {
      whereClause.is_approved = isApproved;
    }

    // Include relations if requested
    const include = includeRelations ? {
      roles: {
        include: {
          role: true
        }
      },
      permissions: {
        include: {
          permission: true
        }
      },
      business_informations: {
        where: {
          is_deleted: false
        },
        orderBy: {
          created_at: 'desc'
        },
        select: {
          id: true,
          company_name: true,
          short_description: true,
          long_description: true,
          logo_url: true,
          banner_url: true,
          address: true,
          city: true,
          state: true,
          country: true,
          postal_code: true,
          phone_number: true,
          email: true,
          website_url: true,
          business_type: true,
          business_category: true,
          established_year: true,
          employee_count: true,
          annual_revenue: true,
          business_license: true,
          tax_id: true,
          social_media_links: true,
          operating_hours: true,
          services_offered: true,
          certifications: true,
          is_verified: true,
          verification_status: true,
          verification_date: true,
          is_active: true,
          created_at: true,
          updated_at: true
        }
      }
    } : {
      roles: {
        include: {
          role: true
        }
      },
      business_informations: {
        where: {
          is_deleted: false
        },
        orderBy: {
          created_at: 'desc'
        },
        select: {
          id: true,
          company_name: true,
          short_description: true,
          long_description: true,
          logo_url: true,
          banner_url: true,
          address: true,
          city: true,
          state: true,
          country: true,
          postal_code: true,
          phone_number: true,
          email: true,
          website_url: true,
          business_type: true,
          business_category: true,
          established_year: true,
          employee_count: true,
          annual_revenue: true,
          business_license: true,
          tax_id: true,
          social_media_links: true,
          operating_hours: true,
          services_offered: true,
          certifications: true,
          is_verified: true,
          verification_status: true,
          verification_date: true,
          is_active: true,
          created_at: true,
          updated_at: true
        }
      }
    };

    const [users, totalUsers] = await Promise.all([
      prisma.users.findMany({
        where: whereClause,
        skip,
        take: limit,
        include,
        orderBy: {
          created_at: 'desc'
        }
      }),
      prisma.users.count({ where: whereClause })
    ]);

    // Process users to filter business information for non-sellers
    const processedUsers = users.map(user => {
      if (user.roles) {
        const userRoles = user.roles.map(userRole => userRole.role.name);
        user.roles = userRoles;

        // Only include business information if user is a seller
        if (!userRoles.includes('Seller')) {
          delete user.business_informations;
        }
      }
      return user;
    });

    const totalPages = Math.ceil(totalUsers / limit);

    return {
      success: true,
      data: processedUsers,
      meta: {
        total: totalUsers,
        pages: totalPages,
        currentPage: page,
        perPage: limit
      }
    };
  },

  async getAllRoles() {
    return await prisma.roles.findMany();
  },

  async updateRole(roleId, roleName, adminId) {
    const role = await prisma.roles.update({
      where: { id: roleId },
      data: { name: roleName },
    });

    await this.createActivityLog(adminId, null, 'Role Updated', `Super Admin updated role to: ${roleName}`);
    return role;
  },

  async createActivityLog(adminId, userId, action, details) {
    try {
      await prisma.activity_logs.create({
        data: {
          user_id: adminId || userId || '00000000-0000-0000-0000-000000000000',
          action,
          details
        },
      });
    } catch (error) {
      console.error('Error creating activity log:', error);
      // Don't throw the error to prevent it from affecting the main operation
    }
  },
  async getActivityLogs() {
    return await prisma.activity_logs.findMany({
      orderBy: { timestamp: 'desc' },
    });
  }
};

module.exports = UserModel;

