"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import GoBack from "@/components/backend/GoBack";
import Breadcrumb from "@/components/backend/Breadcrumb";
import Link from "next/link";
import { useFetchApiQuery } from "@/redux/services/api";
import { handleUnauthorized } from '@/utils/auth';

const OrderDetailsPage = ({ params }) => {
  // Unwrap the params promise
  const unwrappedParams = React.use(params);
  const orderId = unwrappedParams?.orderId;

  const router = useRouter();

  return (
    <OrderDetailsContent
      orderId={orderId}
      router={router}
    />
  );
};

const OrderDetailsContent = ({
  orderId,
  router,
}) => {
  // Fetch order details from API
  const {
    data: orderData,
    isLoading,
    error
  } = useFetchApiQuery({
    endpoint: `/buyer/orders/${orderId}`,
    skip: !orderId,
  });

  // Handle unauthorized access
  useEffect(() => {
    if (error) {
      handleUnauthorized(error, router);
    }
  }, [error, router]);

  // Extract order data from API response
  const order = orderData?.data || {};

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status color
  const getStatusColor = (status) => {
    status = status?.toLowerCase() || '';

    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
      case 'canceled':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'refunded':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <GoBack />
        <Breadcrumb />
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <GoBack />
        <Breadcrumb />
        <div className="text-center py-10">
          <p className="text-red-500">Error loading order details</p>
          <Link
            href="/buyer/orders"
            className="mt-4 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
          >
            Back to Orders
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <div className="flex justify-between items-center">
          <h5 className="font-semibold text-[#343A40] text-xl poppins">
            Order Details
          </h5>
          <span className={`px-3 py-1 rounded-full text-sm ${getStatusColor(order.order_status || order.status)}`}>
            {order.order_status || order.status || 'Pending'}
          </span>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-5">
          {/* Order Summary */}
          <div className="lg:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h2 className="text-lg font-semibold">Order #{order.order_number || `ORD-${order.id.substring(0, 8)}`}</h2>
                  <p className="text-gray-600">Placed on {formatDate(order.created_at || order.date)}</p>
                </div>
                <Link
                  href="/buyer/orders"
                  className="text-sm font-medium px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Back to Orders
                </Link>
              </div>

              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-md font-semibold mb-4">Order Items</h3>

                <div className="overflow-x-auto">
                  <table className="w-full text-sm text-left text-gray-500">
                    <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3">Item</th>
                        <th scope="col" className="px-4 py-3">Quantity</th>
                        <th scope="col" className="px-4 py-3">Price</th>
                        <th scope="col" className="px-4 py-3">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {(order.items || order.order_items || []).map((item, index) => (
                        <tr key={item.id || index} className="bg-white border-b">
                          <td className="px-4 py-4 font-medium text-gray-900">
                            <div>
                              <p className="font-semibold">{item.title || item.name || 'Item'}</p>
                              {item.description && (
                                <p className="text-xs text-gray-500 mt-1">{item.description}</p>
                              )}
                              {item.delivery_time && (
                                <p className="text-xs text-gray-500 mt-1">Delivery Time: {item.delivery_time}</p>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-4">{item.quantity || 1}</td>
                          <td className="px-4 py-4">${(item.price_per_unit || item.price || 0).toFixed(2)}</td>
                          <td className="px-4 py-4 font-semibold">
                            ${(item.total_price || ((item.price_per_unit || item.price || 0) * (item.quantity || 1))).toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-50">
                        <td colSpan="3" className="px-4 py-3 text-right font-semibold">Total</td>
                        <td className="px-4 py-3 font-semibold">${(order.total_amount || order.total || order.amount || 0).toFixed(2)}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </div>
          </div>

          {/* Order Information */}
          <div className="lg:col-span-1">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-md font-semibold mb-4">Order Information</h3>

              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600">Payment Method</p>
                  <p className="font-medium">{order.payment_method || 'Credit Card'}</p>
                  {order.payment_status && (
                    <p className="text-xs mt-1">
                      <span className={`px-2 py-1 rounded-full ${getStatusColor(order.payment_status)}`}>
                        Payment: {order.payment_status}
                      </span>
                    </p>
                  )}
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <p className="text-sm text-gray-600">Billing Address</p>
                  <div className="mt-1">
                    {order.billing_info ? (
                      <>
                        <p className="font-medium">{order.billing_info.full_name}</p>
                        <p>{order.billing_info.address}</p>
                        <p>{order.billing_info.city}, {order.billing_info.state} {order.billing_info.zip_code}</p>
                        <p>{order.billing_info.country}</p>
                      </>
                    ) : order.billing_address ? (
                      <>
                        <p className="font-medium">{order.billing_address.name}</p>
                        <p>{order.billing_address.address}</p>
                        <p>{order.billing_address.city}, {order.billing_address.state} {order.billing_address.zip}</p>
                        <p>{order.billing_address.country}</p>
                      </>
                    ) : (
                      <p className="text-gray-500">No billing information available</p>
                    )}
                  </div>
                </div>

                {order.tracking_code && (
                  <div className="border-t border-gray-200 pt-4">
                    <p className="text-sm text-gray-600">Tracking Information</p>
                    <p className="font-medium mt-1">Tracking Code: {order.tracking_code}</p>
                    {order.delivery_date && (
                      <p className="text-sm">Expected Delivery: {formatDate(order.delivery_date)}</p>
                    )}
                  </div>
                )}

                <div className="border-t border-gray-200 pt-4">
                  <p className="text-sm text-gray-600">Need Help?</p>
                  <div className="mt-2">
                    <Link
                      href="/contact"
                      className="text-sm font-medium px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors inline-block"
                    >
                      Contact Support
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetailsPage;
