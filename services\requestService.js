const RequestModel = require('../models/requestModel');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

class RequestService {
  /**
   * Create a new request
   * @param {Object} requestData - Request data
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Object>} Created request
   */
  static async createRequest(requestData, buyerId) {
    try {
      return await RequestModel.createRequest(requestData, buyerId);

    } catch (error) {
      throw new Error(`Failed to create request: ${error.message}`);
    }
  }

  /**
   * Get request by ID
   * @param {string} requestId - Request ID
   * @param {string} userId - User ID making the request
   * @param {string} userRole - User role
   * @returns {Promise<Object>} Request details
   */
  static async getRequestById(requestId, userId, userRoles) {
    try {
      const request = await RequestModel.getRequestById(requestId);

      if (!request) {
        throw new Error('Request not found');
      }

      // Authorization check
      if (request.buyer_id !== userId && !userRoles.includes('Admin')) {
        throw new Error('Unauthorized access');
      }

      return request;
    } catch (error) {
      throw new Error(`Failed to get request: ${error.message}`);
    }
  }

  /**
   * Update request
   * @param {string} requestId - Request ID
   * @param {Object} updateData - Data to update
   * @param {Object} user - User object making the update
   * @returns {Promise<Object>} Updated request
   */
  static async updateRequest(requestId, updateData, user) {
    try {
      const existingRequest = await RequestModel.getRequestById(requestId);

      console.log('incoming data: ', updateData);

      if (!existingRequest) {
        throw new Error('Request not found');
      }

      // Authorization check
      if (existingRequest.buyer_id !== user.id && !user.roles.includes('Admin')) {
        throw new Error('Unauthorized to update this request');
      }

      // Business rule: Buyers can't change status directly
      if (updateData.status && user.roles.includes('Buyer') && updateData.status !== existingRequest.status) {
        throw new Error('Buyers cannot directly change request status');
      }

      // Handle file updates
      const updatePayload = { ...updateData };

      // Remove special fields that are not directly updated
      delete updatePayload.new_attachments;
      delete updatePayload.new_file;
      delete updatePayload.delete_attachments;

      // Process removed images if present
      let removedImageIds = [];
      if (updateData.removed_images && Array.isArray(updateData.removed_images)) {
        removedImageIds = updateData.removed_images;
        delete updatePayload.removed_images;
      }

      // If there's a new main file, update it and delete the old one
      if (updateData.new_file) {
        // Delete old file if it exists
        if (existingRequest.file) {
          const { deleteFile } = require('../utils/fileDelete');
          deleteFile(existingRequest.file);
        }

        // Set the new file path
        updatePayload.file = updateData.new_file;
      }

      return await RequestModel.updateRequest(
        requestId,
        updatePayload,
        user.id,
        updateData.new_attachments,
        removedImageIds
      );
    } catch (error) {
      throw new Error(`Failed to update request: ${error.message}`);
    }
  }

  /**
   * Delete request (soft delete)
   * @param {string} requestId - Request ID
   * @param {string} userId - User ID making the request
   * @param {string} userRole - User role
   * @returns {Promise<Object>} Deletion result
   */
  static async deleteRequest(requestId, userId, userRole) {
    try {
      const existingRequest = await RequestModel.getRequestById(requestId);

      if (!existingRequest) {
        throw new Error('Request not found');
      }

      // Authorization check
      if (existingRequest.buyer_id !== userId && userRole !== 'admin') {
        throw new Error('Unauthorized to delete this request');
      }

      return await RequestModel.deleteRequest(requestId);
    } catch (error) {
      throw new Error(`Failed to delete request: ${error.message}`);
    }
  }

  /**
   * Admin delete request (hard delete with all related data)
   * @param {string} requestId - Request ID
   * @param {string} adminId - Admin ID making the request
   * @returns {Promise<Object>} Deletion result
   */
  static async adminDeleteRequest(requestId, adminId) {
    try {
      const existingRequest = await RequestModel.getRequestById(requestId);

      if (!existingRequest) {
        throw new Error('Request not found');
      }

      // This method is only called from an admin-protected route, so no need for additional auth checks

      // Perform the complete deletion of the request and all related data
      return await RequestModel.adminDeleteRequest(requestId, adminId);
    } catch (error) {
      throw new Error(`Failed to delete request: ${error.message}`);
    }
  }

  /**
   * Get request status history
   * @param {string} requestId - Request ID
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Array>} Status history of the request
   */
  static async getRequestStatusHistory(requestId, buyerId) {
    try {
      const statusHistory = await RequestModel.getRequestStatusHistory(requestId, buyerId);

      if (!statusHistory) {
        throw new Error('Request not found or you are not authorized to view this request');
      }

      return statusHistory;
    } catch (error) {
      throw new Error(`Failed to get request status history: ${error.message}`);
    }
  }

  /**
   * Get request status history for admin
   * @param {string} requestId - Request ID
   * @returns {Promise<Array>} Status history of the request
   */
  static async getRequestStatusHistoryForAdmin(requestId) {
    try {
      const statusHistory = await RequestModel.getRequestStatusHistoryForAdmin(requestId);

      if (!statusHistory) {
        throw new Error('Request not found');
      }

      return statusHistory;
    } catch (error) {
      throw new Error(`Failed to get request status history: ${error.message}`);
    }
  }

  /**
   * Get filtered requests
   * @param {Object} filters - Filter criteria
   * @param {string} userId - User ID making the request
   * @param {string} userRole - User role
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated requests
   */
  static async getRequests(filters, user, page = 1, limit = 10) {
    try {
      // Apply role-based filtering
      const finalFilters = { ...filters };

      // Determine if this is an admin context
      const isAdminContext = user.roles.includes('Admin');

      // Ensure buyers can only see their own requests
      if (user.roles.includes('Buyer')) {
        finalFilters.buyer_id = user.id;
      }

      // For sellers, they should only see requests assigned to them
      // This is handled in the sellerModel.getAssignedRequests method

      // For admins, they can see all requests (no additional filtering needed)
      // Admin context will filter out child requests (is_child = false)

      return await RequestModel.getRequests(finalFilters, page, limit, isAdminContext);
    } catch (error) {
      throw new Error(`Failed to get requests: ${error.message}`);
    }
  }

  /**
   * Add attachment to request
   * @param {string} requestId - Request ID
   * @param {Object} attachmentData - Attachment data
   * @param {string} userId - User ID making the request
   * @param {string} userRole - User role
   * @returns {Promise<Object>} Created attachment
   */
  static async addAttachment(requestId, attachmentData, userId, userRole) {
    try {
      const existingRequest = await RequestModel.getRequestById(requestId);

      if (!existingRequest) {
        throw new Error('Request not found');
      }

      // Authorization check
      if (existingRequest.buyer_id !== userId && userRole !== 'admin') {
        throw new Error('Unauthorized to add attachments to this request');
      }

      return await RequestModel.addAttachment(requestId, attachmentData);
    } catch (error) {
      throw new Error(`Failed to add attachment: ${error.message}`);
    }
  }

  /**
   * Delete attachment from request
   * @param {string} attachmentId - Attachment ID
   * @param {string} userId - User ID making the request
   * @param {Array} userRoles - User roles
   * @returns {Promise<Object>} Deleted attachment
   */
  static async deleteAttachment(attachmentId, userId, userRoles) {
    try {
      // First get the attachment to check authorization
      const attachment = await prisma.request_attachments.findUnique({
        where: { id: attachmentId },
        include: {
          request: true
        }
      });

      if (!attachment) {
        throw new Error('Attachment not found');
      }

      // Authorization check
      if (attachment.request.buyer_id !== userId && !userRoles.includes('Admin')) {
        throw new Error('Unauthorized to delete this attachment');
      }

      return await RequestModel.deleteAttachment(attachmentId);
    } catch (error) {
      throw new Error(`Failed to delete attachment: ${error.message}`);
    }
  }

  /**
   * Assign sellers to a request
   * @param {string} requestId - Request ID
   * @param {string[]} sellerIds - Array of seller IDs to assign
   * @param {string} userId - User ID making the request
   * @param {string[]} userRoles - User roles
   * @param {string} notes - Optional notes about the assignment
   * @returns {Promise<Object>} Request with assigned sellers
   */
  static async assignSellers(requestId, sellerIds, userId, userRoles, notes = null) {
    try {
      const existingRequest = await RequestModel.getRequestById(requestId);

      if (!existingRequest) {
        throw new Error('Request not found');
      }

      // Authorization check - only admins can assign sellers
      if (!userRoles.includes('Admin')) {
        throw new Error('Unauthorized to assign sellers to this request');
      }

      return await RequestModel.assignSellers(requestId, sellerIds, userId, notes);
    } catch (error) {
      throw new Error(`Failed to assign sellers: ${error.message}`);
    }
  }

  /**
   * Cancel a request
   * @param {string} requestId - Request ID
   * @param {string} userId - User ID making the request
   * @param {string} reason - Reason for cancellation
   * @returns {Promise<Object>} Cancelled request
   */
  static async cancelRequest(requestId, userId, reason = null) {
    try {
      const existingRequest = await RequestModel.getRequestById(requestId);

      if (!existingRequest) {
        throw new Error('Request not found');
      }

      // Authorization check - only the buyer who created the request can cancel it
      if (existingRequest.buyer_id !== userId) {
        throw new Error('Unauthorized to cancel this request');
      }

      // Check if the request is already cancelled
      if (existingRequest.status === 'Cancelled') {
        throw new Error('Request is already cancelled');
      }

      // Check if the request can be cancelled
      const nonCancellableStatuses = ['Completed', 'Delivered', 'Closed'];
      if (nonCancellableStatuses.includes(existingRequest.status)) {
        throw new Error(`Cannot cancel a request with status: ${existingRequest.status}`);
      }

      // Update the request status to Cancelled
      const updateData = {
        status: 'Cancelled',
        reason: reason || 'Cancelled by buyer'
      };

      return await RequestModel.updateRequest(requestId, updateData, userId);
    } catch (error) {
      throw new Error(`Failed to cancel request: ${error.message}`);
    }
  }

  /**
   * Bulk request actions (approve, reject, delete)
   * @param {Array} requestIds - Array of request IDs
   * @param {string} action - Action to perform (approve, reject, delete)
   * @param {string} note - Note for the action
   * @param {string} adminId - Admin user ID
   * @returns {Promise<Object>} Result with successful and failed operations
   */
  static async bulkRequestAction(requestIds, action, note, adminId) {
    const successful = [];
    const failed = [];

    // Process each request individually to handle partial failures
    for (const requestId of requestIds) {

      try {
        let result;

        switch (action) {
          case 'approve':
            result = await RequestModel.approveRequest(requestId, adminId, note);
            break;
          case 'reject':
            result = await RequestModel.rejectRequest(requestId, adminId, note);
            break;
          case 'delete':
            result = await RequestModel.adminDeleteRequest(requestId, adminId);
            break;
          default:
            throw new Error(`Invalid action: ${action}`);
        }

        successful.push({
          request_id: requestId,
          action: action,
          result: result,
          note: note
        });

      } catch (error) {
        console.error(`Failed to ${action} request ${requestId}:`, error.message);
        failed.push({
          request_id: requestId,
          action: action,
          error: error.message,
          note: note
        });
      }
    }

    // Log the bulk operation
    console.log(`Bulk ${action} operation completed by admin ${adminId}:`, {
      total: requestIds.length,
      successful: successful.length,
      failed: failed.length
    });

    return {
      total: requestIds.length,
      successful: successful,
      failed: failed,
      summary: {
        total_processed: requestIds.length,
        successful_count: successful.length,
        failed_count: failed.length,
        action: action,
        admin_id: adminId,
        note: note,
        processed_at: new Date().toISOString()
      }
    };
  }

  /**
   * Create a new request by merging multiple existing requests
   * @param {Object} newRequestData - New request data from admin
   * @param {string[]} requestIdsToMerge - Array of request IDs to merge
   * @param {string} adminId - Admin ID performing the merge
   * @returns {Promise<Object>} Newly created request with merged items
   */
  static async createMergedRequest(newRequestData, requestIdsToMerge, adminId) {
    try {
      // Validate that all requests to merge exist and are not already merged
      for (const requestId of requestIdsToMerge) {
        const request = await RequestModel.getRequestById(requestId);
        if (!request) {
          throw new Error(`Request ${requestId} not found`);
        }
        if (request.status === 'Merged') {
          throw new Error(`Request ${requestId} is already merged`);
        }
      }

      // Create the new merged request
      return await RequestModel.createMergedRequest(newRequestData, requestIdsToMerge, adminId);
    } catch (error) {
      throw new Error(`Failed to create merged request: ${error.message}`);
    }
  }

  /**
   * Create a merged request with offers for each child request (Admin only)
   * @param {string} message - Message for the merged request
   * @param {string} description - Description for the merged request
   * @param {Array} mergedChildrenOffers - Array of offers for child requests
   * @param {string} adminId - Admin ID performing the merge
   * @returns {Promise<Object>} Newly created merged request with offers
   */
  static async createMergedRequestWithOffers(message, description, mergedChildrenOffers, adminId) {
    try {
      // Extract request IDs from the offers
      const requestIdsToMerge = mergedChildrenOffers.map(offer => offer.request_id);

      // Validate that all requests exist and are not already merged
      const requestsToMerge = [];
      for (const requestId of requestIdsToMerge) {
        const request = await RequestModel.getRequestById(requestId);
        if (!request) {
          throw new Error(`Request ${requestId} not found`);
        }
        if (request.status === 'Merged') {
          throw new Error(`Request ${requestId} is already merged`);
        }
        requestsToMerge.push(request);
      }

      // Calculate aggregated data from child requests
      const totalBudgetMin = requestsToMerge.reduce((sum, req) => sum + (req.budget_min || 0), 0);
      const totalBudgetMax = requestsToMerge.reduce((sum, req) => sum + (req.budget_max || 0), 0);
      const totalQuantity = requestsToMerge.reduce((sum, req) => sum + (req.quantity || 1), 0);

      // Use the first request's category and subcategory as default
      const firstRequest = requestsToMerge[0];

      // Create the merged request data
      const mergedRequestData = {
        title: `Merged Request: ${message}`,
        short_description: message,
        description: description,
        category_id: firstRequest.category_id,
        sub_category_id: firstRequest.sub_category_id,
        quantity: totalQuantity,
        budget_min: totalBudgetMin,
        budget_max: totalBudgetMax,
        urgency: 'Normal',
        request_type: 'General',
        additional_info: `Auto-generated merged request from ${requestIdsToMerge.length} child requests`
      };

      // Create the merged request
      const mergedRequest = await RequestModel.createMergedRequest(
        mergedRequestData,
        requestIdsToMerge,
        adminId
      );

      // Create offers for each child request (Admin bypass)
      const { prisma } = require('../config/dbConfig');
      const createdOffers = [];

      for (const offerData of mergedChildrenOffers) {
        try {
          // Find the corresponding request
          const childRequest = requestsToMerge.find(req => req.id === offerData.request_id);

          // Create offer directly in database (bypass seller assignment validation for admin)
          const offer = await prisma.offers.create({
            data: {
              request_id: mergedRequest.id, // Link to the merged request
              seller_id: adminId, // Admin as seller
              offer_title: `Admin Offer for ${childRequest.title}`,
              short_description: `Admin-generated offer for merged request child: ${childRequest.title}`,
              description: `This offer was automatically generated by admin for the merged request.

Original Request: ${childRequest.title}
Original Request ID: ${offerData.request_id}
Merged Request ID: ${mergedRequest.id}
Admin Generated: Yes

${childRequest.description || 'No additional description'}`,
              price: offerData.price,
              delivery_time: offerData.delivery_time,
              quantity: childRequest.quantity || 1,
              message: `Admin offer for child request: ${offerData.request_id}`,
              offer_type: 'service',
              status: 'Pending'
            },
            include: {
              request: {
                include: {
                  buyer: {
                    select: {
                      id: true,
                      first_name: true,
                      last_name: true,
                      email: true,
                      profile_picture_url: true,
                    },
                  },
                  category: true,
                  sub_category: true,
                },
              },
              seller: {
                select: {
                  id: true,
                  first_name: true,
                  last_name: true,
                  email: true,
                  profile_picture_url: true,
                },
              },
            }
          });

          createdOffers.push({
            ...offer,
            original_request: {
              id: childRequest.id,
              title: childRequest.title,
              buyer: childRequest.buyer
            }
          });
        } catch (offerError) {
          console.error(`Failed to create offer for request ${offerData.request_id}:`, offerError);
          // Continue with other offers even if one fails
        }
      }

      return {
        merged_request: mergedRequest,
        created_offers: createdOffers,
        summary: {
          total_child_requests: requestIdsToMerge.length,
          total_offers_created: createdOffers.length,
          total_budget_range: `$${totalBudgetMin} - $${totalBudgetMax}`,
          total_quantity: totalQuantity
        }
      };
    } catch (error) {
      throw new Error(`Failed to create merged request with offers: ${error.message}`);
    }
  }
}

module.exports = RequestService;