/* Custom styles for react-datepicker */

/* Main calendar container */
.react-datepicker {
  font-family: 'Inter', sans-serif;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  background-color: white;
}

/* Header with month and year */
.react-datepicker__header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  padding-top: 0.75rem;
}

/* Month text */
.react-datepicker__current-month {
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 0.5rem;
}

/* Navigation buttons */
.react-datepicker__navigation {
  top: 0.75rem;
}

/* Day names row */
.react-datepicker__day-names {
  margin-top: 0.5rem;
  display: flex;
  justify-content: space-around;
}

/* Day name */
.react-datepicker__day-name {
  color: #6b7280;
  font-size: 0.75rem;
  width: 2rem;
  line-height: 2rem;
}

/* Day cell */
.react-datepicker__day {
  width: 2rem;
  line-height: 2rem;
  margin: 0.125rem;
  border-radius: 0.25rem;
  color: #374151;
}

/* Hover state for day */
.react-datepicker__day:hover {
  background-color: #f3f4f6;
}

/* Selected day */
.react-datepicker__day--selected {
  background-color: #4f46e5 !important;
  color: white !important;
  font-weight: 500;
}

/* Today's date */
.react-datepicker__day--today {
  font-weight: 600;
  color: #4f46e5;
}

/* Disabled days (e.g., past dates when minDate is set) */
.react-datepicker__day--disabled {
  color: #d1d5db;
  cursor: not-allowed;
}

/* Month container */
.react-datepicker__month {
  margin: 0.5rem;
}

/* Triangle pointer */
.react-datepicker__triangle {
  display: none;
}

/* Input field when focused */
.react-datepicker__input-container input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 1px #4f46e5;
}

/* Prevent text selection in the calendar */
.react-datepicker, 
.react-datepicker__header, 
.react-datepicker__day, 
.react-datepicker__day-name {
  user-select: none;
}
