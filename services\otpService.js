const { prisma } = require('../config/dbConfig');
const nodemailer = require('nodemailer');

const OTP_EXPIRY_MINUTES = 10;

function generateOtp() {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
}

async function sendOtpEmail(email, otp) {
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: 'Your Marktzoom Registration OTP',
    html: `<p>Your OTP for registration is: <b>${otp}</b>. It is valid for ${OTP_EXPIRY_MINUTES} minutes.</p>`
  };

  await transporter.sendMail(mailOptions);
}

async function storePendingRegistration(email, otp, data) {
  const expiresAt = new Date(Date.now() + OTP_EXPIRY_MINUTES * 60 * 1000);
  // Upsert: if exists, update; else, create
  await prisma.pending_registrations.upsert({
    where: { email },
    update: { otp, data, expires_at: expiresAt },
    create: { email, otp, data, expires_at: expiresAt },
  });
}

async function getPendingRegistration(email) {
  return prisma.pending_registrations.findUnique({ where: { email } });
}

async function deletePendingRegistration(email) {
  return prisma.pending_registrations.delete({ where: { email } });
}

module.exports = {
  generateOtp,
  sendOtpEmail,
  storePendingRegistration,
  getPendingRegistration,
  deletePendingRegistration,
  OTP_EXPIRY_MINUTES,
};
