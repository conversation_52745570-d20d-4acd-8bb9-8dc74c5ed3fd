/**
 * Simple test script to verify Prisma connection and SellerInterestModel functionality
 */

const { prisma } = require('../config/dbConfig');
const SellerInterestModel = require('../models/sellerInterestModel');

async function testPrismaConnection() {
  console.log('🔧 Testing Prisma connection and SellerInterestModel...\n');

  try {
    // Test basic Prisma connection
    console.log('1. Testing basic Prisma connection...');
    await prisma.$connect();
    console.log('✅ Prisma connection successful');

    // Test a simple query
    console.log('2. Testing simple query...');
    const userCount = await prisma.users.count();
    console.log(`✅ Found ${userCount} users in database`);

    // Test SellerInterestModel methods
    console.log('3. Testing SellerInterestModel methods...');
    
    // Find a seller to test with
    const seller = await prisma.users.findFirst({
      where: {
        roles: {
          some: {
            role: {
              name: '<PERSON><PERSON>'
            }
          }
        }
      }
    });

    if (seller) {
      console.log(`✅ Found test seller: ${seller.first_name} ${seller.last_name} (${seller.id})`);
      
      // Test getInterestedCategories method
      console.log('4. Testing getInterestedCategories method...');
      const categories = await SellerInterestModel.getInterestedCategories(seller.id);
      console.log(`✅ Retrieved ${categories.length} interested categories for seller`);
      
      // Test getInterestedSubcategories method
      console.log('5. Testing getInterestedSubcategories method...');
      const subcategories = await SellerInterestModel.getInterestedSubcategories(seller.id);
      console.log(`✅ Retrieved ${subcategories.length} interested subcategories for seller`);
      
    } else {
      console.log('⚠️ No seller found in database to test with');
    }

    console.log('\n🎉 All tests passed! The Prisma connection issue has been resolved.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
    throw error;
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Prisma connection closed');
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testPrismaConnection()
    .then(() => {
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testPrismaConnection };
