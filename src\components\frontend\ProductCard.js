import Image from "next/image";
import Link from "next/link";
import React from "react";

const ProductCard = ({ item, index }) => {
  const { img, title, rating } = item;
  return (
    <>
      <div
        className="bg-[#FFFFFF01] rounded-[10px] overflow-hidden shadow-md mb-3"
        data-aos="fade-left"
        data-aos-offset="200"
        data-aos-delay={50 * (index + 1)}
        data-aos-duration="1000"
        data-aos-easing="ease-in-out"
      >
        <Image
          src={img}
          width={1000}
          height={1000}
          quality={100}
          className="object-contain mx-auto w-full h-auto"
          alt={title || "Product image"}
        />
        <div className="flex flex-col p-4 border border-[#EBECED] border-t-0">
          <h6 className="text-[#242B3A] archivo font-semibold text-lg mb-1">
            {title}{" "}
          </h6>
          <p className="text-[#74788D] archivo font-normal text-sm mb-1">
            {rating} (254 Reviews){" "}
          </p>

          <Link
            href="/requests/add"
            className="relative p-0.5 overflow-hidden rounded-lg group mt-2"
          >
            {/* Gradient border */}
            <div className="absolute inset-0 bg-gradient-to-r from-[#FD2692] to-[#0A67F2] rounded-lg group-hover:bg-linear-to-t group-hover:from-[#0A67F2] group-hover:to-[#1658BA]"></div>

            {/* Button content with solid background */}
            <div className="relative dm_sans px-5 py-2 bg-white rounded-lg group-hover:bg-transparent transition-all duration-300 ease-linear text-center">
              <p className="font-medium text-sm archivo bg-clip-text text-center text-[#0A67F2] group-hover:text-white transition-all duration-300 ease-in-out inline-flex items-center">
                <span className="transition-all duration-300 ease-linear">
                  Post a request
                </span>
                <svg
                  width={21}
                  height={20}
                  className="ml-2 group-hover:hidden inline-block transition-all duration-300 ease-linear"
                  viewBox="0 0 21 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clipPath="url(#clip0_829_18584)">
                    <path
                      d="M1.74911 9.99938C1.74911 10.248 1.86764 10.4865 2.07862 10.6623C2.2896 10.8381 2.57574 10.9369 2.87411 10.9369H16.6554L11.8254 14.9611C11.614 15.1372 11.4953 15.3761 11.4953 15.6252C11.4953 15.8742 11.614 16.1131 11.8254 16.2892C12.0367 16.4653 12.3234 16.5643 12.6222 16.5643C12.9211 16.5643 13.2078 16.4653 13.4191 16.2892L20.1691 10.6642C20.274 10.5771 20.3572 10.4736 20.414 10.3597C20.4708 10.2457 20.5 10.1235 20.5 10.0002C20.5 9.87677 20.4708 9.7546 20.414 9.64064C20.3572 9.52669 20.274 9.4232 20.1691 9.3361L13.4191 3.7111C13.3145 3.62389 13.1902 3.55472 13.0535 3.50752C12.9168 3.46033 12.7702 3.43604 12.6222 3.43604C12.4742 3.43604 12.3277 3.46033 12.191 3.50752C12.0542 3.55472 11.93 3.62389 11.8254 3.7111C11.7207 3.7983 11.6377 3.90183 11.5811 4.01577C11.5244 4.12971 11.4953 4.25183 11.4953 4.37516C11.4953 4.49849 11.5244 4.62061 11.5811 4.73455C11.6377 4.84849 11.7207 4.95202 11.8254 5.03922L16.6554 9.06188H2.87411C2.57574 9.06188 2.2896 9.16065 2.07862 9.33647C1.86764 9.51228 1.74911 9.75074 1.74911 9.99938Z"
                      fill="#0A67F2"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_829_18584">
                      <rect
                        width={20}
                        height={20}
                        fill="white"
                        transform="matrix(-1 0 0 1 20.5 0)"
                      />
                    </clipPath>
                  </defs>
                </svg>
              </p>
            </div>
          </Link>
        </div>
      </div>
    </>
  );
};

export default ProductCard;
