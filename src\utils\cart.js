import { toast } from 'react-hot-toast';

/**
 * Place an order with the items in the cart
 * @param {Object} mutateApi - The mutation API function from Redux
 * @param {Object} billingInfo - The billing information for the order
 * @param {Object} cardInfo - The card information for the order
 * @param {string} cartId - The ID of the cart
 * @param {number} amount - The total amount of the order
 * @returns {Promise} - A promise that resolves when the order is placed
 */
export const placeOrder = async (mutateApi, billingInfo, cardInfo, cartId, amount) => {
  try {
    const loadingToast = toast.loading('Processing your order...');

    // Format the request data according to the API requirements
    const requestData = {
      billing_info: {
        full_name: billingInfo.fullName,
        email: billingInfo.email,
        address: billingInfo.address,
        city: billingInfo.city,
        state: billingInfo.state,
        zip_code: billingInfo.zipCode,
        country: billingInfo.country
      },
      card_info: {
        card_number: cardInfo.cardNumber.replace(/\s+/g, ''),
        expiry_month: parseInt(cardInfo.cardExpiry.split('/')[0], 10),
        expiry_year: 2000 + parseInt(cardInfo.cardExpiry.split('/')[1], 10),
        cvv: cardInfo.cardCvc
      },
      cart_id: cartId,
      amount: amount
    };

    // Make the API call to confirm the order
    const response = await mutateApi({
      endpoint: '/buyer/orders/confirm',
      method: 'POST',
      data: requestData
    });

    toast.dismiss(loadingToast);
    toast.success('Order placed successfully!');

    return response?.data;
  } catch (error) {
    toast.error(error?.data?.message || 'Failed to place order');
    return null;
  }
};

/**
 * Add an item to the cart
 * @param {Object} mutateApi - The mutation API function from Redux
 * @param {string} offerId - The ID of the offer to add to the cart
 * @param {number} quantity - The quantity to add (defaults to 1)
 * @returns {Promise} - A promise that resolves when the item is added to the cart
 */
export const addToCart = async (mutateApi, offerId, quantity = 1) => {
  if (!offerId) {
    toast.error('Invalid offer ID');
    return null;
  }

  try {
    const loadingToast = toast.loading('Adding item to cart...');

    const response = await mutateApi({
      endpoint: '/buyer/cart',
      method: 'POST',
      data: {
        offer_id: offerId,
        quantity: quantity
      }
    });

    toast.dismiss(loadingToast);
    toast.success('Item added to cart successfully');

    return response?.data;
  } catch (error) {
    toast.error(error?.data?.message || 'Failed to add item to cart');
    return null;
  }
};

/**
 * Remove an item from the cart
 * @param {Object} mutateApi - The mutation API function from Redux
 * @param {string} itemId - The ID of the cart item to remove
 * @returns {Promise} - A promise that resolves when the item is removed from the cart
 */
export const removeFromCart = async (mutateApi, itemId) => {
  if (!itemId) {
    toast.error('Invalid cart item ID');
    return null;
  }

  try {
    const loadingToast = toast.loading('Removing item from cart...');

    const response = await mutateApi({
      endpoint: `/buyer/cart/${itemId}`,
      method: 'DELETE'
    });

    toast.dismiss(loadingToast);
    toast.success('Item removed from cart successfully');

    return response?.data;
  } catch (error) {
    toast.error(error?.data?.message || 'Failed to remove item from cart');
    return null;
  }
};

/**
 * Update a cart item's quantity
 * @param {Object} mutateApi - The mutation API function from Redux
 * @param {string} itemId - The ID of the cart item to update
 * @param {number} quantity - The new quantity
 * @returns {Promise} - A promise that resolves when the item is updated
 */
export const updateCartItem = async (mutateApi, itemId, quantity) => {
  if (!itemId) {
    toast.error('Invalid cart item ID');
    return null;
  }

  try {
    const loadingToast = toast.loading('Updating cart...');

    const response = await mutateApi({
      endpoint: `/buyer/cart/${itemId}`,
      method: 'PUT',
      data: {
        quantity: quantity
      }
    });

    toast.dismiss(loadingToast);
    toast.success('Cart updated successfully');

    return response?.data;
  } catch (error) {
    toast.error(error?.data?.message || 'Failed to update cart');
    return null;
  }
};

/**
 * Clear the entire cart
 * @param {Object} mutateApi - The mutation API function from Redux
 * @returns {Promise} - A promise that resolves when the cart is cleared
 */
export const clearCart = async (mutateApi) => {
  try {
    const loadingToast = toast.loading('Clearing cart...');

    const response = await mutateApi({
      endpoint: '/buyer/cart',
      method: 'DELETE'
    });

    toast.dismiss(loadingToast);
    toast.success('Cart cleared successfully');

    return response?.data;
  } catch (error) {
    toast.error(error?.data?.message || 'Failed to clear cart');
    return null;
  }
};
