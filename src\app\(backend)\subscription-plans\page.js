"use client";

import React, { useState } from 'react';
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import { useFetchApiQuery, useMutateApiMutation } from '@/redux/services/api';
import { subscriptionEndpoints } from '@/services/subscriptionService';
import { getUserRole } from '@/utils/user';
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';

const SubscriptionPlans = () => {
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('credit_card');
  const [autoRenew, setAutoRenew] = useState(true);
  const [isSubscribing, setIsSubscribing] = useState(false);
  const userRole = getUserRole();

  // Fetch available plans
  const { data: plansData, isLoading } = useFetchApiQuery({
    endpoint: subscriptionEndpoints.getAvailablePlans(),
    skip: false
  });

  // Fetch current active subscription
  const { data: activeSubscriptionData } = useFetchApiQuery({
    endpoint: subscriptionEndpoints.getActiveSubscription(),
    skip: false
  });

  // Subscribe mutation
  const [mutateApi] = useMutateApiMutation();

  const plans = plansData?.data || [];
  const activeSubscription = activeSubscriptionData?.data;

  const handleSubscribe = async (plan) => {
    setSelectedPlan(plan);
    setIsSubscribing(true);

    try {
      const subscriptionData = {
        subscription_id: plan.id,
        payment_method: paymentMethod,
        auto_renew: autoRenew
      };

      const response = await mutateApi({
        endpoint: subscriptionEndpoints.subscribe(),
        method: 'POST',
        data: subscriptionData
      }).unwrap();

      if (response.success) {
        toast.success('Successfully subscribed to plan!');
        router.push('/subscriptions');
      }
    } catch (error) {
      console.error('Subscription error:', error);
      toast.error(error?.data?.message || 'Failed to subscribe to plan');
    } finally {
      setIsSubscribing(false);
      setSelectedPlan(null);
    }
  };

  const isCurrentPlan = (planId) => {
    return activeSubscription?.subscription?.id === planId;
  };

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Subscription Plans</h1>
          <p className="text-gray-600">
            Choose the perfect plan for your  needs. Upgrade or downgrade anytime.
          </p>
        </div>

        {/* Current Subscription Alert */}
        {activeSubscription && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <div>
                <h4 className="text-sm font-medium text-blue-800">Current Subscription</h4>
                <p className="text-sm text-blue-700 mt-1">
                  {"You're currently subscribed to"} <strong>{activeSubscription.subscription?.name}</strong>. 
                  Your subscription is active until {new Date(activeSubscription.end_date).toLocaleDateString()}.
                </p>
              </div>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading subscription plans...</p>
          </div>
        ) : plans.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`relative bg-white rounded-lg shadow-sm border-2 transition-all duration-200 ${
                  plan.is_featured 
                    ? 'border-purple-300 ring-2 ring-purple-100' 
                    : 'border-gray-200 hover:border-gray-300'
                } ${isCurrentPlan(plan.id) ? 'ring-2 ring-blue-200 border-blue-300' : ''}`}
              >
                {/* Featured Badge */}
                {plan.is_featured && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-purple-600 text-white text-xs font-medium px-3 py-1 rounded-full">
                      Most Popular
                    </span>
                  </div>
                )}

                {/* Current Plan Badge */}
                {isCurrentPlan(plan.id) && (
                  <div className="absolute -top-3 right-4">
                    <span className="bg-blue-600 text-white text-xs font-medium px-3 py-1 rounded-full">
                      Current Plan
                    </span>
                  </div>
                )}

                <div className="p-6">
                  {/* Plan Header */}
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{plan.name}</h3>
                    <p className="text-gray-600 text-sm mb-4">{plan.description}</p>
                    <div className="mb-4">
                      <span className="text-4xl font-bold text-gray-900">${plan.price}</span>
                      <span className="text-gray-600 text-sm">/{plan.duration_days} days</span>
                    </div>
                  </div>

                  {/* Plan Features */}
                  <div className="space-y-3 mb-6">
                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                      <span className="text-gray-600">Max Requests</span>
                      <span className="font-semibold text-gray-900">{plan.max_requests}</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                      <span className="text-gray-600">Max Offers</span>
                      <span className="font-semibold text-gray-900">{plan.max_offers}</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                      <span className="text-gray-600">Max Orders</span>
                      <span className="font-semibold text-gray-900">{plan.max_orders}</span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-600">User Type</span>
                      <span className="font-semibold text-gray-900 capitalize">{plan.user_type}</span>
                    </div>
                  </div>

                  {/* Action Button */}
                  <div className="text-center">
                    {isCurrentPlan(plan.id) ? (
                      <button
                        disabled
                        className="w-full bg-gray-100 text-gray-500 py-3 px-4 rounded-lg font-medium cursor-not-allowed"
                      >
                        Current Plan
                      </button>
                    ) : (
                      <button
                        onClick={() => handleSubscribe(plan)}
                        disabled={isSubscribing && selectedPlan?.id === plan.id}
                        className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                          plan.is_featured
                            ? 'bg-purple-600 hover:bg-purple-700 text-white'
                            : 'bg-blue-600 hover:bg-blue-700 text-white'
                        } disabled:opacity-50 disabled:cursor-not-allowed`}
                      >
                        {isSubscribing && selectedPlan?.id === plan.id 
                          ? 'Subscribing...' 
                          : activeSubscription 
                            ? 'Switch to This Plan' 
                            : 'Subscribe Now'
                        }
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Plans Available</h3>
              <p className="text-gray-600">
                No subscription plans are currently available. Please check back later.
              </p>
            </div>
          </div>
        )}

        {/* Subscription Options */}
        {!activeSubscription && (
          <div className="mt-12 bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Subscription Preferences</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Payment Method
                </label>
                <select
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="credit_card">Credit Card</option>
                  <option value="debit_card">Debit Card</option>
                  <option value="paypal">PayPal</option>
                  <option value="bank_transfer">Bank Transfer</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoRenewPreference"
                  checked={autoRenew}
                  onChange={(e) => setAutoRenew(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="autoRenewPreference" className="ml-2 text-sm text-gray-700">
                  Enable auto-renewal by default
                </label>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default SubscriptionPlans;
