import React, { useState, useRef } from "react";

const ImageUpload = ({
  className,
  label = null,
  required = false,
  handleChange,
  maxImages = 15
}) => {
  const [images, setImages] = useState([]);
  const [previews, setPreviews] = useState([]);
  const fileInputRef = useRef(null);

  // Handle file input change
  const handleFileChange = (e) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const filesArray = Array.from(files); // Convert FileList to Array

      // Check if adding these files would exceed the maximum
      const remainingSlots = maxImages - images.length;

      if (remainingSlots <= 0) {
        alert(`Maximum of ${maxImages} images allowed.`);
        e.target.value = null;
        return;
      }

      // Take only as many files as we have slots for
      const filesToAdd = filesArray.slice(0, remainingSlots);
      const newImages = [...images, ...filesToAdd];

      setImages(newImages);

      // Generate previews for each file
      const newPreviews = filesToAdd.map((file) => {
        return URL.createObjectURL(file); // Create a URL for the file
      });
      setPreviews([...previews, ...newPreviews]);

      // Notify parent component
      handleChange({
        target: {
          files: newImages
        }
      });

      // Reset the file input value to allow selecting the same file again
      e.target.value = null;
    }
  };



  // Remove an image
  const removeImage = (index) => {
    const newImages = [...images];
    const newPreviews = [...previews];

    // Revoke the object URL to avoid memory leaks
    URL.revokeObjectURL(newPreviews[index]);

    newImages.splice(index, 1); // Remove the image
    newPreviews.splice(index, 1); // Remove the preview

    setImages(newImages);
    setPreviews(newPreviews);

    // Notify parent component
    handleChange({
      target: {
        files: newImages.length > 0 ? newImages : null
      }
    });
  };

  return (
    <div className={`${className}`}>
      {label && (
        <label className="text-sm archivo font-medium mb-2 text-[#374151] inline-block">
          <p className="text-sm archivo font-medium text-[#374151] inline-block">
            {label}
            {required && <span className="text-red-500"> *</span>}
          </p>{" "}
        </label>
      )}

      {/* Image upload container with fixed height */}
      <div className="w-full h-30 border border-gray-300 rounded-lg p-2 bg-white">
        {/* Show image count and clear button if there are images */}
        {previews.length > 0 && (
          <div className="flex justify-between items-center mb-2">
            <p className="text-xs text-gray-500">
              {previews.length} of {maxImages} images
              {previews.length > 5 && (
                <span className="ml-1 text-xs text-blue-500">
                  (scroll to see all)
                </span>
              )}
            </p>
            <div className="flex items-center space-x-2">
              {previews.length > 1 && (
                <button
                  type="button"
                  onClick={() => {
                    // Sort images alphabetically by filename
                    const sortedIndices = [...Array(images.length).keys()].sort((a, b) => {
                      return images[a].name.localeCompare(images[b].name);
                    });

                    const sortedImages = sortedIndices.map(i => images[i]);
                    const sortedPreviews = sortedIndices.map(i => previews[i]);

                    setImages(sortedImages);
                    setPreviews(sortedPreviews);

                    handleChange({
                      target: {
                        files: sortedImages
                      }
                    });
                  }}
                  className="text-xs text-gray-500 hover:text-gray-700"
                >
                  Sort
                </button>
              )}
              <button
                type="button"
                onClick={() => {
                  // Clear all images
                  previews.forEach(preview => URL.revokeObjectURL(preview));
                  setImages([]);
                  setPreviews([]);
                  handleChange({ target: { files: null } });
                }}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                Clear all
              </button>
            </div>
          </div>
        )}

        {/* Empty state - show full upload area */}
        {previews.length === 0 ? (
          <div
            className="flex flex-col items-center justify-center h-24 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
            onClick={() => fileInputRef.current.click()}
          >
            <svg
              className="w-8 h-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="mt-1 text-sm text-gray-500">Add Photos</p>
          </div>
        ) : (
          /* Image grid with previews and add more button */
          <div className="flex gap-2 h-24 overflow-x-auto pb-1 pr-1">
            {/* Image previews */}
            {previews.map((preview, index) => (
              <div key={index} className="relative group h-full aspect-square flex-shrink-0">
                <img
                  src={preview}
                  alt={`Preview ${index}`}
                  className="h-full w-full object-cover rounded-lg border border-gray-200"
                />
                
                <button
                  onClick={() => removeImage(index)}
                  className="absolute top-1 right-1 bg-black bg-opacity-60 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  ×
                </button>
                <div className="absolute bottom-1 left-1 bg-black bg-opacity-40 text-white text-xs px-1.5 py-0.5 rounded-md opacity-0 group-hover:opacity-100 transition-opacity">
                  {index + 1}
                </div>
              </div>
            ))}

            {/* Add more button - always visible within the grid */}
            {previews.length < maxImages && (
              <div
                onClick={() => fileInputRef.current.click()}
                className="h-full aspect-square flex-shrink-0 flex flex-col items-center justify-center border border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
              >
                <svg
                  className="w-6 h-6 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                <span className="text-xs text-gray-500 mt-1">Add</span>
              </div>
            )}

          </div>
        )}
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

export default ImageUpload;
