"use client";

import React, { useState, useEffect } from "react";
import BuyerDashboard from "./BuyerDashboard";
import SellerDashboard from "./SellerDashboard";
import { getUserRole } from "@/utils/user";

const Dashboard = () => {
  const [userRole, setUserRole] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const role = getUserRole();
    setUserRole(role);
    setIsLoading(false);
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Render the appropriate dashboard based on user role
  if (userRole === "Seller") {
    return <SellerDashboard />;
  } else {
    // Default to buyer dashboard for any other role
    return <BuyerDashboard />;
  }
};

export default Dashboard;
