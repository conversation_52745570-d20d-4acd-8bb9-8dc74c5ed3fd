"use client";

import React from 'react';
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import { useFetchApiQuery } from '@/redux/services/api';
import { subscriptionEndpoints, calculateUsagePercentage, getUsageColorClass } from '@/services/subscriptionService';
import { getUserRole } from '@/utils/user';

const SubscriptionUsage = () => {
  const userRole = getUserRole();

  // Fetch usage status
  const { data: usageData, isLoading, error, refetch } = useFetchApiQuery({
    endpoint: subscriptionEndpoints.getUsageStatus(),
    skip: false
  });

  const usage = usageData?.data;

  const UsageCard = ({ title, used, limit, canUse, icon, description }) => {
    const percentage = calculateUsagePercentage(used, limit);
    const colorClass = getUsageColorClass(percentage);
    const remaining = limit - used;

    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              {icon}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
              <p className="text-gray-600 text-sm">{description}</p>
            </div>
          </div>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            canUse ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {canUse ? 'Available' : 'Limit Reached'}
          </span>
        </div>

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Usage</span>
            <span className="text-sm font-medium text-gray-900">
              {used} / {limit}
            </span>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-300 ${colorClass}`}
              style={{ width: `${percentage}%` }}
            />
          </div>

          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">
              {remaining} remaining
            </span>
            <span className={`text-sm font-medium ${
              percentage >= 90 ? 'text-red-600' : 
              percentage >= 75 ? 'text-yellow-600' : 'text-green-600'
            }`}>
              {percentage}% used
            </span>
          </div>

          {percentage >= 90 && (
            <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-800 text-sm font-medium">
                ⚠️ You're approaching your {title.toLowerCase()} limit
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading usage information...</p>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <svg className="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Usage</h3>
              <p className="text-gray-600 mb-4">
                Unable to load your usage information. Please try again.
              </p>
              <button
                onClick={() => refetch()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (!usage || !usage.hasActiveSubscription) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Active Subscription</h3>
              <p className="text-gray-600 mb-4">
                You need an active subscription to view usage information.
              </p>
              <a
                href="/subscription-plans"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors inline-block"
              >
                View Plans
              </a>
            </div>
          </div>
        </div>
      </>
    );
  }

  const { subscription, subscriptionDetails, usage: usageStats } = usage;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysRemaining = () => {
    if (!subscriptionDetails.end_date) return null;
    const endDate = new Date(subscriptionDetails.end_date);
    const today = new Date();
    const diffTime = endDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  const daysRemaining = getDaysRemaining();

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Usage & Limits</h1>
          <p className="text-gray-600">
            Track your current usage against your {subscription.name} plan limits
          </p>
        </div>

        {/* Subscription Overview */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-700">Current Plan</h3>
              <p className="text-lg font-semibold text-gray-900 mt-1">{subscription.name}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700">Status</h3>
              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${
                subscriptionDetails.status === 'ACTIVE' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {subscriptionDetails.status}
              </span>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700">End Date</h3>
              <p className="text-lg font-semibold text-gray-900 mt-1">
                {formatDate(subscriptionDetails.end_date)}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700">Days Remaining</h3>
              <p className={`text-lg font-semibold mt-1 ${
                daysRemaining <= 7 ? 'text-red-600' : 'text-gray-900'
              }`}>
                {daysRemaining} days
              </p>
            </div>
          </div>
        </div>

        {/* Usage Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {usageStats.requests && (
            <UsageCard
              title="Requests"
              used={usageStats.requests.used}
              limit={usageStats.requests.limit}
              canUse={usageStats.requests.canUse}
              description="Product/service requests you can create"
              icon={
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              }
            />
          )}

          {usageStats.offers && (
            <UsageCard
              title="Offers"
              used={usageStats.offers.used}
              limit={usageStats.offers.limit}
              canUse={usageStats.offers.canUse}
              description="Offers you can create or receive"
              icon={
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              }
            />
          )}

          {usageStats.orders && (
            <UsageCard
              title="Orders"
              used={usageStats.orders.used}
              limit={usageStats.orders.limit}
              canUse={usageStats.orders.canUse}
              description="Orders you can place or fulfill"
              icon={
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              }
            />
          )}
        </div>

        {/* Upgrade Prompt */}
        {(usageStats.requests?.remaining <= 5 || usageStats.offers?.remaining <= 5 || usageStats.orders?.remaining <= 5) && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div className="flex items-start">
              <svg className="w-6 h-6 text-yellow-600 mt-1 mr-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">Approaching Plan Limits</h3>
                <p className="text-yellow-700 mb-4">
                  You're running low on some of your plan limits. Consider upgrading to a higher plan to avoid service interruption.
                </p>
                <a
                  href="/subscription-plans"
                  className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors inline-block"
                >
                  Upgrade Plan
                </a>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default SubscriptionUsage;
