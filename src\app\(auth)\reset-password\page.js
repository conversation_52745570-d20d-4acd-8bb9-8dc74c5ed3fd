
"use client";
export const dynamic = "force-dynamic";

import React, { useState, useEffect, Suspense } from "react";
import { useMutateApiMutation } from "../../../redux/services/api";
import GoBack from "@/components/backend/GoBack";
import Image from "next/image";
import Button from "@/components/backend/Button";
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';

// Client component that uses useSearchParams
const ResetPasswordForm = () => {
  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: "",
    token: ""
  });
  const [errors, setErrors] = useState({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [tokenValid, setTokenValid] = useState(true);

  const router = useRouter();
  const searchParams = useSearchParams();

  // Get token from URL query parameters
  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      setFormData(prev => ({ ...prev, token }));
    } else {
      setTokenValid(false);
      setErrors({ server: "Invalid or missing password reset token" });
    }
  }, [searchParams]);

  const [mutateApi, { data: mutationData, error: mutationError }] = useMutateApiMutation();

  useEffect(() => {
    if (mutationData) {
      setIsSubmitted(true);
      setIsLoading(false);
      // Clear form after successful submission
      setFormData({ password: "", confirmPassword: "", token: "" });
    }
    if (mutationError) {
      console.error("Mutation API Error:", mutationError);
      setIsLoading(false);
      setErrors({
        server: mutationError.data?.message ||
               "Failed to reset password. The link may have expired or is invalid."
      });
      setTokenValid(false);
    }
  }, [mutationData, mutationError]);

  const validateForm = () => {
    let newErrors = {};

    if (!formData.token) {
      newErrors.token = "Reset token is missing";
      setTokenValid(false);
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else {
      const password = formData.password;
      const passwordErrors = [];

      // Check minimum length (8 characters)
      if (password.length < 8) {
        passwordErrors.push("at least 8 characters");
      }

      // Check for lowercase letter
      if (!/[a-z]/.test(password)) {
        passwordErrors.push("1 lowercase letter");
      }

      // Check for uppercase letter
      if (!/[A-Z]/.test(password)) {
        passwordErrors.push("1 uppercase letter");
      }

      // Check for number
      if (!/[0-9]/.test(password)) {
        passwordErrors.push("1 number (0-9)");
      }

      // Check for special character
      if (!/[!@#$%^&*]/.test(password)) {
        passwordErrors.push("1 special character (!@#$%^&*)");
      }

      if (passwordErrors.length > 0) {
        newErrors.password = `Password must contain ${passwordErrors.join(", ")}`;
      }
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.confirmPassword !== formData.password) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      setIsLoading(true);
      mutateApi({
        endpoint: "/auth/reset-password",
        data: {
          token: formData.token,
          newPassword: formData.password
        }
      });
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    // Clear error when user starts typing
    if (errors[e.target.name]) {
      setErrors({ ...errors, [e.target.name]: null });
    }
  };

  if (!tokenValid) {
    return (
      <div className="flex min-h-screen">
        <div className="w-full lg:w-1/2 p-8 flex flex-col">
          <div className="max-w-md mx-auto w-full">
            <div className="mb-16">
              <GoBack />
            </div>

            <div className="mb-8">
              <Image
                src="/assets/backend_assets/images/site-logo.svg"
                alt="logo"
                width={100}
                height={100}
                className="w-[200px]"
              />
            </div>

            <div className="text-center">
              <div className="mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-16 w-16 text-red-500 mx-auto"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h2 className="text-3xl inter font-bold text-gray-800 mb-4">
                Invalid Reset Link
              </h2>
              <p className="text-gray-600 mb-8 poppins">
                {errors.server || "This password reset link is invalid or has expired."}
              </p>
              <Button
                onClick={() => router.replace('/forgot-password')}
                className="w-full bg-blue-500 text-white py-3 rounded-md font-semibold transition text-xl"
              >
                Request New Reset Link
              </Button>
            </div>
          </div>
        </div>

        <div className="hidden lg:block lg:w-1/2 relative overflow-hidden">
          <Image
            src="/assets/login-img.png"
            alt="Login Image"
            height={1000}
            width={1000}
            className="object-contain h-[99vh] w-full"
            priority
          />
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen">
      {/* Left Side - Form */}
      <div className="w-full lg:w-1/2 p-8 flex flex-col">
        <div className="max-w-md mx-auto w-full">
          {/* Back Button */}
          <div className="mb-16">
            <GoBack />
          </div>

          {/* Logo */}
          <div className="mb-8">
            <Image
              src="/assets/backend_assets/images/site-logo.svg"
              alt="logo"
              width={100}
              height={100}
              className="w-[200px]"
            />
          </div>

          {isSubmitted ? (
            <div className="text-center">
              <div className="mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-16 w-16 text-green-500 mx-auto"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h2 className="text-3xl inter font-bold text-gray-800 mb-4">
                Password Reset Successful
              </h2>
              <p className="text-gray-600 mb-8 poppins">
                Your password has been updated successfully. You can now
                <button
                  onClick={() => router.replace('/login')}
                  className="text-blue-500 hover:underline cursor-pointer ml-1"
                >
                  log in
                </button> with your new password.
              </p>
            </div>
          ) : (
            <>
              <h2 className="text-3xl inter font-bold text-gray-800 mb-2">
                Reset Your Password
              </h2>
              <p className="text-gray-600 mt-4 mb-8 poppins">
                Please enter your new password below.
              </p>

              {errors.server && (
                <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
                  {errors.server}
                </div>
              )}

              <form onSubmit={handleSubmit}>
                <div className="mb-6">
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    New Password
                  </label>
                  <div className={`flex items-center border ${errors.password ? 'border-red-500' : 'border-gray-300'} rounded-md`}>
                    <div className="pl-4 pr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-700"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                        />
                      </svg>
                    </div>
                    <input
                      type="password"
                      id="password"
                      name="password"
                      className={`w-full py-3 px-2 text-gray-700 focus:outline-none ${errors.password ? 'placeholder-red-300' : ''}`}
                      placeholder="Enter new password"
                      value={formData.password}
                      onChange={handleChange}
                    />
                  </div>
                  {errors.password && (
                    <p className="text-red-500 text-sm mt-1">{errors.password}</p>
                  )}

                  {/* Password Requirements */}
                  {formData.password && (
                    <div className="mt-2 p-3 bg-gray-50 rounded-md">
                      <p className="text-xs font-medium text-gray-700 mb-2">Password Requirements:</p>
                      <div className="space-y-1">
                        <div className={`flex items-center text-xs ${
                          formData.password.length >= 8 ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          <svg className={`w-3 h-3 mr-2 ${
                            formData.password.length >= 8 ? 'text-green-600' : 'text-gray-400'
                          }`} fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          At least 8 characters
                        </div>
                        <div className={`flex items-center text-xs ${
                          /[a-z]/.test(formData.password) ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          <svg className={`w-3 h-3 mr-2 ${
                            /[a-z]/.test(formData.password) ? 'text-green-600' : 'text-gray-400'
                          }`} fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          1 lowercase letter (a-z)
                        </div>
                        <div className={`flex items-center text-xs ${
                          /[A-Z]/.test(formData.password) ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          <svg className={`w-3 h-3 mr-2 ${
                            /[A-Z]/.test(formData.password) ? 'text-green-600' : 'text-gray-400'
                          }`} fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          1 uppercase letter (A-Z)
                        </div>
                        <div className={`flex items-center text-xs ${
                          /[0-9]/.test(formData.password) ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          <svg className={`w-3 h-3 mr-2 ${
                            /[0-9]/.test(formData.password) ? 'text-green-600' : 'text-gray-400'
                          }`} fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          1 number (0-9)
                        </div>
                        <div className={`flex items-center text-xs ${
                          /[!@#$%^&*]/.test(formData.password) ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          <svg className={`w-3 h-3 mr-2 ${
                            /[!@#$%^&*]/.test(formData.password) ? 'text-green-600' : 'text-gray-400'
                          }`} fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          1 special character (!@#$%^&*)
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="mb-6">
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Confirm New Password
                  </label>
                  <div className={`flex items-center border ${errors.confirmPassword ? 'border-red-500' : 'border-gray-300'} rounded-md`}>
                    <div className="pl-4 pr-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-700"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                        />
                      </svg>
                    </div>
                    <input
                      type="password"
                      id="confirmPassword"
                      name="confirmPassword"
                      className={`w-full py-3 px-2 text-gray-700 focus:outline-none ${errors.confirmPassword ? 'placeholder-red-300' : ''}`}
                      placeholder="Confirm new password"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                    />
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-red-500 text-sm mt-1">{errors.confirmPassword}</p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full bg-blue-500 text-white py-3 rounded-md font-semibold transition text-xl"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Resetting...
                    </span>
                  ) : (
                    "Reset Password"
                  )}
                </Button>
              </form>
            </>
          )}
        </div>
      </div>

      {/* Right Side - Image */}
      <div className="hidden lg:block lg:w-1/2 relative overflow-hidden">
        <Image
          src="/assets/login-img.png"
          alt="Login Image"
          height={1000}
          width={1000}
          className="object-contain h-[99vh] w-full"
          priority
        />
      </div>
    </div>
  );
};

// Main page component that wraps the form in Suspense
const ResetPassword = () => {
  return (
    <Suspense fallback={<div className="flex min-h-screen justify-center items-center">Loading...</div>}>
      <ResetPasswordForm />
    </Suspense>
  );
};

export default ResetPassword;