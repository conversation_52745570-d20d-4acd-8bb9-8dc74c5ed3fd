{"name": "<PERSON><PERSON><PERSON>-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "node build.js", "build:next": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@reduxjs/toolkit": "^2.6.1", "aos": "^3.0.0-beta.6", "date-fns": "^4.1.0", "formik": "^2.4.6", "next": "15.2.1", "postcss": "^8.5.3", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-google-recaptcha": "^3.1.0", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "swiper": "^11.2.6", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.11", "eslint": "^9", "eslint-config-next": "15.2.1", "tailwindcss": "^4.0.11", "typescript": "^5.8.3"}}