{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "merge": "node scripts/merge-prisma.js", "migrate": "node scripts/migrate-db.js", "test": "echo \"Error: no test specified\" && exit 1", "test:recaptcha": "node scripts/test-recaptcha.js", "test:offers": "node scripts/test-offers-enhancement.js", "test:seller-merged": "node scripts/test-seller-merged-requests.js", "prisma:fix": "PowerShell -ExecutionPolicy Bypass -File scripts/fix-prisma-windows.ps1", "prisma:reset": "npx prisma migrate reset --force", "prisma:generate": "npx prisma generate", "prisma:migrate": "npx prisma migrate dev"}, "keywords": [], "author": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@prisma/client": "^6.5.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.4.7", "express": "^4.21.0", "express-validator": "^7.2.1", "form-data": "^4.0.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.0", "papaparse": "^5.5.3", "uuid": "^11.1.0", "validator": "^13.12.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.4"}}