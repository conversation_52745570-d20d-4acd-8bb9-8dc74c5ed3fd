import React, { useEffect } from "react";
import WorkCard from "./WorkCard";
import "aos/dist/aos.css"; // You can also use <link> for styles
import Aos from "aos";
const WorkSection = () => {
  useEffect(() => {
    Aos.init();
  }, []);
  return (
    <>
      <section className="bg-linear-to-b from-[#FFF9FC] to-[#F4F6FC] overflow-hidden">
        <div
          className="max-w-6xl mx-auto px-4 bg-[#FAF8FF] pb-10 relative"
          data-aos="zoom-in"
          data-aos-offset="200"
          data-aos-delay="50"
          data-aos-duration="1000"
          data-aos-easing="ease-in-out"
        >
          <div className="text-center">
            <h3 className="archivo text-[32px] font-bold text-[#0F0C1D]">
              How It
              <span className="ml-2 text-transparent bg-clip-text bg-linear-to-r from-[#FD2692]   to-[#0A67F2]">
                Works?
              </span>
            </h3>
            <p className="text-[#656B76] archivo font-normal text-base">
              Reliable and High-Quality Services You Can Count On
            </p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 lg:gap-x-20 gap-y-24 mt-10 relative">
            <WorkCard
              imgSrc="/assets/frontend_assets/work-2.svg"
              title="Register to the system"
              content="Become a member of our community with great services"
              buttonText="Sign Up"
            />
            <svg
              width={342}
              height={24}
              className="absolute hidden lg:block top-[15%] left-1/2 -translate-x-1/2 -translate-y-1/2"
              viewBox="0 0 342 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx={8} cy={12} r={8} fill="#408CFF" />
              <line
                x1={16}
                y1="12.5"
                x2={326}
                y2="12.5"
                stroke="#408CFF"
                strokeDasharray="6 6"
              />
              <path
                d="M326.204 20.2949C326.099 20.1904 326.016 20.0662 325.959 19.9295C325.903 19.7927 325.873 19.6461 325.873 19.498C325.873 19.35 325.903 19.2034 325.959 19.0666C326.016 18.9299 326.099 18.8057 326.204 18.7012L332.906 11.999L326.204 5.29492C325.993 5.08358 325.874 4.79693 325.874 4.49805C325.874 4.19916 325.993 3.91251 326.204 3.70117C326.416 3.48983 326.702 3.37109 327.001 3.37109C327.3 3.37109 327.587 3.48983 327.798 3.70117L335.298 11.2012C335.403 11.3057 335.486 11.4299 335.543 11.5666C335.6 11.7034 335.629 11.85 335.629 11.998C335.629 12.1461 335.6 12.2927 335.543 12.4295C335.486 12.5662 335.403 12.6904 335.298 12.7949L327.798 20.2949C327.694 20.3998 327.569 20.483 327.433 20.5398C327.296 20.5966 327.149 20.6258 327.001 20.6258C326.853 20.6258 326.706 20.5966 326.57 20.5398C326.433 20.483 326.309 20.3998 326.204 20.2949Z"
                fill="#0A67F2"
              />
            </svg>

            <WorkCard
              imgSrc="/assets/frontend_assets/work-1.svg"
              title="Post A Request."
              content="Save time for searching for products."
              buttonText="Post Request"
            />
            <WorkCard
              imgSrc="/assets/frontend_assets/work-3.svg"
              title="Purchase & Save money!"
              content="Purchase & enjoy your desired product less than a time!"
              buttonText="Post a review"
            />
            <svg
              width={24}
              height={79}
              className="absolute hidden lg:block top-1/2 right-[20%] -translate-x-1/2 -translate-y-1/2"
              viewBox="0 0 24 79"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <line
                x1="11.5"
                y1={55}
                x2="11.5"
                y2={20}
                stroke="#408CFF"
                strokeDasharray="6 6"
              />
              <path
                d="M20.2949 63.2043C20.1904 63.0994 20.0662 63.0162 19.9295 62.9594C19.7927 62.9026 19.6461 62.8734 19.498 62.8734C19.35 62.8734 19.2034 62.9026 19.0666 62.9594C18.9299 63.0162 18.8057 63.0994 18.7012 63.2043L11.999 69.9065L5.29492 63.2043C5.08358 62.9929 4.79693 62.8742 4.49805 62.8742C4.19916 62.8742 3.91251 62.9929 3.70117 63.2043C3.48983 63.4156 3.37109 63.7023 3.37109 64.0011C3.37109 64.3 3.48983 64.5867 3.70117 64.798L11.2012 72.298C11.3057 72.4029 11.4299 72.4861 11.5666 72.5429C11.7034 72.5997 11.85 72.6289 11.998 72.6289C12.1461 72.6289 12.2927 72.5997 12.4295 72.5429C12.5662 72.4861 12.6904 72.4029 12.7949 72.298L20.2949 64.798C20.3998 64.6935 20.483 64.5693 20.5398 64.4326C20.5966 64.2958 20.6258 64.1492 20.6258 64.0011C20.6258 63.8531 20.5966 63.7065 20.5398 63.5697C20.483 63.433 20.3998 63.3088 20.2949 63.2043Z"
                fill="#0A67F2"
              />
              <circle cx={12} cy={8} r={8} fill="#408CFF" />
            </svg>

            <WorkCard
              imgSrc="/assets/frontend_assets/work-4.svg"
              title="Get Best Offers."
              content="Save time for compare with others."
              buttonText="Explore Best Offers"
            />
            <svg
              width={240}
              height={24}
              className="absolute hidden lg:block top-[70%] right-[30%] -translate-x-1/2 -translate-y-1/2"
              viewBox="0 0 240 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15.7957 20.2988C15.9006 20.1943 15.9838 20.0701 16.0406 19.9334C16.0974 19.7966 16.1266 19.65 16.1266 19.502C16.1266 19.3539 16.0974 19.2073 16.0406 19.0705C15.9838 18.9338 15.9006 18.8096 15.7957 18.7051L9.09354 12.0029L15.7957 5.29883C16.0071 5.08748 16.1258 4.80084 16.1258 4.50195C16.1258 4.20307 16.0071 3.91642 15.7957 3.70508C15.5844 3.49373 15.2977 3.375 14.9989 3.375C14.7 3.375 14.4133 3.49373 14.202 3.70508L6.70198 11.2051C6.5971 11.3096 6.51388 11.4338 6.4571 11.5705C6.40032 11.7073 6.37109 11.8539 6.37109 12.002C6.37109 12.15 6.40032 12.2966 6.4571 12.4334C6.51388 12.5701 6.5971 12.6943 6.70198 12.7988L14.202 20.2988C14.3065 20.4037 14.4307 20.4869 14.5674 20.5437C14.7042 20.6005 14.8508 20.6297 14.9989 20.6297C15.1469 20.6297 15.2935 20.6005 15.4303 20.5437C15.567 20.4869 15.6912 20.4037 15.7957 20.2988Z"
                fill="#0A67F2"
              />
              <line
                x1={240}
                y1="11.5"
                x2={18}
                y2="11.5"
                stroke="#408CFF"
                strokeDasharray="6 6"
              />
              <circle cx={232} cy={12} r={8} fill="#408CFF" />
            </svg>
          </div>
        </div>
      </section>
    </>
  );
};

export default WorkSection;
