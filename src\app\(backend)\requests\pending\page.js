"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function PendingRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the combined list page with Pending status pre-selected
    router.push("/requests/list?status=Pending");
  }, [router]);

  return (
    <div className="flex justify-center items-center h-64">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
  );
}