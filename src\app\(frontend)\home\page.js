"use client";
import BlogSection from "@/components/frontend/BlogSection";
import CategorySection from "@/components/frontend/CategorySection";
import ClientSlider from "@/components/frontend/ClientSlider";
import FeaturedSlider from "@/components/frontend/FeaturedSlider";
import Footer from "@/components/frontend/Footer";
import ImageSlicerSlider from "@/components/frontend/ImageSlicerSlider";
import Navbar from "@/components/frontend/Navbar";
import PlaceFormSection from "@/components/frontend/PlaceFormSection";
import PopularSlider from "@/components/frontend/PopularSlider";
import WorkSection from "@/components/frontend/WorkSection";
import Link from "next/link";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import DeliveryOptions from "@/components/frontend/DeliveryOptions";

const Home = () => {
  const sectionRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  useEffect(() => {
    const section = sectionRef.current;
    if (!section) return;

    const observer = new IntersectionObserver(
      ([entry]) => setIsVisible(entry.isIntersecting),
      { threshold: 0.5 }
    );

    observer.observe(section); // Start observing

    return () => {
      observer.unobserve(section); // Cleanup
    };
  }, []);

  return (
    <>
      <Navbar />
      <ImageSlicerSlider />
      <section
        className="bg-linear-to-t from-[#FFF9FC] to-[#F4F6FC] py-10 h-screen overflow-hidden"
        ref={sectionRef}
      >
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center">
            <h3 className="archivo text-xl lg:text-[32px] font-bold text-[#0F0C1D]">
              Why Choose
              <span className="text-transparent ml-2 bg-clip-text bg-linear-to-r  from-[#FD2692] to-[#0A67F2]">
                Us?
              </span>
            </h3>
            <p className="text-[#656B76] archivo font-normal text-sm lg:text-base">
              Reliable and High-Quality Services You Can Count On
            </p>
          </div>
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-5 lg:gap-16 lg:py-10 py-5">
            <div className="col-span-2">
              <div className="relative">
                <Image
                  src="/assets/frontend_assets/choose.png"
                  alt="Why Choose Us"
                  width={1000}
                  height={1000}
                  className="w-full h-auto z-40"
                  priority
                />
                <img
                  src="/assets/frontend_assets/about-shape.svg"
                  alt=""
                  className="absolute bottom-[10%] right-0 z-10 bouncing__img"
                />
              </div>
            </div>
            <div className="col-span-3">
              <h2 className="outfit font-semibold text-xl lg:text-5xl tracking-[-0.48px] text-[#0F0C1D] ">
                We implement our ideas from Inception to completion.
              </h2>
              <p className="text-[#336AEA] font-bold text-lg lg:text-xl dm_sans mt-2 lg:mt-5">
                The professional approach to technology for the clients.
              </p>
              <p className="dm_sans text-sm lg:text-base font-normal lg:leading-[30px] text-[#6A6F78] my-2 lg:my-5">
                System is a term used to refer to an organized collection
                symbols and processes that may be used to operate on such
                symbols. Perspiciatis omnis natus error voupems accusa
              </p>
              <div className="max-w-xl">
                <div className="grid grid-cols-2 gap-4">
                  <div className="">
                    <p className="text-[#222429] dm_sans flex items-start lg:items-center font-bold text-base lg:text-xl tracking-[1px]">
                      <svg
                        width={21}
                        height={22}
                        className="mr-1"
                        viewBox="0 0 21 22"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10.5 0.328125C12.4133 0.328125 14.175 0.794792 15.785 1.72812C17.395 2.66146 18.6667 3.93312 19.6 5.54313C20.5333 7.15312 21 8.91479 21 10.8281C21 12.7415 20.5333 14.5031 19.6 16.1131C18.6667 17.7231 17.395 18.9948 15.785 19.9281C14.175 20.8615 12.4133 21.3281 10.5 21.3281C8.58667 21.3281 6.825 20.8615 5.215 19.9281C3.605 18.9948 2.33333 17.7231 1.4 16.1131C0.466667 14.5031 0 12.7415 0 10.8281C0 8.91479 0.466667 7.15312 1.4 5.54313C2.33333 3.93312 3.605 2.66146 5.215 1.72812C6.825 0.794792 8.58667 0.328125 10.5 0.328125ZM5.88 10.1981C5.69333 10.1981 5.54167 10.2565 5.425 10.3731C5.30833 10.4898 5.25 10.6415 5.25 10.8281C5.25 11.0148 5.30833 11.1665 5.425 11.2831C5.54167 11.3998 5.69333 11.4581 5.88 11.4581H13.51L10.71 14.3281C10.57 14.4215 10.5 14.5615 10.5 14.7481C10.5 14.9348 10.5583 15.0981 10.675 15.2381C10.7917 15.3781 10.9433 15.4481 11.13 15.4481C11.3167 15.4481 11.48 15.3781 11.62 15.2381L15.54 11.3181C15.68 11.1781 15.75 11.0148 15.75 10.8281C15.75 10.6415 15.68 10.4781 15.54 10.3381L11.62 6.41813C11.4333 6.23146 11.2233 6.17313 10.99 6.24312C10.7567 6.31312 10.605 6.47646 10.535 6.73312C10.465 6.98979 10.5233 7.18812 10.71 7.32812L13.51 10.1981H5.88Z"
                          fill="#336AEA"
                        />
                      </svg>
                      Internal Networking
                    </p>
                    <p className="dm_sans text-sm lg:text-base font-normal lg:leading-[30px] text-[#6A6F78] mt-2 lg:mt-3">
                      Lorem ipsum dolor sited amet consectetur notted
                    </p>
                  </div>
                  <div className="">
                    <p className="text-[#222429] dm_sans flex items-start lg:items-center font-bold text-base lg:text-xl tracking-[1px]">
                      <svg
                        width={21}
                        height={22}
                        className="mr-1"
                        viewBox="0 0 21 22"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10.5 0.328125C12.4133 0.328125 14.175 0.794792 15.785 1.72812C17.395 2.66146 18.6667 3.93312 19.6 5.54313C20.5333 7.15312 21 8.91479 21 10.8281C21 12.7415 20.5333 14.5031 19.6 16.1131C18.6667 17.7231 17.395 18.9948 15.785 19.9281C14.175 20.8615 12.4133 21.3281 10.5 21.3281C8.58667 21.3281 6.825 20.8615 5.215 19.9281C3.605 18.9948 2.33333 17.7231 1.4 16.1131C0.466667 14.5031 0 12.7415 0 10.8281C0 8.91479 0.466667 7.15312 1.4 5.54313C2.33333 3.93312 3.605 2.66146 5.215 1.72812C6.825 0.794792 8.58667 0.328125 10.5 0.328125ZM5.88 10.1981C5.69333 10.1981 5.54167 10.2565 5.425 10.3731C5.30833 10.4898 5.25 10.6415 5.25 10.8281C5.25 11.0148 5.30833 11.1665 5.425 11.2831C5.54167 11.3998 5.69333 11.4581 5.88 11.4581H13.51L10.71 14.3281C10.57 14.4215 10.5 14.5615 10.5 14.7481C10.5 14.9348 10.5583 15.0981 10.675 15.2381C10.7917 15.3781 10.9433 15.4481 11.13 15.4481C11.3167 15.4481 11.48 15.3781 11.62 15.2381L15.54 11.3181C15.68 11.1781 15.75 11.0148 15.75 10.8281C15.75 10.6415 15.68 10.4781 15.54 10.3381L11.62 6.41813C11.4333 6.23146 11.2233 6.17313 10.99 6.24312C10.7567 6.31312 10.605 6.47646 10.535 6.73312C10.465 6.98979 10.5233 7.18812 10.71 7.32812L13.51 10.1981H5.88Z"
                          fill="#336AEA"
                        />
                      </svg>
                      Cloud Based{" "}
                    </p>
                    <p className="dm_sans text-sm lg:text-base font-normal lg:leading-[30px] text-[#6A6F78] mt-2 lg:mt-3">
                      Lorem ipsum dolor sited amet consectetur notted
                    </p>
                  </div>
                </div>

                <div className="mt-3 lg:mt-5">
                  <div className="flex justify-between mb-2 lg:mb-4">
                    <span className="text-xl tracking-[-0.2px] font-bold text-[#0F0C1D] dm_sans">
                      Satisfied Clients
                    </span>
                    <span className="text-base dm_sans font-bold text-[#6A6F78]">
                      45%
                    </span>
                  </div>
                  <div className="w-full bg-[#ECECED] rounded-full h-[15px] progress__bar">
                    <div
                      style={{ width: isVisible ? "45%" : "0%" }}
                      className={`progress__fill`}
                    />
                  </div>
                </div>
                <div className="relative mt-2 lg:mt-4">
                  <svg
                    width={77}
                    height={52}
                    viewBox="0 0 77 52"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlnsXlink="http://www.w3.org/1999/xlink"
                    className="absolute top-0 right-20 bouncing__img"
                  >
                    <rect
                      width={77}
                      height={51}
                      transform="translate(0 0.5)"
                      fill="url(#pattern0_829_13975)"
                    />
                    <defs>
                      <pattern
                        id="pattern0_829_13975"
                        patternContentUnits="objectBoundingBox"
                        width={1}
                        height={1}
                      >
                        <use
                          xlinkHref="#image0_829_13975"
                          transform="scale(0.012987 0.0196078)"
                        />
                      </pattern>
                      <image
                        id="image0_829_13975"
                        width={77}
                        height={51}
                        preserveAspectRatio="none"
                        xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE0AAAAzCAMAAADl/3UzAAAAn1BMVEUAAAALWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdsLWdviMse+AAAANHRSTlMABfX8+Jhv8Z9WTjov0c2NhHlnJN/CuBkR52FAHtqwgEns48m+tKVqRCoNCdTHMycVqpJcQGlqNQAAAmZJREFUSMetlteWqkAQRYsm5yRIUDHnOJ7//7YbZu6dEZqgst+s5dpNl6dKqB1Bl0/2aR0MqQf0FJ+It8vbsg8gvu9Glq4xiIXwnszHbEufJJ6E+ED1jIwWWSip1vcna4BIaLB9tNhkZPSD6xEa1bJbNssO0rF0fIr665jjZtsUfqUyP9T/+s02D1a5NMGGalAGzbaJVGn6GbX3cU/NthPntAVM4hMrzTbb5twHHvFJrWbbgmPbISIuF+ybbasZp6iqxEVB65Am1aLzu8gjWrTtD15WV7hwhxBtY3oVNU5sYHHzIR6ohZWadLRd2Zra2MKv1JYY8vaDuKNWIjYql+YSb+IhUzs7qbxmBHAGJBnM99QBBe5jIceNygixmFMntFLrPAQVmQaFuiGs4T6GN6x+w6OuCDLi6/d/Axx6ZHiCT08QSLPsfxLK2zJTmU5PcYngGJ8PwljyEEgHUUjPoo8x9kdE8s8mJoGNwZ1ewYhF2B8SG351a+svRTiZQC8y3Kzwm/Ro2wMGiE5hvfkesTAUV9M0ucjMA71HLrKQ+mKnQu9NFs7h9yaz0mfm53oxp3fFk9dxFPACwjrJAm2ydAYq/sHGcV49y4UUdHkm/GVmx7IXGOchN43mEXOTuuBAVcebfVP7b4CWUCeSCDN5xtZGzYyYNxF2Tl05TKBulSPYSjmXjILpjQHnqZQJMphBppwCbKH599wKR+F5unGXDFC1nJ6kgPTn/LMyGeAH0sKdvrIjdEn82q57axooRVEo2TR8eduYKTbUHyMbWY+6/UrUe9QJrmRQjwRq3qduOzf71A2jPb3BLwqhOlQ9sbatAAAAAElFTkSuQmCC"
                      />
                    </defs>
                  </svg>
                </div>

                <Link
                  href="/about"
                  className="text-[13px] inline-block w-[200px] dm_sans px-5 py-4 mt-8 text-white font-normal bg-[#336AEA] text-center rounded-[8px]"
                >
                  DISCOVER MORE
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      <WorkSection />

      <PlaceFormSection />
      <section className="overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="text-center">
            <h3 className="archivo text-[32px] font-bold text-[#0F0C1D]">
              Popular
              <span className="ml-2 text-transparent bg-clip-text bg-linear-to-r  from-[#FD2692]   to-[#0A67F2]">
                Requests
              </span>
            </h3>
            <p className="text-[#656B76] archivo font-normal text-base">
              Reliable and High-Quality Services You Can Count On
            </p>
          </div>
          <ul
            id="tabs"
            className="flex overflow-x-auto whitespace-nowrap scrollbar-hide text-sm font-medium text-center text-gray-500 my-10 px-4 md:px-0 md:justify-center"
          >
            <li className="inline-flex me-2">
              <p
                className="inline-block archivo font-semibold text-base px-4 py-2 hover:text-white text-[#242B3A] transition-all duration-300 ease-linear hover:bg-gradient-to-r hover:from-[#FD2692] hover:to-[#0A67F2] border border-[#EBECED] rounded-[5px] cursor-pointer"
                aria-current="page"
              >
                Business & Financial Services
              </p>
            </li>
            <li className="inline-flex me-2">
              <a
                href="#"
                className="inline-block archivo font-semibold text-base px-4 py-2 hover:text-white text-[#242B3A] transition-all duration-300 ease-linear hover:bg-gradient-to-r hover:from-[#FD2692] hover:to-[#0A67F2] border border-[#EBECED] rounded-[5px]"
              >
                Utilities & Home Services
              </a>
            </li>
            <li className="inline-flex me-2">
              <a
                href="#"
                className="inline-block archivo font-semibold text-base px-4 py-2 hover:text-white text-[#242B3A] transition-all duration-300 ease-linear hover:bg-gradient-to-r hover:from-[#FD2692] hover:to-[#0A67F2] border border-[#EBECED] rounded-[5px]"
              >
                Marketing & Advertising
              </a>
            </li>
            <li className="inline-flex me-2">
              <a
                href="#"
                className="inline-block archivo font-semibold text-base px-4 py-2 hover:text-white text-[#242B3A] transition-all duration-300 ease-linear hover:bg-gradient-to-r hover:from-[#FD2692] hover:to-[#0A67F2] border border-[#EBECED] rounded-[5px]"
              >
                IT & Software
              </a>
            </li>
            <li className="inline-flex me-2">
              <a
                href="#"
                className="inline-block archivo font-semibold text-base px-4 py-2 hover:text-white text-[#242B3A] transition-all duration-300 ease-linear hover:bg-gradient-to-r hover:from-[#FD2692] hover:to-[#0A67F2] border border-[#EBECED] rounded-[5px]"
              >
                Education & Training
              </a>
            </li>
            <li className="inline-flex me-2">
              <a
                href="#"
                className="inline-block archivo font-semibold text-base px-4 py-2 hover:text-white text-[#242B3A] transition-all duration-300 ease-linear hover:bg-gradient-to-r hover:from-[#FD2692] hover:to-[#0A67F2] border border-[#EBECED] rounded-[5px]"
              >
                Health & Wellness
              </a>
            </li>
          </ul>

          <PopularSlider />
        </div>
      </section>
      <section className="py-10 overflow-hidden">
        <FeaturedSlider />
      </section>
      <section className="overflow-hidden">
        <CategorySection />
      </section>
      <BlogSection />
      <ClientSlider />
      <DeliveryOptions />
      <Footer />
    </>
  );
};

export default Home;
