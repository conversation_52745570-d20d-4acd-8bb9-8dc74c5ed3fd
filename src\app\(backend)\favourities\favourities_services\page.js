import Breadcrumb from "@/components/backend/Breadcrumb";
import FavouritiesActionButtons from "@/components/backend/FavouritiesActionButtons";
import GoBack from "@/components/backend/GoBack";
import Pagination from "@/components/backend/Pagination";
import SelectInput from "@/components/backend/SelectInput";
import Link from "next/link";

const FavouritiesServices = () => {
  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold capitalize text-[#343A40] text-xl poppins">
          Favourite Service List{" "}
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4">
          <table className="w-full text-sm text-left rtl:text-right text-gray-500 inter">
            <thead className="text-xs text-[#4C596E] capitalize bg-[#E2E8F0]">
              <tr>
                <th scope="col" className="px-3 py-3 font-medium">
                  #
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Product Title{" "}
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Category{" "}
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Sub Category{" "}
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Inventory Status{" "}
                </th>

                <th scope="col" className="px-3 py-3 font-medium">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              <tr className="bg-white border-b  border-gray-200">
                <th
                  scope="row"
                  className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap "
                >
                  1
                </th>
                <th
                  scope="row"
                  className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap "
                >
                  Offer name Offer name{" "}
                </th>
                <th
                  scope="row"
                  className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap "
                >
                  100200300740056
                </th>
                <th
                  scope="row"
                  className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap "
                >
                  Insurance
                </th>
                <th
                  scope="row"
                  className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap "
                >
                  Life Insurance{" "}
                </th>

                <th
                  scope="row"
                  className="px-3 py-4 font-medium inter text-[#4A5568] whitespace-nowrap "
                >
                  <FavouritiesActionButtons />
                </th>
              </tr>
            </tbody>
          </table>
          <Pagination />
        </div>
      </div>
    </>
  );
};

export default FavouritiesServices;
