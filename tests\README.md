# Seller Merged Requests Test

This directory contains tests for the seller merged requests functionality.

## Test Files

### `sellerModel.getRequestDetailsForSeller.test.js`
A comprehensive Jest test suite that tests the `getRequestDetailsForSeller` method with both merged and non-merged requests. This test uses mocking to avoid database dependencies.

**Note**: This test requires Je<PERSON> to be installed and configured.

### `../scripts/test-seller-merged-requests.js`
A standalone test script that tests the actual implementation against a real database. This script:

- Creates test data in the database
- Tests the merged request functionality
- Tests the non-merged request functionality  
- Cleans up test data after completion
- Provides detailed output and verification

## Running the Tests

### Option 1: Standalone Test Script (Recommended)

```bash
# From the backend directory
npm run test:seller-merged

# Or run directly
node scripts/test-seller-merged-requests.js
```

This test will:
1. 🔧 Set up test data (seller, requests, attachments, merged relationships)
2. 🧪 Test merged request functionality
3. 📊 Display detailed results and verification
4. 🧪 Test non-merged request functionality for comparison
5. 🧹 Clean up all test data

### Option 2: Jest Test Suite

```bash
# Install Jest first (if not already installed)
npm install --save-dev jest

# Run the Jest test
npx jest tests/sellerModel.getRequestDetailsForSeller.test.js
```

## What the Tests Verify

### Merged Request Functionality
- ✅ Correctly identifies merged requests (`is_merged: true`)
- ✅ Returns child request details (id, title, form_fields)
- ✅ Combines attachments from parent and all child requests
- ✅ Preserves all existing functionality
- ✅ Handles null/empty custom_fields gracefully
- ✅ Filters out deleted attachments

### Non-Merged Request Functionality
- ✅ Maintains backward compatibility
- ✅ Returns `is_merged: false` for regular requests
- ✅ Returns empty `child_requests` array
- ✅ Returns only the request's own attachments

### Error Handling
- ✅ Throws appropriate errors for unauthorized access
- ✅ Throws appropriate errors for non-existent requests

## Test Data Structure

The test creates:
- **1 Parent Request** with 2 attachments
- **2 Child Requests** with custom form fields and attachments
  - Child 1: 2 attachments, JavaScript/Senior form fields
  - Child 2: 1 attachment, E-commerce/Budget form fields
- **Merged relationships** linking children to parent
- **Seller assignment** to the parent request

## Expected Results

For a merged request, the method should return:
```javascript
{
  // ... existing request fields
  is_merged: true,
  child_requests: [
    {
      id: "child-1-id",
      title: "Child Request 1", 
      form_fields: {
        preferred_language: "JavaScript",
        experience_level: "Senior"
      }
    },
    {
      id: "child-2-id", 
      title: "Child Request 2",
      form_fields: {
        project_type: "E-commerce",
        budget_range: "$5000-$10000"
      }
    }
  ],
  request_attachments: [
    // 2 parent attachments + 2 child1 attachments + 1 child2 attachment = 5 total
  ]
}
```

## Database Requirements

The standalone test requires:
- A working PostgreSQL database connection
- Prisma client configured and connected
- Database schema with all required tables
- Appropriate permissions for creating/deleting test data

## Cleanup

Both tests clean up after themselves:
- The Jest test uses mocks (no database impact)
- The standalone test deletes all created test data

## Troubleshooting

### Database Connection Issues
Ensure your `.env` file has the correct `DATABASE_URL` configured.

### Permission Issues  
Make sure the database user has CREATE, INSERT, UPDATE, DELETE permissions.

### Missing Dependencies
If you see import errors, ensure all dependencies are installed:
```bash
npm install
```

### Test Failures
Check the detailed output - the test provides specific information about what failed and expected vs actual values.
