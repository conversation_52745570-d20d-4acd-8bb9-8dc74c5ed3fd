import Breadcrumb from "@/components/backend/Breadcrumb";
import FavouritiesActionButtons from "@/components/backend/FavouritiesActionButtons";
import GoBack from "@/components/backend/GoBack";
import Pagination from "@/components/backend/Pagination";
import SelectInput from "@/components/backend/SelectInput";
import Image from "next/image";
import Link from "next/link";

const FavouritiesSuppliers = () => {
  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold capitalize text-[#343A40] text-xl poppins">
          Favourite Suppliers List{" "}
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4">
          <table className="w-full text-sm text-left rtl:text-right text-gray-500 inter">
            <thead className="text-xs text-[#4C596E] capitalize bg-[#E2E8F0]">
              <tr>
                <th scope="col" className="px-3 py-3 font-medium">
                  #
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Sellers Name{" "}
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Contact No.{" "}
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Actions{" "}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr className="bg-white border-b  border-gray-200">
                <th
                  scope="row"
                  className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap "
                >
                  1
                </th>
                <th
                  scope="row"
                  className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap "
                >
                  <div className="flex items-center gap-4">
                    <Image
                      className="w-10 h-10 rounded-full"
                      width={100}
                      height={100}
                      src="/assets/backend_assets/images/user-img.png"
                      alt="Jese Leos"
                    />
                    <div className="font-medium">
                      <div className="text-sm inter font-medium text-[#111827]">
                        Jese Leos
                      </div>
                      <div className="text-sm inter font-light text-[#6B7280]">
                        <EMAIL>{" "}
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  scope="row"
                  className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap "
                >
                  +88 01742199077{" "}
                </th>
                <th
                  scope="row"
                  className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap "
                >
                  <Link
                    href="/profile"
                    className="text-[#374151] border border-[#D1D5DB] font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2"
                  >
                    View Profile
                  </Link>
                </th>
              </tr>
            </tbody>
          </table>
          <Pagination />
        </div>
      </div>
    </>
  );
};

export default FavouritiesSuppliers;
