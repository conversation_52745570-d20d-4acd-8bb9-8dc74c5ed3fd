@import url("https://fonts.googleapis.com/css2?family=Archivo:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Outfit:wght@100..900&display=swap");
@import "tailwindcss";

.archivo {
  font-family: "Archivo", sans-serif;
}
.inter {
  font-family: "Inter", sans-serif;
}

.poppins {
  font-family: "Poppins", sans-serif;
}
.outfit {
  font-family: "Outfit", sans-serif;
}
.dm_sans {
  font-family: "DM Sans", sans-serif;
}

.custom-select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg width="18" height="10" viewBox="0 0 18 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.0306 1.53062L9.53055 9.03061C9.4609 9.10035 9.37818 9.15567 9.28713 9.19341C9.19608 9.23115 9.09849 9.25058 8.99993 9.25058C8.90137 9.25058 8.80377 9.23115 8.71272 9.19341C8.62168 9.15567 8.53896 9.10035 8.4693 9.03061L0.969304 1.53062C0.828573 1.38988 0.749512 1.19901 0.749512 0.99999C0.749512 0.800967 0.828573 0.610095 0.969304 0.469364C1.11003 0.328634 1.30091 0.249573 1.49993 0.249573C1.69895 0.249573 1.88982 0.328634 2.03055 0.469364L8.99993 7.43968L15.9693 0.469364C16.039 0.399682 16.1217 0.344406 16.2128 0.306695C16.3038 0.268983 16.4014 0.249573 16.4999 0.249573C16.5985 0.249573 16.6961 0.268983 16.7871 0.306695C16.8781 0.344406 16.9609 0.399682 17.0306 0.469364C17.1002 0.539047 17.1555 0.621773 17.1932 0.712817C17.2309 0.803862 17.2503 0.901444 17.2503 0.99999C17.2503 1.09854 17.2309 1.19612 17.1932 1.28716C17.1555 1.37821 17.1002 1.46093 17.0306 1.53062Z" fill="%236B7280"/></svg>');
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 12px;
}
.new-custom-select {
  background-size: 12px !important;
}

/* Disabled state */
.custom-select:disabled {
  background-color: #e5e7eb;
  cursor: not-allowed;
}
.datepicker input[type="date"] {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
}

#sidebar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

#sidebar::-webkit-scrollbar {
  width: 5px;
  background-color: #f5f5f5;
}

#sidebar::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  background-color: #878585;
}
.swiper-cards.testimonial__swiper .swiper-slide {
  overflow: visible !important;
}
.testimonial__swiper .swiper-3d .swiper-slide-shadow {
  background: transparent !important;
}
.featured__slider .swiper-pagination-bullet {
  position: relative;
}
.featured__slider .swiper-wrapper {
  padding-bottom: 80px;
}
.featured__slider
  .swiper-pagination-bullet.swiper-pagination-bullet-active::before {
  position: absolute;
  content: "";
  left: -6px;
  top: -6px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #0a67f2;
}
.featured__slider .swiper-pagination-bullet {
  margin: 0 10px !important;
}

.testimonials-slider-wrapper {
  border-radius: 20px;
  background: #edf4ff;
  padding: 40px 70px;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  row-gap: 65px;
}

.testimonials-slider-wrapper .slider-area,
.testimonials-slider-wrapper .title-area {
  position: relative;
  z-index: 2;
}

.testimonials-slider-wrapper .title-area {
  width: 100%;
  max-width: 425px;
  align-items: start;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.testimonials-slider-wrapper .slider-area {
  width: 280px;
  flex-grow: 1;
  max-width: 567px;
  position: relative;
}

.testimonials-slider-wrapper .content blockquote {
  border: none !important;
}

/* @media (min-width: 768px) { */
.testimonials-slider-wrapper .slider-area .slider {
  display: flex;
  flex-direction: column;
  padding: 40px 0 20px;
  cursor: pointer;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.testimonials-slider-wrapper .slider-area .slider .secondary {
  order: 1;
}
/* } */

.testimonials-slider-wrapper .slider-area .testimonial-slide-item {
  display: flex;
}

.testimonials-slider-wrapper .slider-area .testimonial-slide-item .thumb {
  margin-right: -34px;
  margin-top: -34px;
  width: 68px;
  height: 68px;
  border: 2px solid #fff;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

.testimonials-slider-wrapper .slider-area .testimonial-slide-item .thumb img {
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
  border-radius: 50%;
}

.testimonials-slider-wrapper .slider-area .testimonial-slide-item .content {
  width: 0;
  flex-grow: 1;
  padding: 24px 30px;
  border-radius: 20px;
  background: var(--white, #fff);
}

.testimonials-slider-wrapper
  .slider-area
  .testimonial-slide-item
  .content
  blockquote {
  margin-bottom: 1.4rem;
  font-size: 1rem;
  font-weight: 500;
  padding: 0;
}

@media (min-width: 768px) {
  .testimonials-slider-wrapper .slider-area .testimonial-slide-item .content {
    width: 90%;
  }
}

@media (max-width: 1199px) and (min-width: 992px) {
  .testimonials-slider-wrapper
    .slider-area
    .testimonial-slide-item
    .content
    blockquote {
    font-size: 0.9rem;
    line-height: 1.5;
  }
}

@media (max-width: 575px) {
  .testimonials-slider-wrapper .slider-area .testimonial-slide-item .content {
    padding: 25px 15px 15px;
  }

  .testimonials-slider-wrapper
    .slider-area
    .testimonial-slide-item
    .content
    blockquote {
    font-size: 0.9rem;
    line-height: 2;
  }
}

/* @media (min-width: 768px) { */
.testimonials-slider-wrapper
  .slider-area
  .testimonial-slide-item:not(.active)
  .content {
  border-radius: 10px;
  border: 2px solid rgb(102 102 102 / 0.08);
  box-shadow: 0 1.8518518209px 3.1481480598px 0 #fff0,
    0 8.1481485367px 6.5185184479px 0 rgb(0 0 0 / 0.01),
    0 20px 13px 0 rgb(0 0 0 / 0.01),
    0 38.5185203552px 25.4814815521px 0 rgb(0 0 0 / 0.01),
    0 64.8148117065px 46.851852417px 0 rgb(0 0 0 / 0.02),
    0 100px 80px 0 rgb(0 0 0 / 0.02);
  background: #fff0;
}

.testimonials-slider-wrapper
  .slider-area
  .testimonial-slide-item:not(.active):not(.secondary) {
  display: none;
}

.testimonials-slider-wrapper .slider-area .testimonial-slide-item.active {
  position: relative;
  z-index: 8;
  margin-right: 60px;
  animation: clientSlide1 0.3s linear forwards;
}

.testimonials-slider-wrapper .slider-area .testimonial-slide-item.secondary {
  position: relative;
  margin-top: -150px;
  opacity: 0.16;
  z-index: -1;
  margin-left: 60px;
  animation: clientSlide2 0.3s linear forwards;
}

.testimonials-slider-wrapper
  .slider-area
  .testimonial-slide-item.secondary
  .content {
  border: 2px solid rgb(102 102 102 / 0.5);
}
/* } */

@media (max-width: 767px) {
  .testimonials-slider-wrapper .slider-area {
    width: 100%;
  }

  .testimonials-slider-wrapper .title-area {
    align-items: center !important;
  }

  .testimonials-slider-wrapper .slider-area .testimonial-slide-item {
    display: flex !important;
    flex-direction: column;
  }

  .testimonials-slider-wrapper .slider-area .testimonial-slide-item .content {
    width: 100%;
    text-align: center;
  }

  .testimonials-slider-wrapper .slider-area .testimonial-slide-item .thumb {
    margin: 0 auto -20px;
  }

  .testimonials-slider-wrapper .slider-area .slider-nav-btns button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }

  .testimonials-slider-wrapper .slider-area .slider-nav-btns button.prev {
    left: -20px;
  }

  .testimonials-slider-wrapper .slider-area .slider-nav-btns button.next {
    right: -20px;
  }

  .testimonials-slider-wrapper
    .slider-area
    .testimonial-slide-item
    .content
    blockquote {
    border-left: none !important;
  }

  .testimonials-slider-wrapper .slider-nav-btns button {
    width: 28px !important;
    height: 28px !important;
    padding: 10px !important;
  }
}

@media (max-width: 1199px) and (min-width: 992px) {
  .testimonials-slider-wrapper .title-area {
    max-width: 325px;
  }

  .testimonials-slider-wrapper .title-area .title {
    font-size: 32px;
  }
}

@media (max-width: 991px) {
  .testimonials-slider-wrapper {
    padding: 30px;
    row-gap: 30px;
  }
}

.testimonials-slider-wrapper .bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

@media (max-width: 767px) {
  .bg-img {
    object-fit: cover;
  }
}

.testimonials-slider-wrapper .title-area .title-badge {
  color: #0a67f2;
  position: relative;
  padding: 4px 12px;
  font-size: 12px;
  line-height: 1.5;
  margin-bottom: 10px;
  display: inline-block;
  background: #d4e6ff;
  border-radius: 5px;
}

/* .testimonials-slider-wrapper .title-area .title-badge::before {
  inset: 0;
  content: "";
  position: absolute;
  background: #d4e6ff;
  opacity: 0.15;
  z-index: -1;
  border-radius: 5px;
} */

.testimonials-slider-wrapper .title-area .title {
  font-size: 24px;
  color: var(--black, #212c4f);
  margin-bottom: 15px;
}

.testimonials-slider-wrapper .title-area p {
  margin-bottom: 20px;
}

@media (min-width: 576px) {
  .title-area .title {
    font-size: 32px;
    margin-bottom: 20px;
  }
}

@media (min-width: 768px) {
  .title-area .title-badge {
    font-size: 14px;
  }

  /* .title-area .title {
    font-size: 36px;
  } */

  .title-area p {
    margin-bottom: 40px;
  }
}

.testimonials-slider-wrapper .slider-nav-btns {
  display: flex;
  gap: 32px;
}

.testimonials-slider-wrapper .slider-nav-btns button {
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: center;
  display: flex;
  align-items: center;
  border-radius: 50%;
  color: var(--black, #79797a);
  background: #fefcfb;
  border: 1px solid #c5c8cb;
  transition: all ease 0.3s;
}

.testimonials-slider-wrapper .slider-nav-btns button.active,
.slider-nav-btns button:hover {
  color: var(--white, #fff);
  background: var(--base, #0a67f2);
  border-color: var(--base, #0a67f2);
}

@keyframes clientSlide1 {
  0% {
    transform: translateY(60px) translateX(60px);
    opacity: 0.16;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes clientSlide2 {
  0% {
    transform: translateY(-60px) translateX(-60px);
    opacity: 1;
  }

  100% {
    opacity: 0.16;
    transform: translateY(0) translateX(0);
  }
}

/* Add these to your existing CSS */
.slider.next .active {
  animation: slideOutLeft 0.3s forwards;
}

.slider.next .secondary {
  animation: slideInRight 0.3s forwards;
}

.slider.prev .active {
  animation: slideOutRight 0.3s forwards;
}

.slider.prev .secondary {
  animation: slideInLeft 0.3s forwards;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 0.16;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 0.16;
  }
}

/* Fade in animation for modals */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
.bouncing__img {
  animation: smoothBounce 3s cubic-bezier(0.445, 0.05, 0.55, 0.95) infinite
    alternate;
}

@keyframes smoothBounce {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10%);
  }
  100% {
    transform: translateY(10%);
  }
}
.progress__bar {
  @apply w-full bg-[#ECECED] rounded-full h-[15px];
}
.progress__fill {
  height: 100%;
  transition: width 1.5s ease-in-out;
}
.progress__fill {
  width: 0%;
  height: 100%;
  background-color: #336aea;
  border-radius: 9999px;
  transition: width 1.5s ease;
}
.progress__fill.fill {
  width: 45%; /* or whatever percentage you want */
}

/* Hide scrollbar but keep functionality */
#tabs.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
#tabs.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
/* Add more elementor styles as needed */
.delivery-options-wrapper {
  --base: #039d55;
  border-radius: 20px;
  background: rgb(170 255 240 / 0.2);
  padding: 50px;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  gap: 25px;
}

.delivery-options-wrapper .left-tab-menu {
  width: 271px;
  display: flex;
  flex-direction: column;
  gap: 25px;
  font-size: 18px;
  font-family: Readex Pro;
  line-height: 17px;
  letter-spacing: 0.15px;
}

.delivery-options-wrapper .left-tab-menu .left-menu-item {
  border-radius: 10px;
  overflow: hidden;
}

.delivery-options-wrapper .left-tab-menu .left-menu-item.active {
  color: #0a67f2;
}

.delivery-options-wrapper .left-tab-menu .left-menu-item span {
  display: block;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 20px 30px 0 rgb(3 157 139 / 0.1);
  padding: 27px 0;
  text-align: center;
  cursor: pointer;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: relative;
  z-index: 10;
}

@media (max-width: 1199px) {
  .delivery-options-wrapper .left-tab-menu {
    font-size: 15px;
    gap: 10px;
  }

  .delivery-options-wrapper .left-tab-menu .left-menu-item span {
    padding-block: 18px;
  }
}

@media (min-width: 992px) {
  .delivery-options-wrapper {
    gap: 50px;
  }
}

.delivery-options-wrapper .right-tab-content {
  width: 0;
  flex-grow: 1;
  /* align-self: center; */
}

.delivery-options-wrapper .right-tab-content .section-title {
  text-align: center;
  margin-bottom: 32px;
}

@media (max-width: 767px) {
  .delivery-options-wrapper .right-tab-content .section-title {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
    letter-spacing: 0.24px;
  }
}

.delivery-options-wrapper .right-tab-content .section-title .title {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 10px;
}

@media (min-width: 576px) {
  .delivery-options-wrapper .right-tab-content .section-title .title {
    font-size: 24px;
    margin-bottom: 15px;
  }
}

@media (min-width: 768px) {
  .delivery-options-wrapper .right-tab-content .section-title .title {
    font-size: 30px;
  }
}

@media (min-width: 1200px) {
  .delivery-options-wrapper .right-tab-content .section-title .title {
    font-size: 32px;
    margin-bottom: 10px;
  }
}

.delivery-options-wrapper .right-tab-content .right-tab-bottom {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.delivery-options-wrapper
  .right-tab-content
  .right-tab-bottom
  .delivery-options {
  width: 100%;
  max-width: 220px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

@media (min-width: 992px) {
  .delivery-options-wrapper
    .right-tab-content
    .right-tab-bottom
    .delivery-options {
    gap: 25px;
  }
}

@media (min-width: 1200px) {
  .delivery-options-wrapper
    .right-tab-content
    .right-tab-bottom
    .delivery-options {
    gap: 40px;
  }
}

.delivery-options-wrapper
  .right-tab-content
  .right-tab-bottom
  .delivery-options.delivery-options-2 {
  gap: 15px;
}

@media (min-width: 992px) {
  .delivery-options-wrapper
    .right-tab-content
    .right-tab-bottom
    .delivery-options.delivery-options-2 {
    max-width: 362px;
  }
}

.delivery-options-wrapper
  .right-tab-content
  .right-tab-bottom
  .delivery-options
  .info-item
  .inner {
  display: flex;
  gap: 8px;
  color: var(--black, #212c4f);
  font-size: 16px;
  font-family: Readex Pro;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0.32px;
}

.delivery-options-wrapper
  .right-tab-content
  .right-tab-bottom
  .delivery-options
  .info-item
  .inner::before {
  content: "";
  border-radius: 13px;
  width: 13px;
  height: 13px;
  background: linear-gradient(0deg, #5558ff 0%, #00c0ff 100%);
  display: inline-block;
  margin-top: 7px;
}

.delivery-options-wrapper
  .right-tab-content
  .right-tab-bottom
  .delivery-options
  .info-item
  .inner
  span {
  max-width: calc(100% - 21px);
  flex-grow: 1;
}

.delivery-options-wrapper
  .right-tab-content
  .right-tab-bottom
  .delivery-options
  .info-item
  .info-sub-item {
  display: flex;
  gap: 8px;
  width: fit-content;
  color: var(--black, #212c4f);
  font-size: 14px;
  font-family: Readex Pro;
  font-weight: 500;
  line-height: 1.6;
  letter-spacing: 0.32px;
}

.delivery-options-wrapper
  .right-tab-content
  .right-tab-bottom
  .delivery-options
  .info-item
  .info-sub-item::before {
  content: "";
  border-radius: 8px;
  width: 8px;
  height: 8px;
  background: #212c4f;
  display: inline-block;
  margin-top: 6px;
}

.delivery-options-wrapper
  .right-tab-content
  .right-tab-bottom
  .delivery-options
  .info-item
  .info-sub-item
  span {
  max-width: calc(100% - 14px);
  flex-grow: 1;
}

.delivery-options-wrapper
  .right-tab-content
  .right-tab-bottom
  .delivery-options
  .inner::before {
  background: linear-gradient(157deg, #0a67f2 0%, #0a67f2 87.58%) !important;
}

.delivery-options-wrapper .right-tab-content .right-tab-bottom .thumb {
  width: 0;
  flex-grow: 1;
  max-width: 487px;
  @apply mx-auto;
}

.delivery-options-wrapper .right-tab-content .right-tab-bottom .thumb img {
  max-width: 100%;
}

@media (min-width: 992px) {
  .delivery-options-wrapper
    .right-tab-content
    .right-tab-bottom
    .thumb.thumb-2 {
    max-width: 300px;
    margin-right: auto;
  }

  .delivery-options-wrapper
    .right-tab-content
    .right-tab-bottom
    .thumb.thumb-2
    img {
    max-width: unset;
    width: unset;
  }
}

@media (min-width: 992px) {
  .delivery-options-wrapper .right-tab-content .tab-area .tab-item {
    display: none;
  }

  .delivery-options-wrapper
    .right-tab-content
    .tab-area
    .tab-item:nth-child(1) {
    display: block;
  }
}

@media (min-width: 992px) {
  .delivery-options-wrapper .right-tab-content .tab-area .left-tab-menu {
    display: none;
  }
}

@media (max-width: 991px) {
  .delivery-options-wrapper .right-tab-content .tab-area .section-title {
    margin-top: 15px;
  }
}

@media (min-width: 992px) {
  .delivery-options-wrapper {
    padding-right: 0;
  }

  .delivery-options-wrapper .right-tab-content .tab-item {
    padding-right: 50px;
  }
}

@media (max-width: 991px) {
  .delivery-options-wrapper {
    padding: 30px 0;
  }

  .delivery-options-wrapper .slick-slide {
    padding-inline: 20px;
  }

  .delivery-options-wrapper .left-tab-menu,
  .delivery-options-wrapper .right-tab-content {
    width: 100%;
  }

  .delivery-options-wrapper .left-tab-menu .right-tab-bottom,
  .delivery-options-wrapper .right-tab-content .right-tab-bottom {
    justify-content: center;
  }

  .delivery-options-wrapper .left-tab-menu .right-tab-bottom .thumb,
  .delivery-options-wrapper .right-tab-content .right-tab-bottom .thumb {
    width: 100%;
    text-align: center;
  }

  .delivery-options-wrapper .left-tab-menu .right-tab-bottom .delivery-options,
  .delivery-options-wrapper
    .right-tab-content
    .right-tab-bottom
    .delivery-options {
    flex-direction: row;
    flex-wrap: wrap;
    column-gap: 15px;
    row-gap: 5px;
    margin-top: 25px;
    justify-content: center;
  }

  .delivery-options-wrapper
    .left-tab-menu
    .right-tab-bottom
    .delivery-options
    .info-item,
  .delivery-options-wrapper
    .right-tab-content
    .right-tab-bottom
    .delivery-options
    .info-item {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
    letter-spacing: 0.24px;
  }

  .delivery-options-wrapper
    .left-tab-menu
    .right-tab-bottom
    .delivery-options
    .info-item::before,
  .delivery-options-wrapper
    .right-tab-content
    .right-tab-bottom
    .delivery-options
    .info-item::before {
    margin: 0;
    transform: translateY(4px);
    width: 10px;
    height: 10px;
  }

  .delivery-options-wrapper .left-tab-menu .left-menu-item:not(.active) {
    display: none;
  }
}

.column-gap-3 {
  column-gap: 25px;
  row-gap: 10px;
}

@keyframes bg1 {
  0% {
    width: 0;
    height: 10px;
  }

  50% {
    width: 100%;
    height: 10px;
  }

  100% {
    width: 100%;
    height: calc(100% - 1px);
  }
}

@keyframes bg2 {
  0% {
    width: 0;
    height: 10px;
  }

  50% {
    width: 0;
    height: 10px;
  }

  75% {
    width: 100%;
    height: 10px;
  }

  100% {
    width: 100%;
    height: calc(100% - 1px);
  }
}

.left-menu-item {
  position: relative;
  overflow: hidden;
  padding: 1px;
}

.left-menu-item::after,
.left-menu-item::before {
  z-index: 3;
  border-radius: 10px;
  background-repeat: no-repeat;
  background: #0a67f2;
  content: "";
  left: 1px;
  top: 0;
  position: absolute;
  display: none;
  width: 100%;
  height: calc(100% - 1px);
}

.left-menu-item::after {
  left: unset !important;
  top: unset !important;
  bottom: 0 !important;
  right: 0 !important;
}

.left-menu-item.active::after {
  display: block;
  animation: bg2 5s linear forwards;
}

.left-menu-item.active::before {
  display: block;
  animation: bg1 2.5s linear forwards;
}

@media screen and (min-width: 1025px) {
  .thumb.thumb-2 img,
  img.mw-100 {
    width: 520px !important;
  }

  .right-tab-bottom.tab-two {
    align-items: flex-start !important;
  }
}

@media screen and (max-width: 1024px) {
  .delivery-options-wrapper .delivery-options.delivery-options-2 {
    margin-bottom: 10px !important;
  }

  .delivery-options-wrapper
    .right-tab-content
    .right-tab-bottom
    .delivery-options {
    justify-content: start !important;
  }
}
