import React from "react";

const SelectInput = ({
  options,
  value,
  onChange,
  placeholder = "Select an option",
  className = "",
  label = null,
  error = null,
  disabled = false,
  required = false,
}) => {
  return (
    <>
      <div className="">
        {label && (
          <label className="text-sm archivo font-medium mb-2 text-[#374151] inline-block">
            {label} {required && <span className="text-red-500"> *</span>}
          </label>
        )}
        <select
          value={value}
          onChange={onChange}
          disabled={disabled}
          className={`border inter font-medium ${error ? 'border-red-500' : 'border-[#D1D5DB]'} !placeholder:text-[#6B7280] ${value ? '!text-[#374151]' : '!text-[#6B7280]'} text-sm rounded-lg outline-0 block p-2.5 h-10 custom-select ${className}`}
        >
          {placeholder && (
            <option value="" className="text-[#6B7280]" disabled>
              {placeholder}
            </option>
          )}
          {options?.map((opt, index) => (
            <option
              key={`${opt.value || ''}-${index}`}
              value={opt.value || ''}
              className="text-[#374151]"
            >
              {opt.label || 'Option'}
            </option>
          ))}
        </select>
        {error && (
          <div className="text-red-500 text-sm mt-1">{error}</div>
        )}
      </div>
    </>
  );
};

export default SelectInput;
