import React, { useEffect } from "react";
import CategoryCard from "./CategoryCard";
import Link from "next/link";
import "aos/dist/aos.css";
import Aos from "aos";
const CategorySection = () => {
  const categoryData = [
    {
      id: 1,
      img: "/assets/frontend_assets/product-card-img-1.png",
      title: "Accounting & Bookkeeping",
      rating: "4.5",
    },
    {
      id: 2,
      img: "/assets/frontend_assets/product-card-2.png",
      title: "Business Consulting",
      rating: "4.5",
    },
    {
      id: 3,
      img: "/assets/frontend_assets/product-card-3.png",
      title: "Tax Consulting",
      rating: "4.5",
    },
    {
      id: 4,
      img: "/assets/frontend_assets/product-card-5.png",
      title: "Insurance Providers",
      rating: "4.5",
    },
  ];
  useEffect(() => {
    Aos.init();
  }, []);
  return (
    <>
      <div className="py-10">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center">
            <h3 className="archivo text-[32px] font-bold text-[#0F0C1D]">
              Explore our
              <span className="ml-2 text-transparent bg-clip-text bg-linear-to-r from-[#FD2692]   to-[#0A67F2]">
                Categories
              </span>
            </h3>
            <p className="text-[#656B76] archivo font-normal text-base max-w-xl mx-auto">
              Service categories help organize and structure the offerings on a
              marketplace, making it easier for users to find what they need.{" "}
            </p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mt-10">
            {categoryData.map((item, index) => (
              <CategoryCard
                key={index}
                index={index}
                imgSrc={item.img}
                title={item.title}
                review={item.rating}
              />
            ))}
          </div>
          <div className="text-center mt-10">
            <Link
              href=""
              className="text-base inline-block dm_sans px-5 py-2 mt-2 text-white font-normal bg-[#111827] text-center rounded-[8px]"
            >
              View All{" "}
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default CategorySection;
