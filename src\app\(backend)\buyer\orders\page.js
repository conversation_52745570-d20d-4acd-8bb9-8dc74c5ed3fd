"use client";
export const dynamic = "force-dynamic";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import Pagination from "@/components/backend/Pagination";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import { useFetchApiQuery } from "@/redux/services/api";
import { handleUnauthorized } from '@/utils/auth';
import { useRouter } from 'next/navigation';

const Orders = () => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage] = useState(10);

  // Fetch orders from API
  const {
    data: ordersData,
    isLoading,
    error,
    refetch
  } = useFetchApiQuery({
    endpoint: "/buyer/orders",
    params: {
      page: currentPage,
      limit: perPage
    },
    skip: false,
  });

  // Refetch when page changes
  useEffect(() => {
    refetch();
  }, [currentPage, refetch]);

  // Handle unauthorized access
  useEffect(() => {
    if (error) {
      handleUnauthorized(error, router);
    }
  }, [error, router]);

  // Extract orders and pagination data from API response
  const orders = ordersData?.data || [];
  const totalCount = ordersData?.meta?.total || 0;
  const totalPages = ordersData?.meta?.last_page || Math.ceil(totalCount / perPage);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status color
  const getStatusColor = (status) => {
    status = status?.toLowerCase() || '';

    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
      case 'canceled':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'refunded':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold text-[#343A40] text-xl poppins">
          My Orders
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
            </div>
          ) : error ? (
            <div className="text-center py-10">
              <p className="text-red-500">Error loading orders</p>
              <button
                onClick={() => refetch()}
                className="mt-4 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
              >
                Try Again
              </button>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-10">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              <p className="mt-4 text-lg text-gray-600">No orders found</p>
              <p className="text-gray-500">You haven't placed any orders yet.</p>
              <Link
                href="/buyer/processed-offers"
                className="mt-4 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Browse Offers
              </Link>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left rtl:text-right text-gray-500 inter">
                  <thead className="text-xs text-[#4C596E] capitalize bg-[#E2E8F0]">
                    <tr>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Order #
                      </th>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Date
                      </th>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Items
                      </th>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Total
                      </th>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Status
                      </th>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.map((order) => (
                      <tr key={order.id} className="bg-white border-b border-gray-200">
                        <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568]">
                          {order.order_number || `ORD-${order.id.substring(0, 8)}`}
                        </td>
                        <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568]">
                          {formatDate(order.created_at || order.date)}
                        </td>
                        <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568]">
                          <ul className="list-disc list-inside">
                            {(order.items || order.order_items || []).map((item, index) => (
                              <li key={item.id || index}>
                                {item.title || item.name || 'Item'}
                                {item.quantity && `(x${item.quantity})`}
                              </li>
                            ))}
                          </ul>
                        </td>
                        <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568]">
                          ${(order.total_amount || order.total || order.amount || 0).toFixed(2)}
                        </td>
                        <td className="px-3 py-4 font-semibold text-sm inter">
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(order.order_status || order.status)}`}>
                            {order.order_status || order.status || 'Pending'}
                          </span>
                        </td>
                        <td className="px-3 py-4 font-medium inter text-[#4A5568]">
                          <Link
                            href={`/buyer/orders/${order.id}`}
                            className="text-sm font-medium px-3 py-1 bg-[#175CD3] text-white rounded-sm hover:bg-[#1253c0] transition-colors"
                          >
                            View Details
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {totalPages > 1 && (
                <Pagination
                  totalItems={totalCount}
                  pageSize={perPage}
                  currentPage={currentPage}
                  onPageChange={(page) => setCurrentPage(page)}
                />
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Orders;
