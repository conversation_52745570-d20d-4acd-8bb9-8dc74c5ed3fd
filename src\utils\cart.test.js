import { addToCart, removeFromCart, updateCartItem, clearCart } from './cart';
import { toast } from 'react-hot-toast';

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  toast: {
    loading: jest.fn(() => 'loading-toast-id'),
    success: jest.fn(),
    error: jest.fn(),
    dismiss: jest.fn(),
  },
}));

describe('Cart Utilities', () => {
  let mockMutateApi;

  beforeEach(() => {
    jest.clearAllMocks();
    mockMutateApi = jest.fn();
  });

  describe('addToCart', () => {
    it('should call mutateApi with correct parameters', async () => {
      const offerId = 'test-offer-id';
      const quantity = 2;
      const mockResponse = { data: { id: 'cart-item-id' } };
      
      mockMutateApi.mockResolvedValue(mockResponse);
      
      const result = await addToCart(mockMutateApi, offerId, quantity);
      
      expect(mockMutateApi).toHaveBeenCalledWith({
        endpoint: '/buyer/cart',
        method: 'POST',
        data: {
          offer_id: offerId,
          quantity: quantity
        }
      });
      
      expect(toast.loading).toHaveBeenCalledWith('Adding item to cart...');
      expect(toast.dismiss).toHaveBeenCalledWith('loading-toast-id');
      expect(toast.success).toHaveBeenCalledWith('Item added to cart successfully');
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle errors', async () => {
      const offerId = 'test-offer-id';
      const error = { data: { message: 'Error message' } };
      
      mockMutateApi.mockRejectedValue(error);
      
      const result = await addToCart(mockMutateApi, offerId);
      
      expect(toast.error).toHaveBeenCalledWith('Error message');
      expect(result).toBeNull();
    });

    it('should validate offerId', async () => {
      const result = await addToCart(mockMutateApi, null);
      
      expect(mockMutateApi).not.toHaveBeenCalled();
      expect(toast.error).toHaveBeenCalledWith('Invalid offer ID');
      expect(result).toBeNull();
    });
  });

  describe('removeFromCart', () => {
    it('should call mutateApi with correct parameters', async () => {
      const itemId = 'test-item-id';
      const mockResponse = { data: null };
      
      mockMutateApi.mockResolvedValue(mockResponse);
      
      const result = await removeFromCart(mockMutateApi, itemId);
      
      expect(mockMutateApi).toHaveBeenCalledWith({
        endpoint: `/buyer/cart/${itemId}`,
        method: 'DELETE'
      });
      
      expect(toast.loading).toHaveBeenCalledWith('Removing item from cart...');
      expect(toast.dismiss).toHaveBeenCalledWith('loading-toast-id');
      expect(toast.success).toHaveBeenCalledWith('Item removed from cart successfully');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('updateCartItem', () => {
    it('should call mutateApi with correct parameters', async () => {
      const itemId = 'test-item-id';
      const quantity = 3;
      const mockResponse = { data: { id: 'cart-item-id', quantity: 3 } };
      
      mockMutateApi.mockResolvedValue(mockResponse);
      
      const result = await updateCartItem(mockMutateApi, itemId, quantity);
      
      expect(mockMutateApi).toHaveBeenCalledWith({
        endpoint: `/buyer/cart/${itemId}`,
        method: 'PUT',
        data: {
          quantity: quantity
        }
      });
      
      expect(toast.loading).toHaveBeenCalledWith('Updating cart...');
      expect(toast.dismiss).toHaveBeenCalledWith('loading-toast-id');
      expect(toast.success).toHaveBeenCalledWith('Cart updated successfully');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('clearCart', () => {
    it('should call mutateApi with correct parameters', async () => {
      const mockResponse = { data: null };
      
      mockMutateApi.mockResolvedValue(mockResponse);
      
      const result = await clearCart(mockMutateApi);
      
      expect(mockMutateApi).toHaveBeenCalledWith({
        endpoint: '/buyer/cart',
        method: 'DELETE'
      });
      
      expect(toast.loading).toHaveBeenCalledWith('Clearing cart...');
      expect(toast.dismiss).toHaveBeenCalledWith('loading-toast-id');
      expect(toast.success).toHaveBeenCalledWith('Cart cleared successfully');
      expect(result).toEqual(mockResponse.data);
    });
  });
});
