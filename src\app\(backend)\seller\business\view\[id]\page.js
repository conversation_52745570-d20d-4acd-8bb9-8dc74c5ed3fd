"use client";

import React, { useEffect } from "react";
import { useFetchApiQuery } from "@/redux/services/api";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import { useRouter, useParams } from 'next/navigation';
import { handleUnauthorized } from '@/utils/auth';
import Link from 'next/link';
import Image from 'next/image';
import { asseturl } from "@/config";

const BusinessDetails = () => {
  const router = useRouter();
  const params = useParams();
  const businessId = params.id;

  // Fetch business information by ID
  const { data: businessData, error: businessError, isLoading } = useFetchApiQuery({
    endpoint: `/seller/business-information/${businessId}`,
    skip: !businessId,
  });

  // Handle 403 errors
  useEffect(() => {
    if (businessError?.status === 403) {
      handleUnauthorized(businessError, router);
    }
  }, [businessError, router]);

  const business = businessData?.data;

  const getVerificationBadge = (isVerified, verificationStatus) => {
    if (isVerified) {
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          Verified
        </span>
      );
    } else if (verificationStatus === 'pending') {
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
          Pending Verification
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          Not Verified
        </span>
      );
    }
  };

  const getStatusBadge = (isActive) => {
    return isActive ? (
      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
        Active
      </span>
    ) : (
      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
        Inactive
      </span>
    );
  };

  if (isLoading) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </>
    );
  }

  if (businessError && businessError.status !== 403) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 className="text-red-800 font-medium">Error loading business details</h3>
            <p className="text-red-600 text-sm mt-1">
              {businessError?.data?.message || 'Failed to load business information'}
            </p>
          </div>
        </div>
      </>
    );
  }

  if (!business) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
            <h3 className="text-gray-800 font-medium">Business not found</h3>
            <p className="text-gray-600 text-sm mt-1">The requested business information could not be found.</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <div className="flex justify-between items-center mb-5">
          <h5 className="font-semibold text-[#343A40] text-xl poppins">
            Business Details
          </h5>
          <Link
            href={`/seller/business/edit/${business.id}`}
            className="bg-[#4F46E5] text-white px-4 py-2 rounded-lg font-medium text-sm hover:bg-[#4338CA] transition-colors"
          >
            Edit Business
          </Link>
        </div>

        <div className="bg-white rounded-2xl border border-[#CACACA] overflow-hidden">
          {/* Header Section with Banner */}
          {business.banner_url && (
            <div className="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative">
              <Image
                src={asseturl + business.banner_url}
                alt={business.business_name}
                fill
                className="object-cover"
              />
            </div>
          )}

          <div className="p-6">
            {/* Business Header */}
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-start space-x-4">
                {business.logo_url && (
                  <div className="flex-shrink-0">
                    <Image
                      src={ asseturl + business.logo_url}
                      alt={business.business_name}
                      width={80}
                      height={80}
                      className="w-20 h-20 rounded-lg object-cover border-4 border-white shadow-lg"
                    />
                  </div>
                )}
                <div className="flex-1">
                  <h1 className="text-2xl font-bold text-[#343A40] mb-2">
                    {business.business_name}
                  </h1>
                  <p className="text-gray-600 mb-3">{business.short_description}</p>
                  <div className="flex items-center space-x-3">
                    {getVerificationBadge(business.is_verified, business.verification_status)}
                    {getStatusBadge(business.is_active)}
                  </div>
                </div>
              </div>
            </div>

            {/* Business Description */}
            {business.long_description && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-[#343A40] mb-3">About</h3>
                <p className="text-gray-700 leading-relaxed">{business.long_description}</p>
              </div>
            )}

            {/* Business Information Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              {/* Contact Information */}
              <div>
                <h3 className="text-lg font-semibold text-[#343A40] mb-4">Contact Information</h3>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <svg className="w-5 h-5 text-gray-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <div>
                      <p className="font-medium text-gray-900">{business.address}</p>
                      <p className="text-gray-600">{business.city}, {business.state} {business.postal_code}</p>
                      <p className="text-gray-600">{business.country}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    <span className="text-gray-700">{business.phone_number}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span className="text-gray-700">{business.email}</span>
                  </div>
                  {business.website_url && (
                    <div className="flex items-center space-x-3">
                      <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                      </svg>
                      <a href={business.website_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        {business.website_url}
                      </a>
                    </div>
                  )}
                </div>
              </div>

              {/* Business Details */}
              <div>
                <h3 className="text-lg font-semibold text-[#343A40] mb-4">Business Details</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-gray-500 text-sm">Business Type:</span>
                    <p className="font-medium text-gray-900">{business.business_type}</p>
                  </div>
                  <div>
                    <span className="text-gray-500 text-sm">Category:</span>
                    <p className="font-medium text-gray-900">{business.business_category}</p>
                  </div>
                  <div>
                    <span className="text-gray-500 text-sm">Established:</span>
                    <p className="font-medium text-gray-900">{business.established_year}</p>
                  </div>
                  <div>
                    <span className="text-gray-500 text-sm">Employee Count:</span>
                    <p className="font-medium text-gray-900">{business.employee_count}</p>
                  </div>
                  <div>
                    <span className="text-gray-500 text-sm">Annual Revenue:</span>
                    <p className="font-medium text-gray-900">{business.annual_revenue}</p>
                  </div>
                  {business.business_license && (
                    <div>
                      <span className="text-gray-500 text-sm">Business License:</span>
                      <p className="font-medium text-gray-900">{business.business_license}</p>
                    </div>
                  )}
                  {business.tax_id && (
                    <div>
                      <span className="text-gray-500 text-sm">Tax ID:</span>
                      <p className="font-medium text-gray-900">{business.tax_id}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Operating Hours */}
            {business.operating_hours && Object.keys(business.operating_hours).length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-[#343A40] mb-4">Operating Hours</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(business.operating_hours).map(([day, hours]) => (
                    <div key={day} className="text-center p-3 bg-gray-50 rounded-lg">
                      <p className="font-medium text-gray-900 capitalize">{day}</p>
                      <p className="text-sm text-gray-600">{hours}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Services Offered */}
            {business.services_offered && business.services_offered.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-[#343A40] mb-4">Services Offered</h3>
                <div className="flex flex-wrap gap-2">
                  {business.services_offered.map((service, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                    >
                      {service}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Certifications */}
            {business.certifications && business.certifications.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-[#343A40] mb-4">Certifications</h3>
                <div className="flex flex-wrap gap-2">
                  {business.certifications.map((certification, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                    >
                      {certification}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Social Media Links */}
            {business.social_media_links && Object.keys(business.social_media_links).length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-[#343A40] mb-4">Social Media</h3>
                <div className="flex space-x-4">
                  {business.social_media_links.facebook && (
                    <a href={business.social_media_links.facebook} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    </a>
                  )}
                  {business.social_media_links.twitter && (
                    <a href={business.social_media_links.twitter} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-600">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                    </a>
                  )}
                  {business.social_media_links.linkedin && (
                    <a href={business.social_media_links.linkedin} target="_blank" rel="noopener noreferrer" className="text-blue-700 hover:text-blue-900">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </a>
                  )}
                  {business.social_media_links.instagram && (
                    <a href={business.social_media_links.instagram} target="_blank" rel="noopener noreferrer" className="text-pink-600 hover:text-pink-800">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z"/>
                      </svg>
                    </a>
                  )}
                </div>
              </div>
            )}

            {/* Footer Information */}
            <div className="pt-6 border-t border-gray-200">
              <div className="flex justify-between items-center text-sm text-gray-500">
                <span>Created: {new Date(business.created_at).toLocaleDateString()}</span>
                <span>Last Updated: {new Date(business.updated_at).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BusinessDetails;
