
"use client";
export const dynamic = "force-dynamic";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import Pagination from "@/components/backend/Pagination";
import SelectInput from "@/components/backend/SelectInput";
import Link from "next/link";
import React, { useState, useEffect, useCallback } from "react";
import { useFetchApiQuery, useMutateApiMutation } from "@/redux/services/api";
import { useSearchParams, useRouter } from "next/navigation";
import StatusHistoryModal from "@/components/backend/StatusHistoryModal";
import { toast } from "react-hot-toast";

const RequestList = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const statusFromUrl = searchParams.get('status');

  const [selectedSubCategory, setSelectedSubCategory] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedStatus, setSelectedStatus] = useState(statusFromUrl || "All");
  const [selectedUrgency, setSelectedUrgency] = useState("");
  const [selectedRequestType, setSelectedRequestType] = useState("");
  const [subCategories, setSubCategories] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);


  useEffect(() => {
    setSelectedStatus(statusFromUrl || "All");
  }, [statusFromUrl]);

  // Modal states
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRequestId, setSelectedRequestId] = useState(null);

  // Cancel modal state
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelRequestId, setCancelRequestId] = useState(null);
  const [cancelReason, setCancelReason] = useState("");

  // Update URL when status changes
  const handleStatusChange = (e) => {
    const newStatus = e.target.value;
    setSelectedStatus(newStatus);

    // Update URL with new status
    if (newStatus === "All") {
      router.push("/requests/list");
    } else {
      router.push(`/requests/list?status=${newStatus}`);
    }
  };

  // Fetch categories
  const { data: categoriesData } = useFetchApiQuery({
    endpoint: "/buyer/categories",
    skip: false, // Ensure the query always runs with a valid endpoint
  });

  // Build query parameters for API call
  const buildQueryParams = () => {
    const params = new URLSearchParams();

    if (selectedStatus && selectedStatus !== "All") {
      params.append("status", selectedStatus);
    }

    if (selectedCategory) {
      params.append("category_id", selectedCategory);
    }

    if (selectedSubCategory) {
      params.append("sub_category_id", selectedSubCategory);
    }

    if (selectedUrgency) {
      params.append("urgency", selectedUrgency);
    }

    if (selectedRequestType) {
      params.append("request_type", selectedRequestType);
    }

    if (debouncedSearchTerm) {
      params.append("search", debouncedSearchTerm);
    }

    const queryString = params.toString();
    return queryString ? `?${queryString}` : "";
  };

  // Fetch requests with all filters
  const { data: requestsData, isLoading, refetch } = useFetchApiQuery({
    endpoint: `/buyer/requests${buildQueryParams()}`,
    skip: false, // Ensure the query always runs with a valid endpoint
  });

  // Mutation hook for canceling requests
  const [mutateApi, { isLoading: isCanceling }] = useMutateApiMutation();

  // Update subcategories when category changes
  useEffect(() => {
    if (selectedCategory && categoriesData?.data) {
      const category = categoriesData.data.find(cat => cat.id === selectedCategory);
      if (category && category.sub_categories) {
        setSubCategories(category.sub_categories);
      } else {
        setSubCategories([]);
      }
    } else {
      setSubCategories([]);
    }
  }, [selectedCategory, categoriesData]);

  // Refetch when any filter changes
  useEffect(() => {
    refetch();
  }, [
    selectedStatus,
    selectedCategory,
    selectedSubCategory,
    selectedUrgency,
    selectedRequestType,
    debouncedSearchTerm, // Use debounced search term instead of searchTerm
    refetch
  ]);

  const requests = requestsData?.data || [];
  const totalCount = requestsData?.count || 0;

  // Status options for the dropdown
  const statusOptions = [
    { value: "All", label: "All" },
    { value: "Pending", label: "Pending" },
    { value: "Approved", label: "Approved" },
    { value: "Rejected", label: "Rejected" },
    { value: "Cancelled", label: "Cancelled" },
  ];

  // Urgency options for the dropdown
  const urgencyOptions = [
    { value: "", label: "All Urgency" },
    { value: "Low", label: "Low" },
    { value: "Normal", label: "Normal" },
    { value: "High", label: "High" },
    { value: "Urgent", label: "Urgent" },
  ];

  // Request type options for the dropdown
  const requestTypeOptions = [
    { value: "", label: "All Types" },
    { value: "General", label: "General" },
    { value: "Custom", label: "Custom" },
    { value: "Bulk", label: "Bulk" },
  ];

  // Format date to "17th January, 2025 2:00 pm" format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString('default', { month: 'long' });
    const year = date.getFullYear();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'pm' : 'am';
    const formattedHours = hours % 12 || 12;

    // Add ordinal suffix to day
    const suffixes = ['th', 'st', 'nd', 'rd'];
    const relevantDigits = (day < 30) ? day % 20 : day % 30;
    const suffix = (relevantDigits <= 3) ? suffixes[relevantDigits] : suffixes[0];

    return `${day}${suffix} ${month}, ${year} ${formattedHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  };

  // Get status color for the badge
  const getStatusColor = (status) => {
    switch (status) {
      case 'Approved':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-blue-100 text-blue-800';
      case 'Rejected':
        return 'bg-red-800 text-red-100';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // We're now using server-side filtering, so we don't need to filter the requests client-side
  const filteredRequests = requests;

  // Pagination
  const paginatedRequests = filteredRequests.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle opening the status history modal
  const handleOpenStatusModal = (requestId) => {
    setSelectedRequestId(requestId);
    setIsModalOpen(true);
  };

  // Handle closing the status history modal
  const handleCloseStatusModal = () => {
    setIsModalOpen(false);
    setSelectedRequestId(null);
  };

  // Reset all filters
  const handleResetFilters = () => {
    setSelectedCategory("");
    setSelectedSubCategory("");
    setSelectedStatus("All");
    setSelectedUrgency("");
    setSelectedRequestType("");
    setSearchTerm("");
    setCurrentPage(1);
    router.push("/requests/list"); // Reset URL
  };

  // Handle opening the cancel request modal
  const handleOpenCancelModal = (requestId) => {
    setCancelRequestId(requestId);
    setShowCancelModal(true);
  };

  // Handle closing the cancel request modal
  const handleCloseCancelModal = () => {
    setShowCancelModal(false);
    setCancelRequestId(null);
    setCancelReason("");
  };

  // Handle submitting the cancel request
  const handleCancelRequest = () => {
    if (!cancelReason.trim()) {
      toast.error('Please provide a reason for cancellation');
      return;
    }

    const loadingToast = toast.loading('Cancelling request...');

    console.log('Attempting to cancel request:', cancelRequestId, 'with reason:', cancelReason);

    mutateApi({
      endpoint: `/buyer/requests/${cancelRequestId}/cancel`,
      data: { reason: cancelReason }
    }).then(() => {
      toast.dismiss(loadingToast);
      toast.success('Request cancelled successfully');
      handleCloseCancelModal();

      // Refresh the data
      refetch();
    }).catch((error) => {
      console.error('Error cancelling request:', error);
      toast.dismiss(loadingToast);

      // Handle authentication errors
      if (error?.status === 401 || error?.status === 403) {
        import('@/utils/auth').then(({ handleUnauthorized }) => {
          handleUnauthorized(error, router);
        });
      } else {
        toast.error(
          error?.data?.message ||
          "Failed to cancel request. Please try again."
        );
      }
    });
  };

  if (isLoading) {
    return <div>Loading requests...</div>;
  }

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold text-[#343A40] text-xl poppins">
          Request List
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4">
          <div className="mb-4 flex flex-wrap items-center gap-2">
            <div>
              <SelectInput
                options={categoriesData?.data?.map(category => ({
                  value: category.id,
                  label: category.title
                })) || []}
                value={selectedCategory}
                onChange={(e) => {
                  setSelectedCategory(e.target.value);
                  setSelectedSubCategory("");
                }}
                placeholder="Select Category"
                className="w-[170px]"
              />
            </div>
            <div>
              <SelectInput
                options={subCategories.map(subCat => ({
                  value: subCat.id,
                  label: subCat.title
                }))}
                value={selectedSubCategory}
                onChange={(e) => setSelectedSubCategory(e.target.value)}
                placeholder="Select Sub Category"
                className="w-[200px]"
                disabled={!selectedCategory}
              />
            </div>
            <div>
              <SelectInput
                options={statusOptions}
                value={selectedStatus}
                onChange={handleStatusChange}
                placeholder="Status"
                className="w-[150px]"
              />
            </div>
            <div>
              <SelectInput
                options={urgencyOptions}
                value={selectedUrgency}
                onChange={(e) => setSelectedUrgency(e.target.value)}
                placeholder="Urgency"
                className="w-[150px]"
              />
            </div>
            <div>
              <SelectInput
                options={requestTypeOptions}
                value={selectedRequestType}
                onChange={(e) => setSelectedRequestType(e.target.value)}
                placeholder="Request Type"
                className="w-[150px]"
              />
            </div>
            <div className="flex-grow">
              <div className="relative flex">
                <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                  <svg
                    className="w-4 h-4 text-[#374151]"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 20 20"
                  >
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                    />
                  </svg>
                </div>
                <input
                  type="search"
                  id="default-search"
                  className="ps-10 border inter font-medium border-[#D1D5DB] placeholder:text-[#374151] text-[#374151] text-sm rounded-lg outline-0 block p-2 h-10 w-full"
                  placeholder="Search by title or description"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <button
                  onClick={() => refetch()}
                  className="ml-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors cursor-pointer"
                >
                  Search
                </button>
                <button
                  onClick={handleResetFilters}
                  className="ml-2 px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors cursor-pointer"
                >
                  X
                </button>
              </div>
            </div>
          </div>

          <table className="w-full text-sm text-left rtl:text-right text-gray-500 inter">
            <thead className="text-xs text-[#4C596E] capitalize bg-[#E2E8F0]">
              <tr>
                <th scope="col" className="px-3 py-3 font-medium">
                  #
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Request Title
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Status
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Category
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Sub Category
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Urgency Level
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Order Submission Deadline
                </th>
                <th scope="col" className="px-3 py-3 font-medium">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {paginatedRequests.length > 0 ? (
                paginatedRequests.map((request, index) => (
                  <tr key={request.id} className="bg-white border-b border-gray-200">
                    <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                      {(currentPage - 1) * pageSize + index + 1}
                    </td>
                    <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                      <Link
                        href={`/requests/${request.id}`}
                        className="text-sm font-medium rounded-sm hover:text-blue-600"
                      >
                        {request.title}
                      </Link>
                    </td>
                    <td className="px-3 py-4 font-semibold text-sm inter whitespace-nowrap">
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(request.status)}`}>
                        {request.status}
                      </span>
                    </td>
                    <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                      {request.category?.title || 'N/A'}
                    </td>
                    <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                      {request.sub_category?.title || 'N/A'}
                    </td>
                    <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                      {request.urgency}
                    </td>
                    <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                      {formatDate(request.deadline)}
                    </td>
                    <td className="px-3 py-4 font-medium inter text-[#4A5568] whitespace-nowrap">
                      <button
                        onClick={() => handleOpenStatusModal(request.id)}
                        className="text-sm font-medium px-3 py-1 bg-[#175CD3] text-white rounded-sm cursor-pointer"
                      >
                        Check status
                      </button>
                      <Link
                        href={`/requests/${request.id}`}
                        className="text-sm font-medium px-3 py-1 ml-2 bg-[#FC791A] text-white rounded-sm"
                      >
                        View
                      </Link>
                      {request.status === 'Pending' && (
                        <>
                          <Link
                            href={`/requests/edit/${request.id}`}
                            className="text-sm font-medium px-3 py-1 ml-2 bg-[#4F46E5] text-white rounded-sm"
                          >
                            Edit
                          </Link>
                          <button
                            onClick={() => handleOpenCancelModal(request.id)}
                            className="text-sm font-medium px-3 py-1 ml-2 bg-red-500 text-white rounded-sm cursor-pointer"
                          >
                            Cancel
                          </button>
                        </>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="8" className="px-3 py-4 text-center text-gray-500">
                    No requests found matching your criteria
                  </td>
                </tr>
              )}
            </tbody>
          </table>

          {filteredRequests.length > 0 && (
            <Pagination
              totalItems={totalCount}
              pageSize={pageSize}
              currentPage={currentPage}
              onPageChange={handlePageChange}
            />
          )}
        </div>
      </div>

      {/* Status History Modal */}
      <StatusHistoryModal
        requestId={selectedRequestId}
        isOpen={isModalOpen}
        onClose={handleCloseStatusModal}
      />

      {/* Cancel Request Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 backdrop-blur-sm bg-black/70 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Cancel Request</h3>
            <p className="text-gray-600 mb-4">Please provide a reason for cancelling this request:</p>

            <textarea
              value={cancelReason}
              onChange={(e) => setCancelReason(e.target.value)}
              className="w-full border border-gray-300 rounded-lg p-3 mb-4 min-h-[100px]"
              placeholder="Enter reason for cancellation..."
            ></textarea>

            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCloseCancelModal}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 cursor-pointer"
              >
                Close
              </button>
              <button
                onClick={handleCancelRequest}
                disabled={!cancelReason.trim() || isCanceling}
                className={`px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 cursor-pointer ${
                  !cancelReason.trim() || isCanceling ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isCanceling ? 'Cancelling...' : 'Cancel Request'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default RequestList;
