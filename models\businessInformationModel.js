const { prisma } = require('../config/dbConfig');

class BusinessInformationModel {
  /**
   * Create a new business information
   * @param {Object} data - Business information data
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Created business information
   */
  static async createBusinessInformation(data, sellerId) {
    try {
      return await prisma.business_informations.create({
        data: {
          seller_id: sellerId,
          company_name: data.company_name,
          short_description: data.short_description,
          long_description: data.long_description,
          logo_url: data.logo_url,
          banner_url: data.banner_url,
          address: data.address,
          city: data.city,
          state: data.state,
          country: data.country,
          postal_code: data.postal_code,
          phone_number: data.phone_number,
          email: data.email,
          website_url: data.website_url,
          business_type: data.business_type,
          business_category: data.business_category,
          established_year: data.established_year ? parseInt(data.established_year) : null,
          employee_count: data.employee_count,
          annual_revenue: data.annual_revenue,
          business_license: data.business_license,
          tax_id: data.tax_id,
          social_media_links: data.social_media_links || null,
          operating_hours: data.operating_hours || null,
          services_offered: data.services_offered || null,
          certifications: data.certifications || null,
          is_active: data.is_active !== undefined ? data.is_active : true
        },
        include: {
          seller: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true
            }
          }
        }
      });
    } catch (error) {
      throw new Error(`Failed to create business information: ${error.message}`);
    }
  }

  /**
   * Get all business information for a seller
   * @param {string} sellerId - Seller ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} List of business information
   */
  static async getBusinessInformationsBySeller(sellerId, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        includeInactive = false
      } = options;

      const skip = (page - 1) * limit;

      const whereClause = {
        seller_id: sellerId,
        is_deleted: false
      };

      if (!includeInactive) {
        whereClause.is_active = true;
      }

      const [businessInfos, totalCount] = await Promise.all([
        prisma.business_informations.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: { created_at: 'desc' },
          include: {
            seller: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true
              }
            }
          }
        }),
        prisma.business_informations.count({
          where: whereClause
        })
      ]);

      return {
        data: businessInfos,
        meta: {
          total: totalCount,
          page,
          limit,
          totalPages: Math.ceil(totalCount / limit)
        }
      };
    } catch (error) {
      throw new Error(`Failed to get business information: ${error.message}`);
    }
  }

  /**
   * Get business information by ID
   * @param {string} id - Business information ID
   * @param {string} sellerId - Seller ID (for authorization)
   * @returns {Promise<Object|null>} Business information
   */
  static async getBusinessInformationById(id, sellerId = null) {
    try {
      const whereClause = {
        id,
        is_deleted: false
      };

      if (sellerId) {
        whereClause.seller_id = sellerId;
      }

      return await prisma.business_informations.findFirst({
        where: whereClause,
        include: {
          seller: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true
            }
          }
        }
      });
    } catch (error) {
      throw new Error(`Failed to get business information: ${error.message}`);
    }
  }

  /**
   * Update business information
   * @param {string} id - Business information ID
   * @param {Object} data - Update data
   * @param {string} sellerId - Seller ID (for authorization)
   * @returns {Promise<Object>} Updated business information
   */
  static async updateBusinessInformation(id, data, sellerId) {
    try {
      // First check if the business information exists and belongs to the seller
      const existingBusiness = await this.getBusinessInformationById(id, sellerId);
      if (!existingBusiness) {
        throw new Error('Business information not found or access denied');
      }

      const updateData = {};

      // Only update fields that are provided
      if (data.business_name !== undefined) updateData.business_name = data.business_name;
      if (data.short_description !== undefined) updateData.short_description = data.short_description;
      if (data.long_description !== undefined) updateData.long_description = data.long_description;
      if (data.logo_url !== undefined) updateData.logo_url = data.logo_url;
      if (data.banner_url !== undefined) updateData.banner_url = data.banner_url;
      if (data.address !== undefined) updateData.address = data.address;
      if (data.city !== undefined) updateData.city = data.city;
      if (data.state !== undefined) updateData.state = data.state;
      if (data.country !== undefined) updateData.country = data.country;
      if (data.postal_code !== undefined) updateData.postal_code = data.postal_code;
      if (data.phone_number !== undefined) updateData.phone_number = data.phone_number;
      if (data.email !== undefined) updateData.email = data.email;
      if (data.website_url !== undefined) updateData.website_url = data.website_url;
      if (data.business_type !== undefined) updateData.business_type = data.business_type;
      if (data.business_category !== undefined) updateData.business_category = data.business_category;
      if (data.established_year !== undefined) updateData.established_year = data.established_year ? parseInt(data.established_year) : null;
      if (data.employee_count !== undefined) updateData.employee_count = data.employee_count;
      if (data.annual_revenue !== undefined) updateData.annual_revenue = data.annual_revenue;
      if (data.business_license !== undefined) updateData.business_license = data.business_license;
      if (data.tax_id !== undefined) updateData.tax_id = data.tax_id;
      if (data.social_media_links !== undefined) updateData.social_media_links = data.social_media_links;
      if (data.operating_hours !== undefined) updateData.operating_hours = data.operating_hours;
      if (data.services_offered !== undefined) updateData.services_offered = data.services_offered;
      if (data.certifications !== undefined) updateData.certifications = data.certifications;
      if (data.is_active !== undefined) updateData.is_active = data.is_active;

      return await prisma.business_informations.update({
        where: { id },
        data: updateData,
        include: {
          seller: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true
            }
          }
        }
      });
    } catch (error) {
      throw new Error(`Failed to update business information: ${error.message}`);
    }
  }

  /**
   * Delete business information (soft delete)
   * @param {string} id - Business information ID
   * @param {string} sellerId - Seller ID (for authorization)
   * @returns {Promise<Object>} Deleted business information
   */
  static async deleteBusinessInformation(id, sellerId) {
    try {
      // First check if the business information exists and belongs to the seller
      const existingBusiness = await this.getBusinessInformationById(id, sellerId);
      if (!existingBusiness) {
        throw new Error('Business information not found or access denied');
      }

      return await prisma.business_informations.update({
        where: { id },
        data: {
          is_deleted: true,
          is_active: false
        }
      });
    } catch (error) {
      throw new Error(`Failed to delete business information: ${error.message}`);
    }
  }

  /**
   * Get all business information (for admin)
   * @param {Object} options - Query options
   * @returns {Promise<Object>} List of business information with pagination
   */
  static async getAllBusinessInformations(options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        business_type = '',
        verification_status = '',
        is_active = null
      } = options;

      const skip = (page - 1) * limit;

      const whereClause = {
        is_deleted: false
      };

      if (search) {
        whereClause.OR = [
          { company_name: { contains: search, mode: 'insensitive' } },
          { business_category: { contains: search, mode: 'insensitive' } },
          { city: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (business_type) {
        whereClause.business_type = business_type;
      }

      if (verification_status) {
        whereClause.verification_status = verification_status;
      }

      if (is_active !== null) {
        whereClause.is_active = is_active;
      }

      const [businessInfos, totalCount] = await Promise.all([
        prisma.business_informations.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: { created_at: 'desc' },
          include: {
            seller: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true
              }
            }
          }
        }),
        prisma.business_informations.count({
          where: whereClause
        })
      ]);

      return {
        data: businessInfos,
        meta: {
          total: totalCount,
          page,
          limit,
          totalPages: Math.ceil(totalCount / limit)
        }
      };
    } catch (error) {
      throw new Error(`Failed to get business information: ${error.message}`);
    }
  }

  /**
   * Update verification status (for admin)
   * @param {string} id - Business information ID
   * @param {string} status - Verification status
   * @returns {Promise<Object>} Updated business information
   */
  static async updateVerificationStatus(id, status) {
    try {
      const updateData = {
        verification_status: status
      };

      if (status === 'verified') {
        updateData.is_verified = true;
        updateData.verification_date = new Date();
      } else if (status === 'rejected') {
        updateData.is_verified = false;
        updateData.verification_date = null;
      }

      return await prisma.business_informations.update({
        where: { id },
        data: updateData,
        include: {
          seller: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true
            }
          }
        }
      });
    } catch (error) {
      throw new Error(`Failed to update verification status: ${error.message}`);
    }
  }
}

module.exports = BusinessInformationModel;
