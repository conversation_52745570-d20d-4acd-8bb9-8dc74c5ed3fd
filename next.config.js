/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  // Skip type checking during builds
  typescript: {
    ignoreBuildErrors: true,
  },
  // Disable image optimization
  images: {
    unoptimized: true,
  },
  // Disable static generation
  staticPageGenerationTimeout: 1,
  // Completely disable static generation
  output: 'standalone',
  // Disable runtime checks
  reactStrictMode: false,
  // Disable experimental features
  experimental: {
    // Allow server actions from any origin
    serverActions: {
      allowedOrigins: ['*'],
    },
  },
}

module.exports = nextConfig
