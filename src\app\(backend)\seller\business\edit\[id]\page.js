"use client";

import React, { useState, useEffect } from "react";
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useMutateApiMutation, useFetchApiQuery } from "@/redux/services/api";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import TextInput from "@/components/backend/TextInput";
import SelectInput from "@/components/backend/SelectInput";
import ImageUpload from "@/components/backend/ImageUpload";
import toast from 'react-hot-toast';
import { useRouter, useParams } from 'next/navigation';
import { handleUnauthorized } from '@/utils/auth';
import Image from 'next/image';
import { asseturl } from "@/config";

// Validation schema
const validationSchema = Yup.object({
  business_name: Yup.string().required('Business name is required'),
  short_description: Yup.string().required('Short description is required'),
  long_description: Yup.string().required('Long description is required'),
  address: Yup.string().required('Address is required'),
  city: Yup.string().required('City is required'),
  state: Yup.string().required('State is required'),
  country: Yup.string().required('Country is required'),
  postal_code: Yup.string().required('Postal code is required'),
  phone_number: Yup.string().required('Phone number is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  website_url: Yup.string().url('Invalid URL'),
  business_type: Yup.string().required('Business type is required'),
  business_category: Yup.string().required('Business category is required'),
  established_year: Yup.number()
    .min(1900, 'Year must be after 1900')
    .max(new Date().getFullYear(), 'Year cannot be in the future')
    .required('Established year is required'),
  employee_count: Yup.string().required('Employee count is required'),
  annual_revenue: Yup.string().required('Annual revenue is required'),
  business_license: Yup.string(),
  tax_id: Yup.string(),
});

const EditBusiness = () => {
  const router = useRouter();
  const params = useParams();
  const businessId = params.id;
  const [serverError, setServerError] = useState("");
  const [mutateApi] = useMutateApiMutation();

  // Fetch business information by ID
  const { data: businessData, error: businessError, isLoading } = useFetchApiQuery({
    endpoint: `/seller/business-information/${businessId}`,
    skip: !businessId,
  });

  // Handle 403 errors
  useEffect(() => {
    if (businessError?.status === 403) {
      handleUnauthorized(businessError, router);
    }
  }, [businessError, router]);

  const business = businessData?.data;

  // Options for dropdowns
  const businessTypeOptions = [
    { value: "Technology", label: "Technology" },
    { value: "Service", label: "Service" },
    { value: "Manufacturing", label: "Manufacturing" },
    { value: "Retail", label: "Retail" },
    { value: "Healthcare", label: "Healthcare" },
    { value: "Education", label: "Education" },
    { value: "Finance", label: "Finance" },
    { value: "Real Estate", label: "Real Estate" },
    { value: "Food & Beverage", label: "Food & Beverage" },
    { value: "Other", label: "Other" }
  ];

  const employeeCountOptions = [
    { value: "1-10", label: "1-10 employees" },
    { value: "11-50", label: "11-50 employees" },
    { value: "51-200", label: "51-200 employees" },
    { value: "201-500", label: "201-500 employees" },
    { value: "500+", label: "500+ employees" }
  ];

  const annualRevenueOptions = [
    { value: "Under $100K", label: "Under $100K" },
    { value: "$100K-$1M", label: "$100K-$1M" },
    { value: "$1M-$10M", label: "$1M-$10M" },
    { value: "$10M-$100M", label: "$10M-$100M" },
    { value: "$100M+", label: "$100M+" }
  ];

  const countryOptions = [
    { value: "USA", label: "United States" },
    { value: "Canada", label: "Canada" },
    { value: "UK", label: "United Kingdom" },
    { value: "Australia", label: "Australia" },
    { value: "Germany", label: "Germany" },
    { value: "France", label: "France" },
    { value: "India", label: "India" },
    { value: "Other", label: "Other" }
  ];

  // Get initial values from business data
  const getInitialValues = () => {
    if (!business) {
      return {
        business_name: "",
        short_description: "",
        long_description: "",
        logo: null,
        banner: null,
        address: "",
        city: "",
        state: "",
        country: "",
        postal_code: "",
        phone_number: "",
        email: "",
        website_url: "",
        business_type: "",
        business_category: "",
        established_year: "",
        employee_count: "",
        annual_revenue: "",
        business_license: "",
        tax_id: "",
        // Social media links
        facebook: "",
        twitter: "",
        linkedin: "",
        instagram: "",
        // Operating hours
        monday: "",
        tuesday: "",
        wednesday: "",
        thursday: "",
        friday: "",
        saturday: "",
        sunday: "",
        // Services and certifications
        services_offered: "",
        certifications: ""
      };
    }

    return {
      business_name: business.business_name || "",
      short_description: business.short_description || "",
      long_description: business.long_description || "",
      logo: null, // File uploads will be handled separately
      banner: null,
      address: business.address || "",
      city: business.city || "",
      state: business.state || "",
      country: business.country || "",
      postal_code: business.postal_code || "",
      phone_number: business.phone_number || "",
      email: business.email || "",
      website_url: business.website_url || "",
      business_type: business.business_type || "",
      business_category: business.business_category || "",
      established_year: business.established_year || "",
      employee_count: business.employee_count || "",
      annual_revenue: business.annual_revenue || "",
      business_license: business.business_license || "",
      tax_id: business.tax_id || "",
      // Social media links
      facebook: business.social_media_links?.facebook || "",
      twitter: business.social_media_links?.twitter || "",
      linkedin: business.social_media_links?.linkedin || "",
      instagram: business.social_media_links?.instagram || "",
      // Operating hours
      monday: business.operating_hours?.monday || "",
      tuesday: business.operating_hours?.tuesday || "",
      wednesday: business.operating_hours?.wednesday || "",
      thursday: business.operating_hours?.thursday || "",
      friday: business.operating_hours?.friday || "",
      saturday: business.operating_hours?.saturday || "",
      sunday: business.operating_hours?.sunday || "",
      // Services and certifications (convert arrays to comma-separated strings)
      services_offered: business.services_offered ? business.services_offered.join(', ') : "",
      certifications: business.certifications ? business.certifications.join(', ') : ""
    };
  };

  const handleImageChange = (files, setFieldValue, fieldName) => {
    if (files && files.length > 0) {
      setFieldValue(fieldName, files[0]);
    } else {
      setFieldValue(fieldName, null);
    }
  };

  const handleFormSubmit = (values, { setSubmitting, resetForm }) => {
    setServerError("");

    const formPayload = new FormData();

    // Append basic fields
    Object.keys(values).forEach(key => {
      if (values[key] !== null && values[key] !== undefined && values[key] !== "") {
        if (key === 'logo' || key === 'banner') {
          if (values[key] instanceof File) {
            formPayload.append(key, values[key]);
          }
        } else if (key.startsWith('facebook') || key.startsWith('twitter') ||
                   key.startsWith('linkedin') || key.startsWith('instagram')) {
          // Handle social media links - will be processed separately
        } else if (['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].includes(key)) {
          // Handle operating hours - will be processed separately
        } else if (key === 'services_offered' || key === 'certifications') {
          // Handle arrays - will be processed separately
        } else {
          formPayload.append(key, values[key]);
        }
      }
    });

    // Handle social media links
    const socialMediaLinks = {};
    if (values.facebook) socialMediaLinks.facebook = values.facebook;
    if (values.twitter) socialMediaLinks.twitter = values.twitter;
    if (values.linkedin) socialMediaLinks.linkedin = values.linkedin;
    if (values.instagram) socialMediaLinks.instagram = values.instagram;

    if (Object.keys(socialMediaLinks).length > 0) {
      formPayload.append('social_media_links', JSON.stringify(socialMediaLinks));
    }

    // Handle operating hours
    const operatingHours = {};
    ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].forEach(day => {
      if (values[day]) {
        operatingHours[day] = values[day];
      }
    });

    if (Object.keys(operatingHours).length > 0) {
      formPayload.append('operating_hours', JSON.stringify(operatingHours));
    }

    // Handle services offered (convert comma-separated string to array)
    if (values.services_offered) {
      const servicesArray = values.services_offered.split(',').map(s => s.trim()).filter(s => s);
      formPayload.append('services_offered', JSON.stringify(servicesArray));
    }

    // Handle certifications (convert comma-separated string to array)
    if (values.certifications) {
      const certificationsArray = values.certifications.split(',').map(c => c.trim()).filter(c => c);
      formPayload.append('certifications', JSON.stringify(certificationsArray));
    }

    const loadingToast = toast.loading('Updating business information...');

    mutateApi({
      endpoint: `/seller/business-information/${businessId}`,
      method: "PUT",
      data: formPayload,
    }).then((response) => {
      console.log(response);
      if (response?.data?.success) {
        toast.dismiss(loadingToast);
        toast.success('Business information updated successfully!');
        router.push('/seller/business');
      } else {
        throw new Error('Something went wrong');
      }
    }).catch((error) => {
      toast.dismiss(loadingToast);

      if (!handleUnauthorized(error, router)) {
        const errorMessage = error?.data?.message || 'Failed to update business information. Please try again.';
        setServerError(errorMessage);
        toast.error(errorMessage);
        setSubmitting(false);
      }
    });
  };

  if (isLoading) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </>
    );
  }

  if (businessError && businessError.status !== 403) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 className="text-red-800 font-medium">Error loading business details</h3>
            <p className="text-red-600 text-sm mt-1">
              {businessError?.data?.message || 'Failed to load business information'}
            </p>
          </div>
        </div>
      </>
    );
  }

  if (!business) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
            <h3 className="text-gray-800 font-medium">Business not found</h3>
            <p className="text-gray-600 text-sm mt-1">The requested business information could not be found.</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold text-[#343A40] text-xl poppins">
          Edit Business Information
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4 pb-5 border border-[#CACACA] rounded-2xl">
          <h3 className="font-semibold text-[#343A40] text-[32px] leading-normal archivo">
            Update Business Information
          </h3>

          <Formik
            initialValues={getInitialValues()}
            validationSchema={validationSchema}
            enableReinitialize={true}
            onSubmit={handleFormSubmit}
          >
            {({ values, setFieldValue, isSubmitting, errors, touched }) => {
              const errorFields = Object.keys(errors).filter(key => touched[key]);

              return (
                <Form>
                  {/* Server Error Message */}
                  {serverError && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <h4 className="text-red-700 font-medium">{serverError}</h4>
                      </div>
                    </div>
                  )}

                  {/* Validation Error Summary */}
                  {errorFields.length > 0 && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <h4 className="text-red-700 font-medium">Please fix the following errors:</h4>
                      </div>
                      <ul className="list-disc pl-5 text-red-600">
                        {errorFields.map(field => (
                          <li key={field} className="text-sm">
                            {errors[field]}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Basic Information Section */}
                  <div className="mb-8">
                    <h4 className="font-semibold text-[#343A40] text-lg mb-4">Basic Information</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Company Name"
                          placeholder="Enter business name"
                          name="business_name"
                          value={values.business_name}
                          onChange={(e) => setFieldValue('business_name', e.target.value)}
                          error={touched.business_name && errors.business_name}
                          required
                        />
                      </div>
                      <div className="">
                        <SelectInput
                          label="Business Type"
                          options={businessTypeOptions}
                          value={values.business_type}
                          onChange={(e) => setFieldValue('business_type', e.target.value)}
                          placeholder="Select business type"
                          className="w-full h-11 !font-light"
                          name="business_type"
                          error={touched.business_type && errors.business_type}
                          required
                        />
                      </div>
                      <div className="col-span-2">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Short Description"
                          placeholder="Brief description of your business"
                          name="short_description"
                          value={values.short_description}
                          onChange={(e) => setFieldValue('short_description', e.target.value)}
                          error={touched.short_description && errors.short_description}
                          required
                        />
                      </div>
                      <div className="col-span-2">
                        <TextInput
                          className="w-full !placeholder:text-[#6B7280] !font-light"
                          label="Long Description"
                          placeholder="Detailed description of your business"
                          name="long_description"
                          value={values.long_description}
                          onChange={(e) => setFieldValue('long_description', e.target.value)}
                          error={touched.long_description && errors.long_description}
                          multiline={true}
                          row="4"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  {/* Current Images Display */}
                  <div className="mb-8">
                    <h4 className="font-semibold text-[#343A40] text-lg mb-4">Current Images</h4>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      {business.logo_url && (
                        <div>
                          <p className="text-sm text-gray-600 mb-2">Current Logo:</p>
                          <Image
                            src={asseturl + business.logo_url}
                            alt={business.business_name}
                            width={128}
                            height={128}
                            className="w-32 h-32 object-cover rounded-lg border"
                          />
                        </div>
                      )}
                      {business.banner_url && (
                        <div>
                          <p className="text-sm text-gray-600 mb-2">Current Banner:</p>

                          <Image
                            src={ asseturl + business.banner_url}
                            alt={ business.business_name}
                            width={400}
                            height={128}
                            className="w-full h-32 object-cover rounded-lg border"
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Images Section */}
                  <div className="mb-8">
                    <h4 className="font-semibold text-[#343A40] text-lg mb-4">Update Images (Optional)</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="">
                        <ImageUpload
                          label="New Business Logo"
                          required={false}
                          handleChange={(e) => handleImageChange(e.target.files, setFieldValue, 'logo')}
                          maxImages={1}
                          className="mb-2"
                        />
                      </div>
                      <div className="">
                        <ImageUpload
                          label="New Business Banner"
                          required={false}
                          handleChange={(e) => handleImageChange(e.target.files, setFieldValue, 'banner')}
                          maxImages={1}
                          className="mb-2"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Contact Information Section */}
                  <div className="mb-8">
                    <h4 className="font-semibold text-[#343A40] text-lg mb-4">Contact Information</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="col-span-2">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Address"
                          placeholder="Enter business address"
                          name="address"
                          value={values.address}
                          onChange={(e) => setFieldValue('address', e.target.value)}
                          error={touched.address && errors.address}
                          required
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="City"
                          placeholder="Enter city"
                          name="city"
                          value={values.city}
                          onChange={(e) => setFieldValue('city', e.target.value)}
                          error={touched.city && errors.city}
                          required
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="State"
                          placeholder="Enter state"
                          name="state"
                          value={values.state}
                          onChange={(e) => setFieldValue('state', e.target.value)}
                          error={touched.state && errors.state}
                          required
                        />
                      </div>
                      <div className="">
                        <SelectInput
                          label="Country"
                          options={countryOptions}
                          value={values.country}
                          onChange={(e) => setFieldValue('country', e.target.value)}
                          placeholder="Select country"
                          className="w-full h-11 !font-light"
                          name="country"
                          error={touched.country && errors.country}
                          required
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Postal Code"
                          placeholder="Enter postal code"
                          name="postal_code"
                          value={values.postal_code}
                          onChange={(e) => setFieldValue('postal_code', e.target.value)}
                          error={touched.postal_code && errors.postal_code}
                          required
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Phone Number"
                          placeholder="Enter phone number"
                          name="phone_number"
                          value={values.phone_number}
                          onChange={(e) => setFieldValue('phone_number', e.target.value)}
                          error={touched.phone_number && errors.phone_number}
                          type="tel"
                          required
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Email"
                          placeholder="Enter business email"
                          name="email"
                          value={values.email}
                          onChange={(e) => setFieldValue('email', e.target.value)}
                          error={touched.email && errors.email}
                          type="email"
                          required
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Website URL"
                          placeholder="Enter website URL"
                          name="website_url"
                          value={values.website_url}
                          onChange={(e) => setFieldValue('website_url', e.target.value)}
                          error={touched.website_url && errors.website_url}
                          type="url"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Business Details Section */}
                  <div className="mb-8">
                    <h4 className="font-semibold text-[#343A40] text-lg mb-4">Business Details</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Business Category"
                          placeholder="e.g., Software Development"
                          name="business_category"
                          value={values.business_category}
                          onChange={(e) => setFieldValue('business_category', e.target.value)}
                          error={touched.business_category && errors.business_category}
                          required
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Established Year"
                          placeholder="e.g., 2020"
                          name="established_year"
                          value={values.established_year}
                          onChange={(e) => setFieldValue('established_year', e.target.value)}
                          error={touched.established_year && errors.established_year}
                          type="number"
                          required
                        />
                      </div>
                      <div className="">
                        <SelectInput
                          label="Employee Count"
                          options={employeeCountOptions}
                          value={values.employee_count}
                          onChange={(e) => setFieldValue('employee_count', e.target.value)}
                          placeholder="Select employee count"
                          className="w-full h-11 !font-light"
                          name="employee_count"
                          error={touched.employee_count && errors.employee_count}
                          required
                        />
                      </div>
                      <div className="">
                        <SelectInput
                          label="Annual Revenue"
                          options={annualRevenueOptions}
                          value={values.annual_revenue}
                          onChange={(e) => setFieldValue('annual_revenue', e.target.value)}
                          placeholder="Select annual revenue"
                          className="w-full h-11 !font-light"
                          name="annual_revenue"
                          error={touched.annual_revenue && errors.annual_revenue}
                          required
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Business License"
                          placeholder="Enter business license number"
                          name="business_license"
                          value={values.business_license}
                          onChange={(e) => setFieldValue('business_license', e.target.value)}
                          error={touched.business_license && errors.business_license}
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Tax ID"
                          placeholder="Enter tax identification number"
                          name="tax_id"
                          value={values.tax_id}
                          onChange={(e) => setFieldValue('tax_id', e.target.value)}
                          error={touched.tax_id && errors.tax_id}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Social Media Links Section */}
                  <div className="mb-8">
                    <h4 className="font-semibold text-[#343A40] text-lg mb-4">Social Media Links</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Facebook"
                          placeholder="https://facebook.com/yourpage"
                          name="facebook"
                          value={values.facebook}
                          onChange={(e) => setFieldValue('facebook', e.target.value)}
                          error={touched.facebook && errors.facebook}
                          type="url"
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Twitter"
                          placeholder="https://twitter.com/yourhandle"
                          name="twitter"
                          value={values.twitter}
                          onChange={(e) => setFieldValue('twitter', e.target.value)}
                          error={touched.twitter && errors.twitter}
                          type="url"
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="LinkedIn"
                          placeholder="https://linkedin.com/company/yourcompany"
                          name="linkedin"
                          value={values.linkedin}
                          onChange={(e) => setFieldValue('linkedin', e.target.value)}
                          error={touched.linkedin && errors.linkedin}
                          type="url"
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Instagram"
                          placeholder="https://instagram.com/yourpage"
                          name="instagram"
                          value={values.instagram}
                          onChange={(e) => setFieldValue('instagram', e.target.value)}
                          error={touched.instagram && errors.instagram}
                          type="url"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Operating Hours Section */}
                  <div className="mb-8">
                    <h4 className="font-semibold text-[#343A40] text-lg mb-4">Operating Hours</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Monday"
                          placeholder="e.g., 9:00 AM - 6:00 PM or Closed"
                          name="monday"
                          value={values.monday}
                          onChange={(e) => setFieldValue('monday', e.target.value)}
                          error={touched.monday && errors.monday}
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Tuesday"
                          placeholder="e.g., 9:00 AM - 6:00 PM or Closed"
                          name="tuesday"
                          value={values.tuesday}
                          onChange={(e) => setFieldValue('tuesday', e.target.value)}
                          error={touched.tuesday && errors.tuesday}
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Wednesday"
                          placeholder="e.g., 9:00 AM - 6:00 PM or Closed"
                          name="wednesday"
                          value={values.wednesday}
                          onChange={(e) => setFieldValue('wednesday', e.target.value)}
                          error={touched.wednesday && errors.wednesday}
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Thursday"
                          placeholder="e.g., 9:00 AM - 6:00 PM or Closed"
                          name="thursday"
                          value={values.thursday}
                          onChange={(e) => setFieldValue('thursday', e.target.value)}
                          error={touched.thursday && errors.thursday}
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Friday"
                          placeholder="e.g., 9:00 AM - 6:00 PM or Closed"
                          name="friday"
                          value={values.friday}
                          onChange={(e) => setFieldValue('friday', e.target.value)}
                          error={touched.friday && errors.friday}
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Saturday"
                          placeholder="e.g., 10:00 AM - 2:00 PM or Closed"
                          name="saturday"
                          value={values.saturday}
                          onChange={(e) => setFieldValue('saturday', e.target.value)}
                          error={touched.saturday && errors.saturday}
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                          label="Sunday"
                          placeholder="e.g., Closed"
                          name="sunday"
                          value={values.sunday}
                          onChange={(e) => setFieldValue('sunday', e.target.value)}
                          error={touched.sunday && errors.sunday}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Services and Certifications Section */}
                  <div className="mb-8">
                    <h4 className="font-semibold text-[#343A40] text-lg mb-4">Services & Certifications</h4>
                    <div className="grid grid-cols-1 gap-4">
                      <div className="">
                        <TextInput
                          className="w-full !placeholder:text-[#6B7280] !font-light"
                          label="Services Offered"
                          placeholder="Enter services separated by commas (e.g., Web Development, Mobile Apps, Cloud Solutions)"
                          name="services_offered"
                          value={values.services_offered}
                          onChange={(e) => setFieldValue('services_offered', e.target.value)}
                          error={touched.services_offered && errors.services_offered}
                          multiline={true}
                          row="3"
                        />
                      </div>
                      <div className="">
                        <TextInput
                          className="w-full !placeholder:text-[#6B7280] !font-light"
                          label="Certifications"
                          placeholder="Enter certifications separated by commas (e.g., ISO 9001, AWS Certified, Google Partner)"
                          name="certifications"
                          value={values.certifications}
                          onChange={(e) => setFieldValue('certifications', e.target.value)}
                          error={touched.certifications && errors.certifications}
                          multiline={true}
                          row="3"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mt-10 flex items-center space-x-4 justify-end">
                    <button
                      type="button"
                      className="py-2.5 px-5 text-sm font-medium text-[#374151] focus:outline-none bg-white rounded-lg border border-[#D1D5DB] inter"
                      onClick={() => router.push('/seller/business')}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="text-white bg-[#4F46E5] inter font-medium rounded-lg text-sm px-5 py-2.5 disabled:opacity-50"
                    >
                      {isSubmitting ? 'Updating...' : 'Update Business'}
                    </button>
                  </div>
                </Form>
              );
            }}
          </Formik>
        </div>
      </div>
    </>
  );
};

export default EditBusiness;
