"use client";

import React, { useState } from 'react';
import { useFetchApiQuery, useMutateApiMutation } from '@/redux/services/api';
import { subscriptionEndpoints } from '@/services/subscriptionService';
import toast from 'react-hot-toast';

const AvailablePlansModal = ({ isOpen, onClose, onSubscribe }) => {
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('credit_card');
  const [autoRenew, setAutoRenew] = useState(true);
  const [isSubscribing, setIsSubscribing] = useState(false);

  // Fetch available plans
  const { data: plansData, isLoading } = useFetchApiQuery({
    endpoint: subscriptionEndpoints.getAvailablePlans(),
    skip: !isOpen
  });

  // Subscribe mutation
  const [mutateApi] = useMutateApiMutation();

  const plans = plansData?.data || [];

  const handleSubscribe = async () => {
    if (!selectedPlan) {
      toast.error('Please select a plan');
      return;
    }

    setIsSubscribing(true);

    try {
      const subscriptionData = {
        subscription_id: selectedPlan.id,
        payment_method: paymentMethod,
        auto_renew: autoRenew
      };

      const response = await mutateApi({
        endpoint: subscriptionEndpoints.subscribe(),
        method: 'POST',
        data: subscriptionData
      }).unwrap();

      if (response.success) {
        toast.success('Successfully subscribed to plan!');
        onSubscribe();
        onClose();
      }
    } catch (error) {
      console.error('Subscription error:', error);
      toast.error(error?.data?.message || 'Failed to subscribe to plan');
    } finally {
      setIsSubscribing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Choose Your Plan</h2>
              <p className="text-gray-600 text-sm mt-1">Select a subscription plan that fits your needs</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading available plans...</p>
            </div>
          ) : plans.length > 0 ? (
            <>
              {/* Plans Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                {plans.map((plan) => (
                  <div
                    key={plan.id}
                    className={`border rounded-lg p-6 cursor-pointer transition-all ${
                      selectedPlan?.id === plan.id
                        ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                        : 'border-gray-200 hover:border-gray-300'
                    } ${plan.is_featured ? 'ring-2 ring-purple-200 border-purple-300' : ''}`}
                    onClick={() => setSelectedPlan(plan)}
                  >
                    {plan.is_featured && (
                      <div className="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded-full mb-3 inline-block">
                        Most Popular
                      </div>
                    )}
                    
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{plan.name}</h3>
                    <p className="text-gray-600 text-sm mb-4">{plan.description}</p>
                    
                    <div className="mb-4">
                      <span className="text-3xl font-bold text-gray-900">${plan.price}</span>
                      <span className="text-gray-600 text-sm">/{plan.duration_days} days</span>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Max Requests:</span>
                        <span className="font-medium">{plan.max_requests}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Max Offers:</span>
                        <span className="font-medium">{plan.max_offers}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Max Orders:</span>
                        <span className="font-medium">{plan.max_orders}</span>
                      </div>
                    </div>

                    <div className={`w-full py-2 px-4 rounded-lg text-center text-sm font-medium transition-colors ${
                      selectedPlan?.id === plan.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700'
                    }`}>
                      {selectedPlan?.id === plan.id ? 'Selected' : 'Select Plan'}
                    </div>
                  </div>
                ))}
              </div>

              {/* Subscription Options */}
              {selectedPlan && (
                <div className="border-t pt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Subscription Options</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Payment Method
                      </label>
                      <select
                        value={paymentMethod}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="credit_card">Credit Card</option>
                        <option value="debit_card">Debit Card</option>
                        <option value="paypal">PayPal</option>
                        <option value="bank_transfer">Bank Transfer</option>
                      </select>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="autoRenew"
                        checked={autoRenew}
                        onChange={(e) => setAutoRenew(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="autoRenew" className="ml-2 text-sm text-gray-700">
                        Enable auto-renewal
                      </label>
                    </div>
                  </div>

                  {/* Selected Plan Summary */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-6">
                    <h4 className="font-medium text-gray-900 mb-2">Order Summary</h4>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">{selectedPlan.name}</span>
                      <span className="font-medium">${selectedPlan.price}</span>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-gray-600 text-sm">Duration: {selectedPlan.duration_days} days</span>
                      <span className="text-sm text-gray-600">
                        Auto-renew: {autoRenew ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-600">No subscription plans available at the moment.</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t bg-gray-50">
          <div className="flex gap-3 justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            {selectedPlan && (
              <button
                onClick={handleSubscribe}
                disabled={isSubscribing}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSubscribing ? 'Subscribing...' : `Subscribe for $${selectedPlan.price}`}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvailablePlansModal;
