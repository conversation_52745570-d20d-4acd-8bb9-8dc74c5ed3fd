/**
 * Demo script showing how offers now return merged request children
 * This script demonstrates the enhanced functionality in a real-world scenario
 */

const { prisma } = require('../config/dbConfig');
const OfferModel = require('../models/offerModel');

async function demonstrateOfferMergedRequests() {
  console.log('🎯 Demonstrating Offer Merged Requests Functionality');
  console.log('====================================================\n');

  try {
    // Find an existing offer to demonstrate with
    console.log('🔍 Looking for existing offers...');
    const offers = await OfferModel.getAllOffers({}, 1, 5);
    
    if (offers.data.length === 0) {
      console.log('❌ No offers found in the database to demonstrate with');
      return;
    }

    console.log(`✅ Found ${offers.data.length} offers to examine\n`);

    // Examine each offer
    for (let i = 0; i < offers.data.length; i++) {
      const offer = offers.data[i];
      console.log(`📋 Offer ${i + 1}: ${offer.offer_title || offer.id}`);
      console.log(`   Request: ${offer.request.title}`);
      console.log(`   Seller: ${offer.seller.first_name} ${offer.seller.last_name}`);
      console.log(`   Price: $${offer.price}`);
      console.log(`   Status: ${offer.status}`);
      
      if (offer.request.is_merged) {
        console.log(`   🔗 This is a MERGED REQUEST with ${offer.request.merged_children.length} children:`);
        
        offer.request.merged_children.forEach((child, childIndex) => {
          console.log(`      Child ${childIndex + 1}: ${child.title}`);
          console.log(`         - Budget: $${child.budget_min} - $${child.budget_max}`);
          console.log(`         - Quantity: ${child.quantity}`);
          console.log(`         - Urgency: ${child.urgency}`);
          console.log(`         - Attachments: ${child.attachments?.length || 0}`);

          if (child.buyer) {
            console.log(`         - Buyer: ${child.buyer.first_name} ${child.buyer.last_name} (${child.buyer.email})`);
            if (child.buyer.business_name) {
              console.log(`         - Business: ${child.buyer.business_name}`);
            }
          }

          if (child.custom_fields && Object.keys(child.custom_fields).length > 0) {
            console.log(`         - Custom Fields: ${JSON.stringify(child.custom_fields)}`);
          }
        });
      } else {
        console.log(`   📄 This is a regular (non-merged) request`);
      }
      console.log('');
    }

    // Demonstrate getOfferById with detailed information
    console.log('🔍 Getting detailed information for the first offer...');
    const detailedOffer = await OfferModel.getOfferById(offers.data[0].id);
    
    if (detailedOffer) {
      console.log('\n📊 Detailed Offer Information:');
      console.log('==============================');
      console.log(`Offer ID: ${detailedOffer.id}`);
      console.log(`Offer Title: ${detailedOffer.offer_title || 'N/A'}`);
      console.log(`Price: $${detailedOffer.price}`);
      console.log(`Delivery Time: ${detailedOffer.delivery_time} days`);
      console.log(`Status: ${detailedOffer.status}`);
      
      console.log('\nRequest Information:');
      console.log(`Request ID: ${detailedOffer.request.id}`);
      console.log(`Request Title: ${detailedOffer.request.title}`);
      console.log(`Buyer: ${detailedOffer.request.buyer.first_name} ${detailedOffer.request.buyer.last_name}`);
      console.log(`Category: ${detailedOffer.request.category.title}`);
      console.log(`Subcategory: ${detailedOffer.request.sub_category.title}`);
      console.log(`Is Merged: ${detailedOffer.request.is_merged ? 'Yes' : 'No'}`);
      
      if (detailedOffer.request.is_merged && detailedOffer.request.merged_children) {
        console.log(`\n🔗 Merged Children (${detailedOffer.request.merged_children.length}):`);
        
        detailedOffer.request.merged_children.forEach((child, index) => {
          console.log(`\n   Child Request ${index + 1}:`);
          console.log(`   ├── ID: ${child.id}`);
          console.log(`   ├── Title: ${child.title}`);
          console.log(`   ├── Description: ${child.description}`);
          console.log(`   ├── Budget: $${child.budget_min} - $${child.budget_max}`);
          console.log(`   ├── Quantity: ${child.quantity}`);
          console.log(`   ├── Urgency: ${child.urgency}`);
          console.log(`   ├── Status: ${child.status}`);
          console.log(`   ├── Attachments: ${child.attachments?.length || 0}`);

          if (child.buyer) {
            console.log(`   ├── Buyer: ${child.buyer.first_name} ${child.buyer.last_name}`);
            console.log(`   ├── Buyer Email: ${child.buyer.email}`);
            console.log(`   ├── Buyer Phone: ${child.buyer.phone_number || 'N/A'}`);
            console.log(`   ├── Buyer Status: ${child.buyer.status} (Approved: ${child.buyer.is_approved})`);
            if (child.buyer.business_name) {
              console.log(`   ├── Business Name: ${child.buyer.business_name}`);
            }
          }

          if (child.attachments && child.attachments.length > 0) {
            console.log(`   ├── Attachment Files:`);
            child.attachments.forEach((att, attIndex) => {
              console.log(`   │   ${attIndex + 1}. ${att.file_path} (${att.file_type})`);
            });
          }

          if (child.custom_fields && Object.keys(child.custom_fields).length > 0) {
            console.log(`   └── Custom Fields:`);
            Object.entries(child.custom_fields).forEach(([key, value]) => {
              console.log(`       • ${key}: ${value}`);
            });
          } else {
            console.log(`   └── No custom fields`);
          }
        });
      }
      
      if (detailedOffer.offer_attachments && detailedOffer.offer_attachments.length > 0) {
        console.log(`\n📎 Offer Attachments (${detailedOffer.offer_attachments.length}):`);
        detailedOffer.offer_attachments.forEach((att, index) => {
          console.log(`   ${index + 1}. ${att.file_path} (${att.file_type})`);
        });
      }
    }

    console.log('\n🎉 Demonstration completed successfully!');
    console.log('\n📋 Key Features Demonstrated:');
    console.log('- ✅ Offers now include merged request information');
    console.log('- ✅ Child request details are returned with offers');
    console.log('- ✅ Child request attachments are included');
    console.log('- ✅ Custom fields from child requests are preserved');
    console.log('- ✅ Budget, quantity, and urgency details from children');
    console.log('- ✅ Buyer information for each child request included');
    console.log('- ✅ Buyer contact details and business information');
    console.log('- ✅ Both getAllOffers and getOfferById support merged requests');
    console.log('- ✅ Backward compatibility maintained for non-merged requests');

  } catch (error) {
    console.error('❌ Demonstration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the demonstration if this file is executed directly
if (require.main === module) {
  demonstrateOfferMergedRequests()
    .then(() => {
      console.log('\n✅ Demonstration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Demonstration failed:', error.message);
      process.exit(1);
    });
}

module.exports = { demonstrateOfferMergedRequests };
