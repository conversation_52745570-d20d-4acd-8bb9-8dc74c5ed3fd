"use client";

import React, { useEffect } from "react";
import { useFetchApiQuery } from "@/redux/services/api";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import { useRouter } from 'next/navigation';
import { handleUnauthorized } from '@/utils/auth';
import Link from 'next/link';
import Image from 'next/image';
import { asseturl } from "@/config";

const BusinessList = () => {
  const router = useRouter();

  // Fetch business information
  const { data: businessData, error: businessError, isLoading } = useFetchApiQuery({
    endpoint: "/seller/business-information",
    skip: false,
  });

  // Handle 403 errors
  useEffect(() => {
    if (businessError?.status === 403) {
      handleUnauthorized(businessError, router);
    }
  }, [businessError, router]);

  const businesses = businessData?.data?.data || [];

  const getVerificationBadge = (isVerified, verificationStatus) => {
    if (isVerified) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          Verified
        </span>
      );
    } else if (verificationStatus === 'pending') {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
          Pending
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          Not Verified
        </span>
      );
    }
  };

  const getStatusBadge = (isActive) => {
    return isActive ? (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        Active
      </span>
    ) : (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
        Inactive
      </span>
    );
  };

  if (isLoading) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <div className="flex justify-between items-center mb-5">
          <h5 className="font-semibold text-[#343A40] text-xl poppins">
            Business Information
          </h5>
          <Link
            href="/seller/business/add"
            className="bg-[#4F46E5] text-white px-4 py-2 rounded-lg font-medium text-sm hover:bg-[#4338CA] transition-colors"
          >
            Add New Business
          </Link>
        </div>

        {businesses.length === 0 ? (
          <div className="bg-white p-8 rounded-2xl border border-[#CACACA] text-center">
            <div className="mb-4">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No businesses found</h3>
            <p className="text-gray-500 mb-4">Get started by creating your first business profile.</p>
            <Link
              href="/seller/business/add"
              className="bg-[#4F46E5] text-white px-6 py-2 rounded-lg font-medium hover:bg-[#4338CA] transition-colors"
            >
              Add Business
            </Link>
          </div>
        ) : (
          <div className="grid gap-6">
            {businesses.map((business) => (
              <div key={business.id} className="bg-white p-6 rounded-2xl border border-[#CACACA] shadow-sm">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-4">
                    {business.logo_url && (
                      <div className="flex-shrink-0">
                        <Image
                          src={ asseturl + business.logo_url}
                          alt={business.business_name}
                          width={64}
                          height={64}
                          className="w-16 h-16 rounded-lg object-cover"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-[#343A40] mb-2">
                        {business.business_name}
                      </h3>
                      <p className="text-gray-600 mb-2">{business.short_description}</p>
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-sm text-gray-500">
                          {business.business_type} • {business.business_category}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getVerificationBadge(business.is_verified, business.verification_status)}
                        {getStatusBadge(business.is_active)}
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Link
                      href={`/seller/business/edit/${business.id}`}
                      className="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                      title="Edit Business"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </Link>
                    <Link
                      href={`/seller/business/view/${business.id}`}
                      className="text-green-600 hover:text-green-800 p-2 rounded-lg hover:bg-green-50 transition-colors"
                      title="View Business"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </Link>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Location:</span>
                    <p className="font-medium">{business.city}, {business.state}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Established:</span>
                    <p className="font-medium">{business.established_year}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Employees:</span>
                    <p className="font-medium">{business.employee_count}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Revenue:</span>
                    <p className="font-medium">{business.annual_revenue}</p>
                  </div>
                </div>

                {business.services_offered && business.services_offered.length > 0 && (
                  <div className="mt-4">
                    <span className="text-gray-500 text-sm">Services:</span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {business.services_offered.slice(0, 3).map((service, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {service}
                        </span>
                      ))}
                      {business.services_offered.length > 3 && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          +{business.services_offered.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                <div className="mt-4 pt-4 border-t border-gray-200 flex justify-between items-center text-sm text-gray-500">
                  <span>Created: {new Date(business.created_at).toLocaleDateString()}</span>
                  <span>Updated: {new Date(business.updated_at).toLocaleDateString()}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default BusinessList;
