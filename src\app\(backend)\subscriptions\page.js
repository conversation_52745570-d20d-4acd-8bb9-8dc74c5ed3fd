"use client";

import React, { useState, useEffect } from 'react';
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import { useFetchApiQuery, useMutateApiMutation } from "@/redux/services/api";
import { subscriptionEndpoints, formatSubscriptionData, getStatusColorClass } from "@/services/subscriptionService";
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import SubscriptionUsageCard from '@/components/backend/SubscriptionUsageCard';
import AvailablePlansModal from '@/components/backend/AvailablePlansModal';

const Subscriptions = () => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [showPlansModal, setShowPlansModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [subscriptionToCancel, setSubscriptionToCancel] = useState(null);

  // Fetch active subscription
  const { data: activeSubscriptionData, isLoading: isLoadingActive, refetch: refetchActive } = useFetchApiQuery({
    endpoint: subscriptionEndpoints.getActiveSubscription(),
    skip: false
  });

  // Fetch subscription history
  const { data: historyData, isLoading: isLoadingHistory, refetch: refetchHistory } = useFetchApiQuery({
    endpoint: subscriptionEndpoints.getSubscriptionHistory(currentPage, 10),
    skip: false
  });

  // Fetch usage status
  const { data: usageData, isLoading: isLoadingUsage, refetch: refetchUsage } = useFetchApiQuery({
    endpoint: subscriptionEndpoints.getUsageStatus(),
    skip: false
  });

  // Cancel subscription mutation
  const [cancelSubscription, { isLoading: isCanceling }] = useMutateApiMutation();

  const activeSubscription = activeSubscriptionData?.data;
  const subscriptionHistory = historyData?.data || [];
  const historyMeta = historyData?.meta;
  const usageInfo = usageData?.data;

  // Handle subscription cancellation
  const handleCancelSubscription = async () => {
    if (!subscriptionToCancel) return;

    try {
      const response = await cancelSubscription({
        endpoint: subscriptionEndpoints.cancelSubscription(subscriptionToCancel.id),
        method: 'PUT'
      }).unwrap();

      if (response.success) {
        toast.success('Subscription cancelled successfully');
        setShowCancelModal(false);
        setSubscriptionToCancel(null);
        refetchActive();
        refetchHistory();
        refetchUsage();
      }
    } catch (error) {
      console.error('Cancel subscription error:', error);
      toast.error(error?.data?.message || 'Failed to cancel subscription');
    }
  };

  const openCancelModal = (subscription) => {
    setSubscriptionToCancel(subscription);
    setShowCancelModal(true);
  };

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <div className="flex justify-between items-center mb-6">
          <h5 className="font-semibold capitalize text-[#343A40] text-xl poppins">
            My Subscriptions
          </h5>
          <Link href="/subscription-plans"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors cursor-pointer"
          >
            View Available Plans
          </Link>
        </div>

        {/* Usage Information */}
        {usageInfo && (
          <div className="mb-6">
            <SubscriptionUsageCard usageData={usageInfo} />
          </div>
        )}

        {/* Active Subscription */}
        {activeSubscription && (
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Current Subscription</h3>
                <p className="text-gray-600 text-sm">Your active subscription plan</p>
              </div>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColorClass(activeSubscription.status)}`}>
                {activeSubscription.status}
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <p className="text-sm font-medium text-gray-700">Plan Name</p>
                <p className="text-sm text-gray-900 mt-1">{activeSubscription.subscription?.name || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Price</p>
                <p className="text-sm text-gray-900 mt-1">${activeSubscription.subscription?.price || '0'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">End Date</p>
                <p className="text-sm text-gray-900 mt-1">
                  {activeSubscription.end_date ? new Date(activeSubscription.end_date).toLocaleDateString() : 'N/A'}
                </p>
              </div>
            </div>

            <div className="flex gap-2">
              <button
                onClick={() => setShowPlansModal(true)}
                className="text-xs inline-block font-medium px-3 py-1.5 transition-colors duration-300 rounded-sm text-blue-600 border border-blue-200 hover:bg-blue-50"
              >
                Upgrade Plan
              </button>
              {activeSubscription.status === 'ACTIVE' && (
                <button
                  onClick={() => openCancelModal(activeSubscription)}
                  className="text-xs inline-block font-medium px-3 py-1.5 transition-colors duration-300 rounded-sm text-red-600 border border-red-200 hover:bg-red-50"
                >
                  Cancel Subscription
                </button>
              )}
            </div>
          </div>
        )}

        {/* No Active Subscription */}
        {!isLoadingActive && !activeSubscription && (
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6 text-center">
            <div className="max-w-md mx-auto">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Active Subscription</h3>
              <p className="text-gray-600 text-sm mb-4">
                {"You don't have an active subscription. Choose a plan to unlock premium features."}
              </p>
            <Link href="/subscription-plans"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Choose a Plan
              </Link>
            </div>
          </div>
        )}

        {/* Subscription History */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <h3 className="text-lg font-semibold text-gray-900">Subscription History</h3>
            <p className="text-gray-600 text-sm">View your past and current subscriptions</p>
          </div>

          <div className="overflow-x-auto">
            {isLoadingHistory ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading subscription history...</p>
              </div>
            ) : subscriptionHistory.length > 0 ? (
              <table className="w-full text-sm text-left text-gray-500">
                <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3">Plan Name</th>
                    <th scope="col" className="px-6 py-3">Status</th>
                    <th scope="col" className="px-6 py-3">Start Date</th>
                    <th scope="col" className="px-6 py-3">End Date</th>
                    <th scope="col" className="px-6 py-3">Price</th>
                    <th scope="col" className="px-6 py-3">Auto Renew</th>
                  </tr>
                </thead>
                <tbody>
                  {subscriptionHistory.map((subscription, index) => (
                    <tr key={subscription.id} className="bg-white border-b hover:bg-gray-50">
                      <td className="px-6 py-4 font-medium text-gray-900">
                        {subscription.subscription?.name || 'N/A'}
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(subscription.status)}`}>
                          {subscription.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-gray-900">
                        {subscription.start_date ? new Date(subscription.start_date).toLocaleDateString() : 'N/A'}
                      </td>
                      <td className="px-6 py-4 text-gray-900">
                        {subscription.end_date ? new Date(subscription.end_date).toLocaleDateString() : 'N/A'}
                      </td>
                      <td className="px-6 py-4 text-gray-900">
                        ${subscription.subscription?.price || '0'}
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          subscription.auto_renew ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {subscription.auto_renew ? 'Yes' : 'No'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="p-8 text-center">
                <p className="text-gray-600">No subscription history found.</p>
              </div>
            )}
          </div>

          {/* Pagination */}
          {historyMeta && historyMeta.totalPages > 1 && (
            <div className="p-6 border-t">
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-700">
                  Showing page {historyMeta.page} of {historyMeta.totalPages} ({historyMeta.total} total)
                </p>
                <div className="flex gap-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(historyMeta.totalPages, prev + 1))}
                    disabled={currentPage === historyMeta.totalPages}
                    className="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Available Plans Modal */}
        {showPlansModal && (
          <AvailablePlansModal
            isOpen={showPlansModal}
            onClose={() => setShowPlansModal(false)}
            onSubscribe={() => {
              setShowPlansModal(false);
              refetchActive();
              refetchHistory();
              refetchUsage();
            }}
          />
        )}

        {/* Cancel Subscription Modal */}
        {showCancelModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancel Subscription</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to cancel your subscription? This action cannot be undone.
              </p>
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => {
                    setShowCancelModal(false);
                    setSubscriptionToCancel(null);
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Keep Subscription
                </button>
                <button
                  onClick={handleCancelSubscription}
                  disabled={isCanceling}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
                >
                  {isCanceling ? 'Canceling...' : 'Cancel Subscription'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Subscriptions;
