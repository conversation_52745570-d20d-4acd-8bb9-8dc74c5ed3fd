model categories {
  id            String    @id @default(uuid())
  title         String    @unique
  description   String?
  color         String?   // Hex color code for category
  image         String?   // URL to main category image
  thumbnail     String?   // URL to thumbnail image
  created_at    DateTime  @default(now())
  updated_at    DateTime  @updatedAt
  is_deleted    Boolean   @default(false)
  is_featured   Boolean   @default(false)
  is_premium   Boolean   @default(false)
  is_active   Boolean   @default(true)
  sort_order    Int?     

  // Relations
  sub_categories  sub_categories[] @relation("Category")
  requests        requests[]       @relation("RequestCategory")
  offers          offers[]         @relation("OfferCategory")
  products       products[]        @relation("ProductCategory")
  services       services[]        @relation("ServiceCategory")
  translations   category_translations[]
  seller_interests seller_interested_categories[] @relation("CategorySellerInterests")

  // Metadata
  seo_title       String?
  seo_description String?
  seo_keywords    String?
}

model category_translations {
  id          String   @id @default(uuid())
  category_id String
  language    String   // e.g., 'en', 'bn', 'fr'
  title       String
  description String?
  seo_title   String?
  seo_description String?
  seo_keywords String?

  // Relations
  category    categories @relation(fields: [category_id], references: [id])

  @@unique([category_id, language]) // Prevent duplicate translations
}