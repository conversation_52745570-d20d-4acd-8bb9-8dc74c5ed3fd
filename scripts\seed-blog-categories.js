/**
 * Seed script to create default blog categories
 * Run this script to populate the blog_categories table with default categories
 */

const { prisma } = require('../config/dbConfig');

const defaultCategories = [
  {
    name: 'Technology',
    slug: 'technology',
    description: 'Latest technology trends, tutorials, and insights for developers and tech enthusiasts',
    color: '#3B82F6',
    icon: 'fas fa-laptop-code',
    sort_order: 1,
    is_active: true
  },
  {
    name: 'Web Development',
    slug: 'web-development',
    description: 'Frontend and backend web development tutorials, frameworks, and best practices',
    color: '#10B981',
    icon: 'fas fa-code',
    sort_order: 2,
    is_active: true
  },
  {
    name: 'Mobile Development',
    slug: 'mobile-development',
    description: 'iOS, Android, and cross-platform mobile app development guides',
    color: '#8B5CF6',
    icon: 'fas fa-mobile-alt',
    sort_order: 3,
    is_active: true
  },
  {
    name: 'Design',
    slug: 'design',
    description: 'UI/UX design principles, tools, and creative inspiration',
    color: '#F59E0B',
    icon: 'fas fa-palette',
    sort_order: 4,
    is_active: true
  },
  {
    name: 'Business',
    slug: 'business',
    description: 'Business strategies, entrepreneurship, and industry insights',
    color: '#EF4444',
    icon: 'fas fa-chart-line',
    sort_order: 5,
    is_active: true
  },
  {
    name: 'Tutorials',
    slug: 'tutorials',
    description: 'Step-by-step guides and how-to articles for various topics',
    color: '#06B6D4',
    icon: 'fas fa-book-open',
    sort_order: 6,
    is_active: true
  },
  {
    name: 'News',
    slug: 'news',
    description: 'Latest news and updates from the tech and business world',
    color: '#84CC16',
    icon: 'fas fa-newspaper',
    sort_order: 7,
    is_active: true
  },
  {
    name: 'Tips & Tricks',
    slug: 'tips-tricks',
    description: 'Useful tips, tricks, and productivity hacks',
    color: '#F97316',
    icon: 'fas fa-lightbulb',
    sort_order: 8,
    is_active: true
  }
];

async function seedBlogCategories() {
  console.log('🌱 Seeding blog categories...\n');

  try {
    // Check if categories already exist
    const existingCategories = await prisma.blog_categories.findMany();
    
    if (existingCategories.length > 0) {
      console.log(`📋 Found ${existingCategories.length} existing categories:`);
      existingCategories.forEach(cat => {
        console.log(`   - ${cat.name} (${cat.slug})`);
      });
      console.log('\n❓ Do you want to continue and add more categories? (This will skip duplicates)');
    }

    let createdCount = 0;
    let skippedCount = 0;

    for (const categoryData of defaultCategories) {
      try {
        // Check if category already exists
        const existing = await prisma.blog_categories.findFirst({
          where: {
            OR: [
              { name: categoryData.name },
              { slug: categoryData.slug }
            ]
          }
        });

        if (existing) {
          console.log(`⏭️  Skipped: ${categoryData.name} (already exists)`);
          skippedCount++;
          continue;
        }

        // Create the category
        const category = await prisma.blog_categories.create({
          data: categoryData
        });

        console.log(`✅ Created: ${category.name} (${category.slug})`);
        createdCount++;

      } catch (error) {
        console.error(`❌ Failed to create ${categoryData.name}:`, error.message);
      }
    }

    console.log('\n📊 Summary:');
    console.log(`   ✅ Created: ${createdCount} categories`);
    console.log(`   ⏭️  Skipped: ${skippedCount} categories`);
    console.log(`   📋 Total categories in database: ${existingCategories.length + createdCount}`);

    if (createdCount > 0) {
      console.log('\n🎉 Blog categories seeded successfully!');
      console.log('\n💡 You can now use these category IDs when creating blog posts:');
      
      const allCategories = await prisma.blog_categories.findMany({
        orderBy: { sort_order: 'asc' }
      });

      allCategories.forEach(cat => {
        console.log(`   ${cat.name}: ${cat.id}`);
      });
    }

  } catch (error) {
    console.error('❌ Error seeding blog categories:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function main() {
  console.log('🚀 Blog Categories Seeder');
  console.log('========================\n');

  try {
    await seedBlogCategories();
  } catch (error) {
    console.error('\n❌ Seeding failed:', error);
    process.exit(1);
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  seedBlogCategories,
  defaultCategories
};
