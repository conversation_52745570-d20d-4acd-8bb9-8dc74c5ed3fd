import React, { useState } from "react";

const DeliveryOptions = () => {
  const [activeTab, setActiveTab] = useState(1); // Start with Promotions Management as active

  const tabs = [
    {
      id: 0,
      title: "Product Delivery",
      heading: "Provide Flexible Delivery Options",
      description:
        "Let your customers choose their preferred delivery options at a convenient time and place.",
      options: ["Takeaway", "Home Delivery", "Scheduled Delivery"],
      image: "/assets/frontend_assets/delivary-1.svg",
    },
    {
      id: 1,
      title: "Promotions Management",
      heading: "Boost Sales with Promotional Activities",
      description:
        "Manage various promotions to advertise & promote your business and to encourage customers to purchase more products.",
      options: [
        {
          main: "Campaigns",
          subs: ["Store-based Campaigns", "Product-based Campaigns"],
        },
        {
          main: "Promotional Banners",
        },
        {
          main: "Coupons",
          subs: [
            "Customer-wise",
            "Store-wise",
            "Zone-wise",
            "Free delivery",
            "First order",
          ],
        },
        {
          main: "Push Notifications",
        },
      ],
      image: "/assets/frontend_assets/delivary-2.webp",
    },
    {
      id: 2,
      title: "Retain Customers",
      heading: "Nurture Long-Term Customer Retention",
      description:
        "Allow your customers to avail wallet facility through your business. Sustain customers & encourage future purchases.",
      options: ["Loyalty Points", "Referral Bonus", "Refund Request"],
      image: "/assets/frontend_assets/delivary-3.webp",
    },
    {
      id: 3,
      title: "Provide Support",
      heading: "Maintain a Real-Time & Transparent Communication",
      description:
        "Stay connected with your customers through live-chat & emails.",
      image: "/assets/frontend_assets/delivary-4.webp",
    },
    {
      id: 4,
      title: "Dashboard & Reporting",
      heading: "Manage, Monitor, and Control Your Business",
      description:
        "Keep track of all your business modules' performance via dedicated module-based dashboards.",
      image: "/assets/frontend_assets/delivary-5.webp",
    },
  ];

  return (
    <section className="mb-10">
      <div className="max-w-7xl mx-auto px-4">
        <div className="delivery-options-wrapper flex flex-row gap-20 w-full">
          {/* Left Tab Menu - Desktop */}
          <div className="left-tab-menu w-[271px] d-lg-flex">
            {tabs.map((tab) => (
              <div
                key={tab.id}
                className={`left-menu-item ${
                  activeTab === tab.id ? "active" : ""
                }`}
                onClick={() => setActiveTab(tab.id)}
              >
                <span className="dm_sans">{tab.title}</span>
              </div>
            ))}
          </div>

          {/* Right Tab Content */}
          <div className="right-tab-content relative">
            <div className="tab-area absolute w-full top-0">
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  className="tab-item"
                  style={{ display: activeTab === tab.id ? "block" : "none" }}
                >
                  {/* Left Tab Menu - Mobile */}
                  <div className="left-tab-menu d-lg-none">
                    <div className="left-menu-item">
                      <span className="dm_sans">{tab.title}</span>
                    </div>
                  </div>

                  <div className="section-title">
                    <h2 className="title archivo">
                      {tab.heading.split(" ").map((word, i) =>
                        word.startsWith("<span") ? (
                          <span key={i} className="text-base">
                            {word.replace(/<\/?span[^>]*>/g, "")}
                          </span>
                        ) : (
                          <React.Fragment key={i}>{word} </React.Fragment>
                        )
                      )}
                    </h2>
                    <div className="max-w-xl mx-auto dm_sans">
                      {tab.description}
                    </div>
                  </div>

                  <div
                    className={`right-tab-bottom ${
                      tab.id === 1 ? "tab-two" : ""
                    }`}
                  >
                    {Array.isArray(tab.options) && (
                      <div
                        className={`delivery-options ${
                          tab.id === 1 ? "delivery-options-2" : ""
                        }`}
                      >
                        {tab.options.map((option, i) => {
                          if (typeof option === "string") {
                            return (
                              <div key={i} className="info-item">
                                <div className="inner">
                                  <span className="dm_sans">{option}</span>
                                </div>
                              </div>
                            );
                          }
                          if (typeof option === "object" && option !== null) {
                            return (
                              <div key={i} className="info-item">
                                <div className="inner">
                                  <span className="dm_sans">{option.main}</span>
                                </div>
                                {option.subs && (
                                  <div className="ps-3 d-flex flex-wrap column-gap-3 mt-3">
                                    {option.subs.map((sub, j) => (
                                      <div key={j} className="info-sub-item">
                                        <span className="dm_sans">{sub}</span>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            );
                          }
                          return null;
                        })}
                      </div>
                    )}

                    <div className={`thumb ${tab.id === 1 ? "thumb-2" : ""}`}>
                      <img
                        loading="eager"
                        decoding="async"
                        src={tab.image}
                        alt={`6amMart ${tab.title}`}
                        className={tab.id >= 3 ? "mw-100" : ""}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DeliveryOptions;
