"use client";

import React from 'react';
import SubscriptionPreview from '@/components/backend/SubscriptionPreview';

const SubscriptionDemo = () => {
  // Sample subscription data from your API response
  const sampleSubscriptionData = {
    "id": "24d4eb1c-b85e-4a01-9627-0a5a8fafc79e",
    "subscription_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "ACTIVE",
    "start_date": "2025-06-12T16:34:02.964Z",
    "end_date": "2025-07-03T16:34:02.964Z",
    "auto_renew": false,
    "plan": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "name": "Trial Plan",
      "description": "14-day trial subscription with basic features",
      "price": "0",
      "duration_days": 21,
      "max_requests": 10,
      "max_offers": 10,
      "max_orders": 5,
      "user_type": "BOTH",
      "features": {
        "trial": true,
        "support": "email",
        "priority": "low"
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Subscription Information Demo
          </h1>
          <p className="text-gray-600">
            This page demonstrates how subscription information will be displayed in the user profile.
          </p>
        </div>

        <div className="space-y-8">
          {/* Trial Plan Example */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Trial Plan Example
            </h2>
            <SubscriptionPreview subscriptionData={sampleSubscriptionData} />
          </div>

          {/* Premium Plan Example */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Premium Plan Example
            </h2>
            <SubscriptionPreview 
              subscriptionData={{
                ...sampleSubscriptionData,
                plan: {
                  ...sampleSubscriptionData.plan,
                  name: "Premium Plan",
                  description: "Full access to all features with priority support",
                  price: "29.99",
                  duration_days: 30,
                  max_requests: 100,
                  max_offers: 50,
                  max_orders: 25,
                  features: {
                    trial: false,
                    support: "priority",
                    priority: "high",
                    analytics: true,
                    api_access: true
                  }
                },
                auto_renew: true
              }} 
            />
          </div>

          {/* Expired Plan Example */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Expired Plan Example
            </h2>
            <SubscriptionPreview 
              subscriptionData={{
                ...sampleSubscriptionData,
                status: "EXPIRED",
                end_date: "2025-01-01T16:34:02.964Z",
                plan: {
                  ...sampleSubscriptionData.plan,
                  name: "Basic Plan",
                  description: "Basic features for small businesses",
                  price: "9.99",
                  features: {
                    trial: false,
                    support: "email",
                    priority: "normal"
                  }
                }
              }} 
            />
          </div>

          {/* No Subscription Example */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              No Subscription Example
            </h2>
            <SubscriptionPreview subscriptionData={null} />
          </div>
        </div>

        <div className="mt-12 p-6 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            Implementation Notes
          </h3>
          <ul className="text-blue-800 space-y-2 text-sm">
            <li>• Subscription data is stored in cookies after successful login</li>
            <li>• Trial plans are highlighted with a special "Trial" badge</li>
            <li>• Days remaining are calculated and shown for active subscriptions</li>
            <li>• Plan limits and features are displayed in an organized layout</li>
            <li>• Different status colors are used (Active: green, Expired: red, etc.)</li>
            <li>• Auto-renewal status is clearly indicated</li>
            <li>• Expiration warnings are shown when subscription is about to expire</li>
          </ul>
        </div>

        <div className="mt-8 text-center">
          <a 
            href="/profile" 
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            View Actual Profile Page
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionDemo;
