import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import Image from "next/image";
import React from "react";

const Chat = () => {
  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold capitalize text-[#343A40] text-xl poppins">
          My Chats{" "}
        </h5>
      </div>
      <div className="flex w-full relative overflow-x-auto drop-shadow-xs mt-5 bg-white">
        {/* Sidebar (Contact List) */}
        <div className="w-[326px]  flex flex-col">
          {/* Search Bar */}
          <div className="p-4">
            <div className="relative bg-[#dcdbdb] rounded-3xl">
              <input
                type="search"
                id="default-search"
                className="block w-full px-4 py-2 inter font-normal end-10 text-sm text-[#898F94] outline-0 rounded-lg bg-gray-50"
                placeholder="Search"
                required
              />
              <div className="absolute inset-y-0 end-3 flex items-center pointer-events-none">
                <svg
                  className="w-4 h-4 text-[#898F9466]"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 20 20"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                  />
                </svg>
              </div>
            </div>
          </div>
          {/* Contact List */}
          <div className="flex-1 p-4">
            {/* Contact Item */}
            <div className="flex items-center mb-2 last:mb-0 p-2 bg-[#D1E6F8] rounded-2xl cursor-pointer">
              <Image
                height={100}
                width={100}
                quality={100}
                src="/assets/backend_assets/images/chat-head.png"
                alt="Contact"
                className="w-[54px] h-[54px] rounded-full mr-3"
              />
              <div>
                <p className="font-medium inter text-black text-sm">
                  Contact seller
                </p>
                <p className="text-[10px] font-light text-[#898F94]">
                  Nice, I dont know why people get all worked up about hawaiian
                  pizza...
                </p>
              </div>
            </div>{" "}
            <div className="flex items-center mb-2 last:mb-0 p-2 bg-[#F9F9F9] rounded-2xl cursor-pointer">
              <Image
                height={100}
                width={100}
                quality={100}
                src="/assets/backend_assets/images/chat-head.png"
                alt="Contact"
                className="w-[54px] h-[54px] rounded-full mr-3"
              />
              <div>
                <p className="font-medium inter text-black text-sm">
                  Contact seller
                </p>
                <p className="text-[10px] font-light text-[#898F94]">
                  Nice, I dont know why people get all worked up about hawaiian
                  pizza...
                </p>
              </div>
            </div>
          </div>
        </div>
        {/* Chat Area */}
        <div className="flex-1 flex flex-col w-[calc(100%-326px)]">
          {/* Chat Header */}
          <div className="px-4 border-b border-[#A6A6A6] flex justify-between items-center">
            <div className="flex items-center p-2">
              <Image
                height={100}
                width={100}
                quality={100}
                src="/assets/backend_assets/images/chat-head.png"
                alt="Contact"
                className="w-[54px] h-[54px] rounded-full mr-3"
              />
              <div>
                <p className="font-medium inter text-black text-sm">
                  Contacted seller{" "}
                </p>
                <p className="text-[10px] flex items-center font-light text-[#898F94] mt-1">
                  <span className="flex w-2 h-2 me-2 bg-[#73C255] rounded-full"></span>
                  <span className="text-[#898F94] font-light text-xs inter">
                    Online
                  </span>
                </p>
              </div>
            </div>
            <button className=" text-[#656C73] px-4 py-2 rounded-lg flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                />
              </svg>
            </button>
          </div>
          {/* Chat Messages */}
          <div className="p-4">
            <div className="flex flex-col mt-5 ">
              <div className="flex justify-end mb-4">
                <div className="mr-2 py-3 px-6 bg-[#0C68F2] text-[12.5px] rounded-bl-3xl rounded-tl-3xl rounded-tr-xl text-white flex inter items-center">
                  start a new conversation{" "}
                  <svg
                    width={22}
                    height={22}
                    viewBox="0 0 22 22"
                    fill="none"
                    className="ml-2"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clipPath="url(#clip0_428_10273)">
                      <path
                        d="M1.79367 11.0135C1.83639 11.0132 1.87912 11.013 1.92313 11.0127C2.06625 11.012 2.20935 11.0118 2.35246 11.0117C2.4551 11.0113 2.55774 11.0109 2.66038 11.0105C2.93922 11.0094 3.21805 11.0088 3.49689 11.0085C3.67117 11.0082 3.84545 11.0079 4.01974 11.0075C4.50276 11.0065 4.98578 11.0056 5.46881 11.0053C5.4997 11.0053 5.53059 11.0053 5.56241 11.0053C5.59338 11.0053 5.62434 11.0052 5.65624 11.0052C5.71897 11.0052 5.78171 11.0051 5.84444 11.0051C5.87556 11.0051 5.90668 11.0051 5.93874 11.0051C6.44261 11.0047 6.94646 11.0033 7.45032 11.0013C7.96827 10.9993 8.48621 10.9983 9.00416 10.9982C9.29471 10.9981 9.58525 10.9976 9.8758 10.9961C10.1492 10.9947 10.4227 10.9946 10.6961 10.9953C10.7962 10.9954 10.8964 10.995 10.9965 10.9942C11.9752 10.9865 11.9752 10.9865 12.367 11.2761C12.4099 11.3075 12.4099 11.3075 12.4537 11.3396C12.7125 11.5628 12.8459 11.8896 12.8882 12.2238C12.8959 12.398 12.8972 12.5711 12.8959 12.7455C12.8962 12.8119 12.8965 12.8783 12.897 12.9447C12.8979 13.1237 12.8976 13.3026 12.8969 13.4815C12.8964 13.6697 12.8969 13.8578 12.8972 14.0459C12.8976 14.3618 12.8971 14.6776 12.8961 14.9934C12.895 15.3574 12.8953 15.7214 12.8965 16.0854C12.8974 16.3992 12.8976 16.7129 12.897 17.0266C12.8967 17.2135 12.8966 17.4003 12.8973 17.5872C12.8979 17.763 12.8975 17.9387 12.8963 18.1145C12.896 18.1786 12.8961 18.2427 12.8966 18.3068C12.8998 18.8038 12.8257 19.2126 12.4925 19.5983C12.4011 19.6706 12.4011 19.6706 12.3069 19.7316C12.2604 19.7622 12.2604 19.7622 12.2129 19.7935C11.9387 19.9512 11.6805 19.9819 11.3672 19.97C11.3392 19.969 11.3111 19.9681 11.2822 19.9671C11.221 19.9649 11.1598 19.9627 11.0986 19.9604C10.9279 19.9539 10.7573 19.9484 10.5866 19.9429C10.5502 19.9417 10.5138 19.9405 10.4763 19.9393C9.96476 19.923 9.45301 19.9155 8.94127 19.9078C8.5261 19.9013 8.11143 19.8917 7.69654 19.8747C5.63103 19.7049 5.63103 19.7049 3.82246 20.5071C3.47667 20.8409 3.18001 21.2159 2.92293 21.6216C2.87389 21.6893 2.87389 21.6893 2.79025 21.7311C2.63193 21.7532 2.47875 21.7658 2.3348 21.6888C2.22372 21.599 2.16656 21.5328 2.12113 21.3965C2.11758 21.2884 2.11628 21.1814 2.11704 21.0732C2.1171 21.0419 2.11716 21.0105 2.11722 20.9781C2.11746 20.878 2.11798 20.7779 2.11851 20.6777C2.11872 20.6098 2.11891 20.5419 2.11909 20.474C2.11955 20.3075 2.12027 20.1411 2.12113 19.9747C2.08144 19.9752 2.04175 19.9757 2.00087 19.9763C1.94853 19.9767 1.8962 19.9772 1.84227 19.9776C1.79051 19.9781 1.73875 19.9787 1.68542 19.9792C1.29033 19.9671 0.93892 19.8069 0.666254 19.5194C0.35427 19.1282 0.34626 18.7442 0.347797 18.2637C0.347558 18.1963 0.347267 18.1289 0.346927 18.0615C0.346179 17.8791 0.346226 17.6967 0.346464 17.5143C0.346594 17.3617 0.346339 17.2091 0.346087 17.0566C0.3455 16.6965 0.345564 16.3365 0.346041 15.9765C0.346521 15.6057 0.345932 15.2348 0.344811 14.864C0.343883 14.545 0.343607 14.2261 0.343853 13.9071C0.343993 13.7168 0.343894 13.5266 0.343173 13.3363C0.342524 13.1574 0.342693 12.9785 0.343478 12.7995C0.343632 12.7341 0.343504 12.6686 0.343065 12.6032C0.340915 12.2559 0.347335 11.9568 0.531974 11.6525C0.555701 11.6117 0.555702 11.6117 0.579909 11.57C0.848761 11.127 1.30997 11.0126 1.79367 11.0135ZM1.32655 12.1543C1.26876 12.2699 1.2792 12.3633 1.27886 12.4927C1.27862 12.5471 1.27838 12.6014 1.27813 12.6574C1.27808 12.718 1.27803 12.7786 1.27799 12.8392C1.2778 12.9027 1.27759 12.9663 1.27736 13.0298C1.27681 13.2027 1.27654 13.3756 1.27636 13.5485C1.27624 13.6565 1.27606 13.7645 1.27588 13.8724C1.27531 14.2102 1.2749 14.5479 1.27474 14.8856C1.27455 15.2757 1.27382 15.6658 1.27261 16.0558C1.2717 16.3572 1.27128 16.6585 1.27123 16.9599C1.27118 17.1399 1.27093 17.32 1.27019 17.5C1.2695 17.6694 1.26941 17.8388 1.26978 18.0082C1.26981 18.0704 1.26963 18.1325 1.26921 18.1947C1.26868 18.2796 1.269 18.3645 1.26936 18.4494C1.2693 18.4969 1.26924 18.5444 1.26918 18.5933C1.28678 18.7367 1.32392 18.8142 1.41019 18.9292C1.70941 19.1159 2.0583 19.0716 2.39965 19.0693C2.75199 19.0742 2.75199 19.0742 2.92632 19.1757C3.03098 19.3618 3.03199 19.5264 3.03332 19.7368C3.03437 19.7757 3.03543 19.8147 3.03651 19.8548C3.03896 19.9505 3.04049 20.0462 3.04116 20.1419C3.27417 19.9484 3.48759 19.741 3.69983 19.5251C3.76224 19.4616 3.82475 19.3982 3.88736 19.3349C3.91463 19.3072 3.94189 19.2794 3.96998 19.2508C4.13557 19.0943 4.239 19.0859 4.46154 19.0854C4.50626 19.0851 4.50626 19.0851 4.55189 19.0848C4.65205 19.0843 4.75221 19.0844 4.85237 19.0844C4.92409 19.0842 4.9958 19.0839 5.06752 19.0836C5.22209 19.0829 5.37667 19.0826 5.53125 19.0824C5.77568 19.0821 6.02012 19.0812 6.26455 19.0802C6.7836 19.0782 7.30265 19.0768 7.8217 19.0755C8.38159 19.0741 8.94147 19.0726 9.50136 19.0703C9.74432 19.0694 9.98728 19.0688 10.2302 19.0685C10.3815 19.0683 10.5327 19.0677 10.684 19.067C10.7541 19.0667 10.8242 19.0666 10.8943 19.0667C10.9901 19.0667 11.0858 19.0663 11.1816 19.0657C11.2352 19.0656 11.2888 19.0655 11.344 19.0653C11.5388 19.0509 11.6856 19.0163 11.8364 18.8899C11.9588 18.7403 11.9541 18.6097 11.9544 18.4232C11.9546 18.3699 11.9547 18.3165 11.9549 18.2615C11.9549 18.2023 11.9549 18.143 11.9549 18.0838C11.955 18.0215 11.9551 17.9592 11.9553 17.8969C11.9557 17.7275 11.9558 17.5582 11.9558 17.3888C11.9558 17.2475 11.956 17.1061 11.9561 16.9648C11.9565 16.6313 11.9566 16.2979 11.9565 15.9644C11.9565 15.6204 11.957 15.2763 11.9576 14.9323C11.9582 14.637 11.9584 14.3416 11.9584 14.0463C11.9584 13.8699 11.9585 13.6935 11.9589 13.5171C11.9593 13.3512 11.9593 13.1853 11.959 13.0193C11.959 12.9585 11.9591 12.8976 11.9593 12.8367C11.9596 12.7535 11.9594 12.6704 11.9591 12.5872C11.9592 12.5407 11.9592 12.4942 11.9592 12.4463C11.9438 12.2615 11.9039 12.1564 11.7808 12.0172C11.6029 11.9146 11.452 11.9219 11.25 11.9237C11.2088 11.9234 11.1677 11.9231 11.1253 11.9228C10.9872 11.9221 10.8492 11.9225 10.7111 11.9229C10.6122 11.9226 10.5133 11.9223 10.4144 11.9218C10.1456 11.9209 9.87676 11.921 9.60796 11.9214C9.44009 11.9216 9.27222 11.9215 9.10435 11.9212C9.07648 11.9212 9.04861 11.9212 9.01989 11.9211C8.96327 11.921 8.90665 11.921 8.85003 11.9209C8.3191 11.9202 7.78818 11.9205 7.25725 11.9213C6.7716 11.9219 6.28596 11.9213 5.80032 11.9201C5.30156 11.9188 4.80281 11.9184 4.30405 11.9188C4.24762 11.9188 4.1912 11.9188 4.13477 11.9189C4.09313 11.9189 4.09313 11.9189 4.05065 11.9189C3.85514 11.919 3.65963 11.9186 3.46412 11.918C3.20087 11.9173 2.93765 11.9175 2.6744 11.9185C2.57773 11.9187 2.48107 11.9186 2.3844 11.9181C2.25253 11.9174 2.12073 11.918 1.98886 11.919C1.95052 11.9185 1.91218 11.918 1.87267 11.9175C1.6181 11.9212 1.50275 11.9682 1.32655 12.1543Z"
                        fill="white"
                      />
                      <path
                        d="M11.2869 0.291912C11.3897 0.291697 11.4926 0.291393 11.5955 0.291006C11.8451 0.290313 12.0947 0.290591 12.3443 0.291344C12.5472 0.291932 12.7502 0.292093 12.9531 0.291953C12.9821 0.291933 13.0111 0.291913 13.0409 0.291892C13.0997 0.291849 13.1586 0.291805 13.2174 0.29176C13.7369 0.291402 14.2563 0.292097 14.7758 0.293342C15.2792 0.294548 15.7826 0.294844 16.286 0.294199C16.835 0.293498 17.3841 0.29333 17.9331 0.294062C17.9917 0.294139 18.0503 0.294216 18.1089 0.294292C18.1378 0.29433 18.1666 0.294368 18.1963 0.294408C18.3989 0.294642 18.6015 0.294504 18.8041 0.294227C19.077 0.293869 19.3499 0.294415 19.6228 0.295559C19.7229 0.295847 19.8229 0.295852 19.923 0.29555C20.0597 0.295186 20.1964 0.295855 20.3332 0.296782C20.3923 0.296265 20.3923 0.296265 20.4527 0.295738C20.8234 0.300364 21.1711 0.437118 21.4429 0.694684C21.6723 0.980067 21.7877 1.24118 21.7887 1.6113C21.7889 1.6394 21.7891 1.6675 21.7893 1.69644C21.7898 1.79005 21.7897 1.88365 21.7897 1.97725C21.7899 2.0446 21.7902 2.11194 21.7905 2.17928C21.7912 2.36186 21.7914 2.54444 21.7915 2.72702C21.7916 2.84122 21.7917 2.95543 21.792 3.06963C21.7927 3.46844 21.7931 3.86724 21.793 4.26605C21.793 4.63716 21.7938 5.00826 21.7952 5.37936C21.7963 5.69848 21.7967 6.01759 21.7966 6.33671C21.7966 6.52708 21.7969 6.71744 21.7977 6.90781C21.7986 7.08699 21.7986 7.26616 21.798 7.44535C21.7979 7.51085 21.7981 7.57636 21.7986 7.64187C21.8021 8.12713 21.7531 8.53186 21.4093 8.89873C21.0782 9.17385 20.7224 9.27431 20.3007 9.27037C20.2483 9.26999 20.1958 9.26961 20.1418 9.26922C20.102 9.26874 20.0621 9.26825 20.0211 9.26775C20.0217 9.30973 20.0224 9.35171 20.0231 9.39496C20.0253 9.55122 20.0268 9.70748 20.028 9.86376C20.0286 9.9313 20.0294 9.99883 20.0305 10.0664C20.0321 10.1636 20.0328 10.2609 20.0333 10.3582C20.034 10.3882 20.0346 10.4182 20.0353 10.4492C20.0354 10.6782 19.9782 10.8066 19.8277 10.9745C19.6653 11.0557 19.5288 11.0587 19.352 11.0242C19.1283 10.9011 18.9927 10.6985 18.8603 10.4878C18.5179 9.95518 18.0653 9.41157 17.4206 9.26474C16.4224 9.07729 15.3571 9.15871 14.348 9.19326C13.8911 9.20864 13.4341 9.21459 12.977 9.22054C12.51 9.22684 12.0431 9.23394 11.5763 9.24713C11.5049 9.24905 11.4336 9.25097 11.3623 9.25288C11.231 9.25641 11.0997 9.26054 10.9684 9.26502C10.4438 9.28064 10.0672 9.22627 9.66282 8.87831C9.40698 8.58099 9.26348 8.29353 9.26191 7.89485C9.26169 7.86716 9.26147 7.83946 9.26124 7.81093C9.26062 7.71867 9.26059 7.62643 9.26056 7.53417C9.26024 7.46779 9.25989 7.40141 9.2595 7.33503C9.25859 7.15503 9.25822 6.97504 9.258 6.79504C9.25785 6.68241 9.25759 6.56977 9.25729 6.45714C9.25627 6.06366 9.25572 5.67018 9.25557 5.27669C9.25542 4.91081 9.25417 4.54495 9.25242 4.17907C9.25097 3.86429 9.25032 3.5495 9.2503 3.23471C9.25026 3.047 9.2499 2.85931 9.24871 2.6716C9.24761 2.49482 9.24752 2.31806 9.24819 2.14127C9.24827 2.07674 9.24799 2.01221 9.24732 1.94769C9.24266 1.46956 9.30102 1.04322 9.63407 0.676388C10.1258 0.243414 10.6669 0.288623 11.2869 0.291912ZM10.2352 1.44744C10.1747 1.58201 10.1879 1.72825 10.1875 1.87447C10.1873 1.92782 10.1871 1.98118 10.1868 2.03615C10.1868 2.09518 10.1867 2.15421 10.1867 2.21325C10.1865 2.27549 10.1863 2.33773 10.186 2.39998C10.1855 2.56899 10.1852 2.73799 10.185 2.907C10.1849 3.01262 10.1847 3.11823 10.1846 3.22385C10.184 3.55436 10.1836 3.88488 10.1834 4.21539C10.1832 4.5968 10.1825 4.9782 10.1813 5.35961C10.1804 5.65454 10.18 5.94946 10.1799 6.24439C10.1799 6.4205 10.1796 6.5966 10.1789 6.7727C10.1782 6.93836 10.1781 7.10401 10.1785 7.26966C10.1785 7.33039 10.1783 7.39111 10.1779 7.45183C10.1774 7.53488 10.1777 7.61792 10.178 7.70097C10.178 7.7474 10.1779 7.79382 10.1779 7.84166C10.196 7.99303 10.2395 8.09258 10.3189 8.22226C10.5241 8.35911 10.711 8.3528 10.9536 8.3518C10.9834 8.35174 11.0131 8.35168 11.0438 8.35162C11.1433 8.35138 11.2428 8.35085 11.3423 8.35033C11.4138 8.35011 11.4852 8.34992 11.5567 8.34975C11.7105 8.34937 11.8643 8.3489 12.0181 8.34838C12.2615 8.34763 12.505 8.34755 12.7484 8.34764C12.7895 8.34766 12.8306 8.34768 12.873 8.34769C12.9996 8.34775 13.1262 8.34781 13.2529 8.34788C13.7769 8.34816 14.301 8.34796 14.825 8.34557C15.2074 8.34384 15.5898 8.34343 15.9721 8.34457C16.1744 8.34513 16.3765 8.34501 16.5787 8.34328C16.7691 8.34166 16.9594 8.34181 17.1497 8.34328C17.2193 8.34351 17.289 8.34311 17.3586 8.342C17.6918 8.33703 17.9513 8.33571 18.2141 8.56915C18.2741 8.63351 18.3329 8.69909 18.3901 8.76591C18.4516 8.82737 18.5135 8.88848 18.5758 8.94904C18.6334 9.00725 18.6908 9.06557 18.7482 9.124C18.8065 9.18339 18.8649 9.24269 18.9235 9.3019C18.9486 9.32753 18.9738 9.35317 18.9997 9.37958C19.0508 9.43978 19.0508 9.43978 19.101 9.43503C19.1012 9.41079 19.1014 9.38655 19.1016 9.36157C19.1029 9.25097 19.1059 9.14047 19.1089 9.0299C19.1092 8.97269 19.1092 8.97269 19.1096 8.91433C19.1154 8.7385 19.1287 8.62381 19.2159 8.46877C19.4312 8.34341 19.6457 8.36178 19.8904 8.3634C20.3308 8.37492 20.3308 8.37492 20.732 8.22226C20.8529 8.06087 20.8733 7.94317 20.874 7.74362C20.8747 7.66192 20.8747 7.66192 20.8754 7.57857C20.8753 7.5185 20.8752 7.45843 20.875 7.39837C20.8753 7.33488 20.8757 7.2714 20.8761 7.20791C20.877 7.0357 20.8772 6.86351 20.8771 6.6913C20.8771 6.54746 20.8774 6.40363 20.8778 6.2598C20.8786 5.92044 20.8787 5.58108 20.8784 5.24172C20.8781 4.89178 20.8791 4.54185 20.8807 4.19191C20.8819 3.8913 20.8824 3.59069 20.8822 3.29008C20.8822 3.1106 20.8824 2.93114 20.8834 2.75167C20.8843 2.58289 20.8843 2.41413 20.8834 2.24534C20.8833 2.18347 20.8835 2.1216 20.8841 2.05973C20.9013 1.69235 20.9013 1.69235 20.7565 1.36947C20.5677 1.21357 20.41 1.23203 20.1737 1.23191C20.1327 1.23172 20.0916 1.23154 20.0493 1.23135C19.9109 1.23079 19.7725 1.23053 19.634 1.23028C19.5351 1.22995 19.4362 1.2296 19.3373 1.22924C19.0399 1.22824 18.7425 1.2276 18.445 1.22706C18.361 1.22691 18.277 1.22674 18.193 1.22657C17.6716 1.22552 17.1501 1.22459 16.6286 1.22407C16.5078 1.22395 16.387 1.22383 16.2662 1.2237C16.2361 1.22367 16.2061 1.22364 16.1752 1.22361C15.6887 1.22308 15.2022 1.22169 14.7158 1.21995C14.2165 1.21817 13.7171 1.21719 13.2178 1.21697C12.9374 1.21682 12.657 1.21633 12.3766 1.21496C12.1129 1.2137 11.8491 1.21343 11.5854 1.21393C11.4885 1.21394 11.3916 1.21359 11.2948 1.21285C11.1626 1.21191 11.0305 1.21225 10.8984 1.21292C10.86 1.21233 10.8215 1.21175 10.7819 1.21116C10.528 1.21431 10.4073 1.25666 10.2352 1.44744Z"
                        fill="white"
                      />
                      <path
                        d="M19.0949 11.9053C19.3985 11.9053 19.7021 11.9053 20.0149 11.9053C20.0177 12.2688 20.02 12.6324 20.0214 12.9959C20.0221 13.1648 20.0229 13.3336 20.0244 13.5025C20.0257 13.6657 20.0265 13.829 20.0268 13.9922C20.027 14.0542 20.0275 14.1162 20.0282 14.1782C20.0336 14.6972 19.985 15.1363 19.6385 15.5436C19.3119 15.8476 18.9604 15.9674 18.5173 15.9665C18.4896 15.9666 18.4618 15.9666 18.4332 15.9666C18.342 15.9665 18.2508 15.9662 18.1596 15.9659C18.0962 15.9658 18.0327 15.9657 17.9693 15.9657C17.8027 15.9655 17.6361 15.9651 17.4695 15.9647C17.2994 15.9642 17.1292 15.9641 16.959 15.9638C16.6255 15.9634 16.2919 15.9627 15.9584 15.9618C15.9584 15.6582 15.9584 15.3546 15.9584 15.0418C16.0379 15.0412 16.1175 15.0405 16.1994 15.0399C16.463 15.0377 16.7266 15.035 16.9901 15.0319C17.1498 15.03 17.3095 15.0284 17.4692 15.0273C17.6235 15.0262 17.7778 15.0245 17.9321 15.0224C17.9908 15.0217 18.0495 15.0212 18.1082 15.021C18.5399 15.0315 18.5399 15.0315 18.9276 14.8745C19.1109 14.608 19.073 14.2799 19.0744 13.9677C19.0751 13.9081 19.0759 13.8485 19.0767 13.7889C19.0787 13.6329 19.0799 13.4769 19.081 13.3209C19.0823 13.1614 19.0842 13.0019 19.0861 12.8424C19.0897 12.53 19.0925 12.2177 19.0949 11.9053Z"
                        fill="white"
                      />
                      <path
                        d="M3.47053 4.32426C3.51464 4.32424 3.51464 4.32424 3.55964 4.32422C3.65604 4.32428 3.75242 4.32493 3.84881 4.32558C3.91596 4.32574 3.98311 4.32586 4.05026 4.32594C4.2264 4.32626 4.40253 4.32708 4.57867 4.32801C4.75865 4.32887 4.93862 4.32925 5.1186 4.32967C5.47129 4.33056 5.82398 4.33203 6.17667 4.33375C6.17667 4.63736 6.17667 4.94098 6.17667 5.25379C6.09289 5.25462 6.00911 5.25545 5.92278 5.2563C5.64569 5.2591 5.36861 5.26235 5.09153 5.26581C4.92356 5.2679 4.7556 5.26984 4.58763 5.27143C4.42548 5.27297 4.26333 5.27491 4.10118 5.27713C4.03937 5.2779 3.97757 5.27853 3.91576 5.27902C3.82906 5.27973 3.74238 5.28094 3.65569 5.28227C3.60638 5.28283 3.55707 5.28339 3.50626 5.28396C3.36904 5.29611 3.28188 5.30452 3.16564 5.37925C3.0888 5.56997 3.0711 5.74673 3.06867 5.95091C3.06784 6.00447 3.06701 6.05804 3.06615 6.11322C3.06566 6.17083 3.06517 6.22844 3.06469 6.28605C3.06391 6.34564 3.06309 6.40523 3.06223 6.46482C3.0601 6.62081 3.05844 6.7768 3.05688 6.9328C3.0552 7.09232 3.05306 7.25183 3.05097 7.41135C3.04693 7.72371 3.04344 8.03608 3.04018 8.34846C2.73657 8.34846 2.43296 8.34846 2.12015 8.34846C2.1173 7.9849 2.11501 7.62135 2.11365 7.25779C2.113 7.08893 2.11211 6.92009 2.11068 6.75124C2.10931 6.588 2.10857 6.42477 2.10824 6.26153C2.10801 6.19954 2.10757 6.13756 2.10688 6.07557C2.10131 5.54787 2.14425 5.13327 2.5096 4.72843C2.80868 4.45716 3.05471 4.3228 3.47053 4.32426Z"
                        fill="white"
                      />
                      <path
                        d="M14.1636 15.0391C14.4672 15.0391 14.7708 15.0391 15.0836 15.0391C15.0836 15.3427 15.0836 15.6463 15.0836 15.9591C14.78 15.9591 14.4764 15.9591 14.1636 15.9591C14.1636 15.6555 14.1636 15.3519 14.1636 15.0391Z"
                        fill="white"
                      />
                      <path
                        d="M7.93604 15.0391C8.23965 15.0391 8.54326 15.0391 8.85607 15.0391C8.85607 15.3427 8.85607 15.6463 8.85607 15.9591C8.55246 15.9591 8.24885 15.9591 7.93604 15.9591C7.93604 15.6555 7.93604 15.3519 7.93604 15.0391Z"
                        fill="white"
                      />
                      <path
                        d="M6.1333 15.0391C6.43691 15.0391 6.74052 15.0391 7.05334 15.0391C7.05334 15.3427 7.05334 15.6463 7.05334 15.9591C6.74973 15.9591 6.44611 15.9591 6.1333 15.9591C6.1333 15.6555 6.1333 15.3519 6.1333 15.0391Z"
                        fill="white"
                      />
                      <path
                        d="M4.37744 15.0391C4.68105 15.0391 4.98467 15.0391 5.29748 15.0391C5.29748 15.3427 5.29748 15.6463 5.29748 15.9591C4.99387 15.9591 4.69025 15.9591 4.37744 15.9591C4.37744 15.6555 4.37744 15.3519 4.37744 15.0391Z"
                        fill="white"
                      />
                      <path
                        d="M16.8402 4.33203C17.1438 4.33203 17.4474 4.33203 17.7602 4.33203C17.7602 4.63564 17.7602 4.93926 17.7602 5.25207C17.4566 5.25207 17.153 5.25207 16.8402 5.25207C16.8402 4.94846 16.8402 4.64484 16.8402 4.33203Z"
                        fill="white"
                      />
                      <path
                        d="M15.0765 4.33203C15.3802 4.33203 15.6838 4.33203 15.9966 4.33203C15.9966 4.63564 15.9966 4.93926 15.9966 5.25207C15.693 5.25207 15.3894 5.25207 15.0765 5.25207C15.0765 4.94846 15.0765 4.64484 15.0765 4.33203Z"
                        fill="white"
                      />
                      <path
                        d="M13.2816 4.33203C13.5852 4.33203 13.8888 4.33203 14.2017 4.33203C14.2017 4.63564 14.2017 4.93926 14.2017 5.25207C13.898 5.25207 13.5944 5.25207 13.2816 5.25207C13.2816 4.94846 13.2816 4.64484 13.2816 4.33203Z"
                        fill="white"
                      />
                      <path
                        d="M7.0542 4.33203C7.35781 4.33203 7.66142 4.33203 7.97424 4.33203C7.97424 4.63564 7.97424 4.93926 7.97424 5.25207C7.67062 5.25207 7.36701 5.25207 7.0542 5.25207C7.0542 4.94846 7.0542 4.64484 7.0542 4.33203Z"
                        fill="white"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_428_10273">
                        <rect
                          width="21.4118"
                          height="21.4118"
                          fill="white"
                          transform="translate(0.356445 0.31543)"
                        />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
              </div>
              <div className="flex justify-start mb-4 max-w-[474px]">
                <div className="ml-2 py-3 px-4 bg-[#F8F8F8] rounded-[15px] inter font-normal text-xs text-[#656C73]">
                  <p className="">
                    HelloThank you for your application.As the next step, you
                    are now required to submit an assignment for the evaluation
                    of your candidature.Deadline: 6 DecWhat to
                    do?--------------Aviate is a platform which not only
                    provides industry leading recommendation for job seekers but
                    also helps candidates to prepare for the selection process
                    by conducting orientation sessions and mock interviews.
                    Design a dashboard for the candidates which allows them to
                    view their progress.The dashboard should be able to cover
                    all the relevant information like:- List of jobs, candidate
                    has already applied for.- Timeline/Info of all the relevant
                    event associated with all applied job (events like
                    Orientation sessions, mock interviews, interview round
                    1/2/3,. etc)- Application status for all applied jobs.
                    (Applied, in progress, rejected, selected,. etc)- List of
                    jobs that are recommended to the candidateNote: Think about
                    all possible questions a candidates might have during/after
                    they apply for a job, and make a dashboard that answers all
                    such questions.You will be assessed on the basis of:-
                    Creativity and Consistency.- Presentation skills.-
                    Aesthetics of the website.- Use of vectors and
                    illustrations.- Understanding of modern web elements and
                    design language.- Animations and effects.
                  </p>

                  <p className="inter text-[10px] text-[#898F94] font-normal text-right py-2">
                    Today, 8:30 am
                  </p>
                </div>
              </div>
            </div>

            {/* chat input field */}
            <form className="w-full flex items-center mx-auto">
              <div className="relative w-full">
                <input
                  type="text"
                  className="block w-full ps-5 rounded-lg outline-0 flex-1 bg-[#F5F5F5] inter text-xs text-[#2B2A28] p-3"
                  placeholder="Hello! Welcome to Marktzroom. How can I help you?"
                  required
                />
                <div className=" w-[22px] h-[22px] justify-center absolute end-17 top-1/2 -translate-y-1/2 bg-[#FFFFFF] flex items-center">
                  <label className="flex flex-col justify-center w-full items-center text-blue tracking-wide uppercase cursor-pointer text-white h-full">
                    <svg
                      width={15}
                      height={13}
                      viewBox="0 0 15 13"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2.15683 12.0594C1.8297 12.0594 1.54976 11.9431 1.31701 11.7103C1.08426 11.4776 0.967682 11.1974 0.967285 10.8699V3.13787H2.15683V10.8699H12.2679V12.0594H2.15683ZM4.53591 9.68036C4.20879 9.68036 3.92885 9.56398 3.6961 9.33123C3.46334 9.09847 3.34677 8.81834 3.34637 8.49081V1.94833C3.34637 1.62121 3.46295 1.34127 3.6961 1.10851C3.92925 0.875761 4.20919 0.759186 4.53591 0.758789H7.50977L8.69931 1.94833H12.8627C13.1898 1.94833 13.47 2.06491 13.7031 2.29806C13.9363 2.53121 14.0526 2.81115 14.0523 3.13787V8.49081C14.0523 8.81794 13.9359 9.09808 13.7031 9.33123C13.4704 9.56438 13.1902 9.68075 12.8627 9.68036H4.53591ZM5.72546 7.30127H11.6732L9.62121 4.6248L8.25323 6.40912L7.33134 5.21957L5.72546 7.30127Z"
                        fill="#0066FF"
                      />
                    </svg>

                    <input type="file" className="hidden" />
                  </label>
                </div>
                <div className=" w-[22px] h-[22px] justify-center absolute end-11 top-1/2 -translate-y-1/2 bg-[#FFFFFF] flex items-center">
                  <label className="flex flex-col justify-center w-full items-center text-blue tracking-wide uppercase cursor-pointer text-[#2B2A28] h-full">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="size-3"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625ZM7.5 15a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5A.75.75 0 0 1 7.5 15Zm.75 2.25a.75.75 0 0 0 0 1.5H12a.75.75 0 0 0 0-1.5H8.25Z"
                        clipRule="evenodd"
                      />
                      <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                    </svg>

                    <input type="file" className="hidden" />
                  </label>
                </div>
                <div className=" w-[22px] h-[22px] justify-center absolute end-5 top-1/2 -translate-y-1/2 bg-[#FFFFFF] flex items-center">
                  <label className="flex flex-col justify-center w-full items-center text-blue tracking-wide uppercase cursor-pointer text-[#2B2A28] h-full">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="size-3"
                    >
                      <path d="M8.25 4.5a3.75 3.75 0 1 1 7.5 0v8.25a3.75 3.75 0 1 1-7.5 0V4.5Z" />
                      <path d="M6 10.5a.75.75 0 0 1 .75.75v1.5a5.25 5.25 0 1 0 10.5 0v-1.5a.75.75 0 0 1 1.5 0v1.5a6.751 6.751 0 0 1-6 6.709v2.291h3a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3v-2.291a6.751 6.751 0 0 1-6-6.709v-1.5A.75.75 0 0 1 6 10.5Z" />
                    </svg>

                    <input type="file" className="hidden" />
                  </label>
                </div>
              </div>
              <button
                type="submit"
                className="p-2.5 ms-2 text-sm font-medium w-[42px] cursor-pointer text-white bg-blue-700 rounded-lg border border-blue-700"
              >
                <svg
                  className="size-4"
                  viewBox="0 0 19 19"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.5257 0.778306C17.4321 0.461591 18.3028 1.33234 17.9861 2.23877L12.7001 17.343C12.3566 18.3226 10.9916 18.3779 10.5705 17.4295L8.01984 11.6912L11.6099 8.10024C11.7281 7.97339 11.7924 7.80563 11.7894 7.63228C11.7863 7.45893 11.7161 7.29354 11.5935 7.17095C11.4709 7.04835 11.3055 6.97813 11.1321 6.97507C10.9588 6.97201 10.791 7.03636 10.6642 7.15455L7.07326 10.7446L1.33491 8.19391C0.386547 7.77192 0.442752 6.40782 1.42145 6.06434L16.5257 0.778306Z"
                    fill="white"
                  />
                </svg>
              </button>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default Chat;
