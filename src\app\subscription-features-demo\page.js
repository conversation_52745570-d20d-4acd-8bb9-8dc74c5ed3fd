"use client";

import React from 'react';
import Link from 'next/link';

const SubscriptionFeaturesDemo = () => {
  const features = [
    {
      title: "Subscription Management",
      description: "Complete subscription lifecycle management for both buyers and sellers",
      items: [
        "View active subscription details",
        "Browse available subscription plans",
        "Subscribe to new plans",
        "Cancel existing subscriptions",
        "View subscription history with pagination",
        "Auto-renewal management"
      ],
      link: "/subscriptions",
      color: "blue"
    },
    {
      title: "Usage Tracking & Limits",
      description: "Real-time usage monitoring with visual progress indicators",
      items: [
        "Track requests, offers, and orders usage",
        "Visual progress bars with color coding",
        "Usage warnings when approaching limits",
        "Remaining quota calculations",
        "Plan limit comparisons",
        "Expiration warnings"
      ],
      link: "/subscription-usage",
      color: "green"
    },
    {
      title: "Plan Selection & Comparison",
      description: "Browse and compare available subscription plans",
      items: [
        "Featured plan highlighting",
        "Detailed plan comparisons",
        "Pricing and duration information",
        "Feature lists and limitations",
        "Current plan indicators",
        "Upgrade/downgrade options"
      ],
      link: "/subscription-plans",
      color: "purple"
    },
    {
      title: "API Integration",
      description: "Comprehensive API integration for both buyer and seller endpoints",
      items: [
        "Role-based endpoint routing",
        "Available plans fetching",
        "Subscription creation",
        "Active subscription retrieval",
        "Usage status monitoring",
        "Subscription cancellation"
      ],
      link: "/subscription-demo",
      color: "indigo"
    }
  ];

  const apiEndpoints = [
    {
      method: "GET",
      endpoint: "/api/{role}/subscriptions/available",
      description: "Get available subscription plans"
    },
    {
      method: "POST",
      endpoint: "/api/{role}/subscriptions/subscribe",
      description: "Subscribe to a subscription plan"
    },
    {
      method: "GET",
      endpoint: "/api/{role}/subscriptions/active",
      description: "Get user's active subscription"
    },
    {
      method: "GET",
      endpoint: "/api/{role}/subscriptions/history",
      description: "Get subscription history with pagination"
    },
    {
      method: "GET",
      endpoint: "/api/{role}/subscriptions/usage",
      description: "Get current usage status"
    },
    {
      method: "PUT",
      endpoint: "/api/{role}/subscriptions/{id}/cancel",
      description: "Cancel user's subscription"
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: "bg-blue-50 border-blue-200 text-blue-800",
      green: "bg-green-50 border-green-200 text-green-800",
      purple: "bg-purple-50 border-purple-200 text-purple-800",
      indigo: "bg-indigo-50 border-indigo-200 text-indigo-800"
    };
    return colors[color] || colors.blue;
  };

  const getButtonClasses = (color) => {
    const colors = {
      blue: "bg-blue-600 hover:bg-blue-700",
      green: "bg-green-600 hover:bg-green-700",
      purple: "bg-purple-600 hover:bg-purple-700",
      indigo: "bg-indigo-600 hover:bg-indigo-700"
    };
    return colors[color] || colors.blue;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Subscription Management System
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Complete subscription management solution for both buyers and sellers with 
            real-time usage tracking, plan management, and comprehensive API integration.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`rounded-lg border-2 p-6 ${getColorClasses(feature.color)}`}
            >
              <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
              <p className="text-sm mb-4 opacity-80">{feature.description}</p>
              
              <ul className="space-y-2 mb-6">
                {feature.items.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start text-sm">
                    <svg className="w-4 h-4 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {item}
                  </li>
                ))}
              </ul>

              <Link
                href={feature.link}
                className={`inline-block text-white px-4 py-2 rounded-lg font-medium transition-colors ${getButtonClasses(feature.color)}`}
              >
                View Demo
              </Link>
            </div>
          ))}
        </div>

        {/* API Documentation */}
        <div className="bg-white rounded-lg shadow-sm border p-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">API Endpoints</h2>
          <p className="text-gray-600 mb-6">
            All endpoints support both buyer and seller roles. Replace <code className="bg-gray-100 px-2 py-1 rounded">{"{role}"}</code> with either <code className="bg-gray-100 px-2 py-1 rounded">buyer</code> or <code className="bg-gray-100 px-2 py-1 rounded">seller</code>.
          </p>
          
          <div className="grid grid-cols-1 gap-4">
            {apiEndpoints.map((endpoint, index) => (
              <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                      endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                      endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {endpoint.method}
                    </span>
                    <code className="text-sm font-mono text-gray-700">{endpoint.endpoint}</code>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-2">{endpoint.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Implementation Notes */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">Implementation Highlights</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-blue-800">
            <div>
              <h4 className="font-medium mb-2">Frontend Features</h4>
              <ul className="text-sm space-y-1">
                <li>• Role-based navigation and functionality</li>
                <li>• Real-time usage tracking with visual indicators</li>
                <li>• Responsive design for all screen sizes</li>
                <li>• Modal-based plan selection</li>
                <li>• Comprehensive error handling</li>
                <li>• Toast notifications for user feedback</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Backend Integration</h4>
              <ul className="text-sm space-y-1">
                <li>• RTK Query for efficient data fetching</li>
                <li>• Automatic token refresh handling</li>
                <li>• Cookie-based subscription data storage</li>
                <li>• Pagination support for history</li>
                <li>• Optimistic updates for better UX</li>
                <li>• Comprehensive API error handling</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Navigation</h3>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/subscriptions" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
              My Subscriptions
            </Link>
            <Link href="/subscription-plans" className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
              Available Plans
            </Link>
            <Link href="/subscription-usage" className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
              Usage & Limits
            </Link>
            <Link href="/profile" className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
              User Profile
            </Link>
            <Link href="/dashboard" className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
              Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionFeaturesDemo;
