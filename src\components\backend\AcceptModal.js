"use client";
import React from "react";
import Button from "@/components/backend/Button";

const AcceptModal = ({ isOpen, onClose, onAccept, isLoading }) => {
  if (!isOpen) return null;

  // Close modal when clicking outside
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 backdrop-blur-sm bg-black/70 flex items-center justify-center z-50 transition-opacity duration-300"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg p-6 w-full max-w-md shadow-xl">
        <h3 className="text-lg font-semibold mb-4">Accept Request</h3>
        <p className="text-gray-600 mb-6">
          {"Are you sure you want to accept this request? Once accepted, you'll be responsible for fulfilling it according to the requirements."}
        </p>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <Button
            onClick={onAccept}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            {isLoading ? "Processing..." : "Accept Request"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AcceptModal;