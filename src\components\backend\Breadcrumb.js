"use client"; // only needed if using app directory in Next.js 13+

import React from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Icon from "./Icon";

const Breadcrumb = () => {
  const pathname = usePathname(); // e.g. "/requests/pending"
  const pathParts = pathname.split("/").filter((part) => part); // ['requests', 'pending']

  // Convert slug to title case
  const formatTitle = (slug) => {
    return slug
      .replace(/-/g, " ")
      .replace(/\b\w/g, (l) => l.toUpperCase());
  };

  return (
    <nav className="flex mt-5" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
        <li className="inline-flex items-center">
          <Link href="/" className="inline-flex items-center text-[#2B2A28]">
            <Icon name="dashboard" />
          </Link>
        </li>

        {pathParts.map((part, index) => {
          const href = "/" + pathParts.slice(0, index + 1).join("/");
          const isLast = index === pathParts.length - 1;

          return (
            <li key={href}>
              <div className="flex items-center">
                <svg
                  className="size-6"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="m8.25 4.5 7.5 7.5-7.5 7.5"
                  />
                </svg>
                {isLast ? (
                  <span className="ms-1 text-sm capitalize font-medium archivo text-[#2B2A28] md:ms-2">
                    {formatTitle(part)}
                  </span>
                ) : (
                  <Link
                    href={href}
                    className="ms-1 text-sm capitalize font-medium text-[#2B2A28] archivo md:ms-2"
                  >
                    {formatTitle(part)}
                  </Link>
                )}
              </div>
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default Breadcrumb;
