"use client";
import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/effect-cards";
import { EffectCards, Navigation } from "swiper/modules";
import Image from "next/image";
import { EffectCreative } from "swiper/modules";
import "swiper/css/effect-creative";
const TestimonialSection = () => {
  return (
    <>
      <div className="bg-red-50">
        <div className="max-w-5xl mx-auto px-4 py-10">
          <div className="text-center pb-10">
            <h3 className="archivo text-[32px] font-bold text-[#0F0C1D]">
              Testimonials of our
              <span className="ml-2 text-transparent bg-clip-text bg-linear-to-r from-[#FD2692]   to-[#0A67F2]">
                Customers
              </span>
            </h3>
            <p className="text-[#656B76] archivo font-normal text-base max-w-xl mx-auto">
              Real Stories from Satisfied Customers Who Found Exactly What They
              Needed
            </p>
          </div>
          <div
            className="p-12"
            style={{
              backgroundImage:
                "url(/assets/frontend_assets/testimonial-bg.png)",
            }}
          >
            <div className="flex">
              <div className="flex-1  w-1/2 basis-1/2">
                <span className="bg-[#0A67F21A] text-[#0A67F2] text-sm font-normal px-4 py-1 rounded-sm inline-block w-max mb-2 archivo">
                  Testimonials{" "}
                </span>
                <h3 className="text-[#0F0C1D] archivo text-[32px] font-medium mb-1">
                  Our clients <span className="font-bold">Love Us</span>
                </h3>
                <p className="text-[#656B76] archivo font-normal text-sm">
                  Each listing is designed to be clear and concise, providing
                  customers
                </p>
                <div className="flex items-center space-x-4 mt-5">
                  <button className="custom-prev cursor-pointer w-10 h-10 z-10 rounded-full text-[#242B3A] flex items-center justify-center bg-white drop-shadow-md ">
                    <svg
                      width={18}
                      height={16}
                      viewBox="0 0 18 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.96937 15.6712L0.219376 8.92118C0.149643 8.85152 0.0943222 8.76881 0.0565796 8.67776C0.018837 8.58671 -0.000589371 8.48912 -0.000589371 8.39055C-0.000589371 8.29199 0.018837 8.1944 0.0565796 8.10335C0.0943222 8.0123 0.149643 7.92958 0.219376 7.85993L6.96937 1.10993C7.11011 0.969198 7.30098 0.890137 7.5 0.890137C7.69902 0.890137 7.88989 0.969198 8.03063 1.10993C8.17136 1.25066 8.25042 1.44153 8.25042 1.64055C8.25042 1.83958 8.17136 2.03045 8.03063 2.17118L2.56031 7.64055H17.25C17.4489 7.64055 17.6397 7.71957 17.7803 7.86022C17.921 8.00088 18 8.19164 18 8.39055C18 8.58947 17.921 8.78023 17.7803 8.92088C17.6397 9.06154 17.4489 9.14055 17.25 9.14055H2.56031L8.03063 14.6099C8.17136 14.7507 8.25042 14.9415 8.25042 15.1406C8.25042 15.3396 8.17136 15.5304 8.03063 15.6712C7.88989 15.8119 7.69902 15.891 7.5 15.891C7.30098 15.891 7.11011 15.8119 6.96937 15.6712Z"
                        fill="black"
                      />
                    </svg>
                  </button>
                  <button className="custom-next cursor-pointer w-10 h-10 z-10 rounded-full text-[#242B3A] flex items-center justify-center bg-[#0A67F2] drop-shadow-md ">
                    <svg
                      width={18}
                      height={16}
                      viewBox="0 0 18 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M11.0306 15.6712L17.7806 8.92118C17.8504 8.85152 17.9057 8.76881 17.9434 8.67776C17.9812 8.58671 18.0006 8.48912 18.0006 8.39055C18.0006 8.29199 17.9812 8.1944 17.9434 8.10335C17.9057 8.0123 17.8504 7.92958 17.7806 7.85993L11.0306 1.10993C10.8899 0.969198 10.699 0.890137 10.5 0.890137C10.301 0.890137 10.1101 0.969198 9.96937 1.10993C9.82864 1.25066 9.74958 1.44153 9.74958 1.64055C9.74958 1.83958 9.82864 2.03045 9.96937 2.17118L15.4397 7.64055H0.75C0.551088 7.64055 0.360322 7.71957 0.21967 7.86022C0.0790178 8.00088 0 8.19164 0 8.39055C0 8.58947 0.0790178 8.78023 0.21967 8.92088C0.360322 9.06154 0.551088 9.14055 0.75 9.14055H15.4397L9.96937 14.6099C9.82864 14.7507 9.74958 14.9415 9.74958 15.1406C9.74958 15.3396 9.82864 15.5304 9.96937 15.6712C10.1101 15.8119 10.301 15.891 10.5 15.891C10.699 15.891 10.8899 15.8119 11.0306 15.6712Z"
                        fill="white"
                      />
                    </svg>
                  </button>
                </div>
              </div>
              <div className="flex-1 w-1/2 basis-1/2">
                {/* <Swiper
                  effect={"cards"}
                  grabCursor={true}
                  modules={[EffectCards]}
                  className="testimonial__swiper"
                >
                  <SwiperSlide>
                    <div className="bg-white p-10 w-[400px] relative rounded-[20px]">
                      <div className="bg-gradient-to-r from-[#053B8C] to-[#0A67F2] absolute -top-7 -left-7 h-[72px] w-[72px] rounded-full flex items-center justify-center z-10">
                        <Image
                          src="/assets/frontend_assets/client-img.png"
                          width={1000}
                          height={1000}
                          quality={100}
                          alt=""
                          className="rounded-full w-[60px] h-[60px] object-cover"
                        />
                      </div>

                      <p className="archivo font-normal text-base text-black">
                        Engineer Design, Quantity Team, Quality Engineer,
                        Procurement Manager, Asset Team, Grate job! 👍
                      </p>
                      <h5 className="archivo font-bold text-lg text-black mt-2">
                        Ahnaf Ayon
                      </h5>
                      <p className="archivo font-normal text-sm text-[#656B76]">
                        Founder & CEO
                      </p>
                    </div>
                  </SwiperSlide>
                  <SwiperSlide>
                    <div className="bg-white p-10 w-[400px] relative rounded-[20px]">
                      <div className="bg-gradient-to-r from-[#053B8C] to-[#0A67F2] absolute -top-7 -left-7 h-[72px] w-[72px] rounded-full flex items-center justify-center z-10">
                        <Image
                          src="/assets/frontend_assets/client-img.png"
                          width={1000}
                          height={1000}
                          quality={100}
                          alt=""
                          className="rounded-full w-[60px] h-[60px] object-cover"
                        />
                      </div>

                      <p className="archivo font-normal text-base text-black">
                        Engineer Design, Quantity Team, Quality Engineer,
                        Procurement Manager, Asset Team, Grate job! 👍
                      </p>
                      <h5 className="archivo font-bold text-lg text-black mt-2">
                        Ahnaf Ayon
                      </h5>
                      <p className="archivo font-normal text-sm text-[#656B76]">
                        Founder & CEO
                      </p>
                    </div>
                  </SwiperSlide>
                  <SwiperSlide>
                    <div className="bg-white p-10 w-[400px] relative rounded-[20px]">
                      <div className="bg-gradient-to-r from-[#053B8C] to-[#0A67F2] absolute -top-7 -left-7 h-[72px] w-[72px] rounded-full flex items-center justify-center z-10">
                        <Image
                          src="/assets/frontend_assets/client-img.png"
                          width={1000}
                          height={1000}
                          quality={100}
                          alt=""
                          className="rounded-full w-[60px] h-[60px] object-cover"
                        />
                      </div>

                      <p className="archivo font-normal text-base text-black">
                        Engineer Design, Quantity Team, Quality Engineer,
                        Procurement Manager, Asset Team, Grate job! 👍
                      </p>
                      <h5 className="archivo font-bold text-lg text-black mt-2">
                        Ahnaf Ayon
                      </h5>
                      <p className="archivo font-normal text-sm text-[#656B76]">
                        Founder & CEO
                      </p>
                    </div>
                  </SwiperSlide>
                </Swiper> */}

                <Swiper
                  grabCursor={true}
                  effect={"creative"}
                  creativeEffect={{
                    prev: {
                      shadow: true,
                      translate: ["-20%", 0, -1],
                    },
                    next: {
                      translate: ["100%", 0, 0],
                    },
                  }}
                  navigation={{
                    nextEl: ".custom-next",
                    prevEl: ".custom-prev",
                  }}
                  modules={[EffectCreative, Navigation]}
                  className="mySwiper3 !w-[400px]"
                >
                  <SwiperSlide>
                    <div className="bg-white p-10 w-[400px] relative">
                      <div className="bg-gradient-to-r from-[#053B8C] to-[#0A67F2] absolute -top-7 -left-7 h-[72px] w-[72px] rounded-full flex items-center justify-center z-10">
                        <Image
                          src="/assets/frontend_assets/client-img.png"
                          width={60}
                          height={60}
                          quality={100}
                          alt="Client testimonial"
                          className="rounded-full w-[60px] h-[60px] object-cover"
                        />
                      </div>

                      <p className="archivo font-normal text-base text-black">
                        Engineer Design, Quantity Team, Quality Engineer,
                        Procurement Manager, Asset Team, Grate job! 👍
                      </p>
                      <h5 className="archivo font-bold text-lg text-black mt-2">
                        Ahnaf Ayon
                      </h5>
                      <p className="archivo font-normal text-sm text-[#656B76]">
                        Founder & CEO
                      </p>
                    </div>
                  </SwiperSlide>{" "}
                  <SwiperSlide>
                    <div className="bg-white p-10 w-[400px] relative">
                      <div className="bg-gradient-to-r from-[#053B8C] to-[#0A67F2] absolute -top-7 -left-7 h-[72px] w-[72px] rounded-full flex items-center justify-center z-10">
                        <Image
                          src="/assets/frontend_assets/client-img.png"
                          width={60}
                          height={60}
                          quality={100}
                          alt="Client testimonial"
                          className="rounded-full w-[60px] h-[60px] object-cover"
                        />
                      </div>

                      <p className="archivo font-normal text-base text-black">
                        Engineer Design, Quantity Team, Quality Engineer,
                        Procurement Manager, Asset Team, Grate job! 👍
                      </p>
                      <h5 className="archivo font-bold text-lg text-black mt-2">
                        Ahnaf Ayon
                      </h5>
                      <p className="archivo font-normal text-sm text-[#656B76]">
                        Founder & CEO
                      </p>
                    </div>
                  </SwiperSlide>{" "}
                  <SwiperSlide>
                    <div className="bg-white p-10 w-[400px] relative">
                      <div className="bg-gradient-to-r from-[#053B8C] to-[#0A67F2] absolute -top-7 -left-7 h-[72px] w-[72px] rounded-full flex items-center justify-center z-10">
                        <Image
                          src="/assets/frontend_assets/client-img.png"
                          width={60}
                          height={60}
                          quality={100}
                          alt="Client testimonial"
                          className="rounded-full w-[60px] h-[60px] object-cover"
                        />
                      </div>

                      <p className="archivo font-normal text-base text-black">
                        Engineer Design, Quantity Team, Quality Engineer,
                        Procurement Manager, Asset Team, Grate job! 👍
                      </p>
                      <h5 className="archivo font-bold text-lg text-black mt-2">
                        Ahnaf Ayon
                      </h5>
                      <p className="archivo font-normal text-sm text-[#656B76]">
                        Founder & CEO
                      </p>
                    </div>
                  </SwiperSlide>
                </Swiper>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TestimonialSection;
