"use client";
export const dynamic = "force-dynamic";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import Pagination from "@/components/backend/Pagination";
import SelectInput from "@/components/backend/SelectInput";
import Image from "next/image";
import React, { useState } from "react";

const Notifications = () => {
  const [selectedNotifications, setSelectedNotifications] = useState([]);
  const [activeTab, setActiveTab] = useState("all");
  const [selectedGroupBy, setSelectedGroupBy] = useState("");

  const notifications = [
    {
      id: "1",
      avatar: "/placeholder.svg?height=40&width=40",
      name: "<PERSON>",
      message: "wants to collaborate",
      time: "3 days ago",
      read: false,
    },
    {
      id: "2",
      avatar: "/placeholder.svg?height=40&width=40",
      name: "<PERSON> <PERSON>",
      message:
        "we've got a new user research opportunity for you. <PERSON> from The Mayor's Office is looking for people like you.",
      time: "1 month ago",
      read: false,
    },
    {
      id: "3",
      avatar: "/placeholder.svg?height=40&width=40",
      name: "<PERSON> <PERSON>",
      message:
        "we've got a new user research opportunity for you. <PERSON> is looking for people like you.",
      time: "1 month ago",
      read: false,
    },
    {
      id: "4",
      avatar: "/placeholder.svg?height=40&width=40",
      name: "Hey Peter",
      message:
        "we've got a new user research opportunity for you. Quagmire from Giggity Co. is looking for people like you.",
      time: "1 month ago",
      read: false,
    },
    {
      id: "5",
      avatar: "/placeholder.svg?height=40&width=40",
      name: "Hey Peter",
      message:
        "we've got a new side project opportunity for you. Herbert from Children's Program is looking for people like you.",
      time: "1 month ago",
      read: false,
    },
    {
      id: "6",
      avatar: "/placeholder.svg?height=40&width=40",
      name: "Hey Peter",
      message:
        "we've got a new side project opportunity for you. Cleveland from The Post Office is looking for people like you.",
      time: "2 months ago",
      read: false,
    },
    {
      id: "7",
      avatar: "/placeholder.svg?height=40&width=40",
      name: "Hey Peter",
      message:
        "we've got a new user research opportunity for you. Joe is looking for people like you.",
      time: "2 months ago",
      read: false,
    },
  ];

  const toggleSelectAll = () => {
    if (selectedNotifications.length === notifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(
        notifications.map((notification) => notification.id)
      );
    }
  };

  const toggleSelectNotification = () => {
    if (selectedNotifications.includes(id)) {
      setSelectedNotifications(
        selectedNotifications.filter((notificationId) => notificationId !== id)
      );
    } else {
      setSelectedNotifications([...selectedNotifications, id]);
    }
  };

  const groupByData = [
    { value: "date", label: "Date" },
    { value: "type", label: "Type" },
    { value: "sender", label: "Sender" },
  ];
  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold capitalize text-[#343A40] text-xl poppins">
          Notifications{" "}
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4">
          <div className="max-w-5xl mx-auto p-4 bg-white rounded-lg shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="flex space-x-2">
                <button
                  size="sm"
                  className="rounded-md h-8"
                  onClick={() => setActiveTab("all")}
                >
                  All
                </button>
                <button
                  size="sm"
                  className="rounded-md h-8"
                  onClick={() => setActiveTab("unread")}
                >
                  Unread
                </button>
              </div>
              <div className="flex items-center space-x-2">
                {/* <div className="relative">
                  <Filter className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Filter notifications"
                    className="pl-8 h-8 w-[400px]"
                  />
                </div> */}

                <form className="max-w-sm mx-auto">
                  <div className="flex">
                    <div
                      id="dropdown-states"
                      className="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow-sm w-44 dark:bg-gray-700"
                    >
                      <span className="py-2 text-sm text-gray-700">
                        Group by:
                      </span>
                    </div>

                    <SelectInput
                      options={groupByData}
                      value={selectedGroupBy}
                      onChange={(e) => setSelectedGroupBy(e.target.value)}
                      className="w-[170px] "
                    />
                  </div>
                </form>
              </div>
            </div>

            <div className="border rounded-md mb-4">
              <div className="p-3 border-b flex items-center">
                <div className="flex items-center mb-4">
                  <input
                    id="default-checkbox"
                    type="checkbox"
                    defaultValue
                    checked={
                      selectedNotifications.length === notifications.length
                    }
                    onChange={toggleSelectAll}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <label
                    htmlFor="default-checkbox"
                    className="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                  >
                    Select all
                  </label>
                </div>
              </div>

              <div className="divide-y">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className="flex items-start p-4 gap-3"
                  >
                    <input
                      id={`notification-${notification.id}`}
                      checked={selectedNotifications.includes(notification.id)}
                      onCheckedChange={() =>
                        toggleSelectNotification(notification.id)
                      }
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm "
                    />
                    <Image
                      height={100}
                      width={100}
                      quality={100}
                      src="/assets/backend_assets/images/chat-head.png"
                      alt="Contact"
                      className="w-[54px] h-[54px] rounded-full mr-3"
                    />

                    <div className="flex-1">
                      <div className="flex flex-col">
                        {notification.id === "1" ? (
                          <div className="flex items-center">
                            <span className="font-medium">
                              {notification.name}
                            </span>
                            <span className="text-gray-600 ml-1">
                              {notification.message}
                            </span>
                          </div>
                        ) : (
                          <div>
                            <span className="text-gray-600">
                              {notification.message}
                            </span>
                          </div>
                        )}
                        <span className="text-gray-500 text-sm">
                          {notification.time}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <Pagination />
        </div>
      </div>
    </>
  );
};

export default Notifications;
