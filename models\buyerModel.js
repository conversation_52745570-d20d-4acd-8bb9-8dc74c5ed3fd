const { prisma } = require('../config/dbConfig');

class BuyerModel {
  /**
   * Get buyer profile by ID
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Object>} Buyer profile
   */
  static async getBuyerProfile(buyerId) {
    const user = await prisma.users.findUnique({
      where: {
        id: buyerId,
        roles: {
          some: {
            role: {
              name: 'Buyer'
            }
          }
        }
      },
      select: {
        id: true,
        first_name: true,
        last_name: true,
        email: true,
        phone_number: true,
        profile_picture_url: true,
        gender: true,
        date_of_birth: true,
        address: true,
        is_email_verified: true,
        is_approved: true,
        status: true,
        created_at: true,
        updated_at: true,
        roles: {
          include: {
            role: true
          }
        }
      }
    });

    if (!user) return null;

    // Transform roles to simple array
    user.roles = user.roles.map(userRole => userRole.role.name);

    // Get buyer statistics
    const stats = await BuyerModel.getBuyerStatistics(buyerId);

    return {
      ...user,
      stats
    };
  }

  /**
   * Get buyer statistics
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Object>} Buyer statistics
   */
  static async getBuyerStatistics(buyerId) {
    // Get count of requests by status
    const requestCounts = await prisma.requests.groupBy({
      by: ['status'],
      where: {
        buyer_id: buyerId,
        is_deleted: false
      },
      _count: {
        id: true
      }
    });

    // Format the counts into a more usable structure
    const requestStats = {
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0,
      completed: 0,
      cancelled: 0
    };

    requestCounts.forEach(item => {
      const status = item.status.toLowerCase();
      requestStats[status] = item._count.id;
      requestStats.total += item._count.id;
    });

    // Get count of offers received
    const offerCount = await prisma.offers.count({
      where: {
        request: {
          buyer_id: buyerId
        },
        is_deleted: false
      }
    });

    // Get count of completed transactions
    const completedTransactions = await prisma.offers.count({
      where: {
        request: {
          buyer_id: buyerId
        },
        status: 'Completed',
        is_deleted: false
      }
    });

    return {
      requests: requestStats,
      offers_received: offerCount,
      completed_transactions: completedTransactions
    };
  }

  /**
   * Find a user by email
   * @param {string} email - User email
   * @returns {Promise<Object|null>} User object or null if not found
   */
  static async findUserByEmail(email) {
    return await prisma.users.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
      },
    });
  }

  /**
   * Find a user by phone number
   * @param {string} phone_number - User phone number
   * @returns {Promise<Object|null>} User object or null if not found
   */
  static async findUserByPhone(phone_number) {
    return await prisma.users.findUnique({
      where: { phone_number },
      select: {
        id: true,
        phone_number: true,
      },
    });
  }

  /**
   * Update buyer profile
   * @param {string} buyerId - Buyer ID
   * @param {Object} profileData - Profile data to update
   * @returns {Promise<Object>} Updated buyer profile
   */
  static async updateBuyerProfile(buyerId, profileData) {
    // Update the user
    const updatedUser = await prisma.users.update({
      where: { id: buyerId },
      data: profileData,
      select: {
        id: true,
        first_name: true,
        last_name: true,
        email: true,
        phone_number: true,
        profile_picture_url: true,
        gender: true,
        date_of_birth: true,
        address: true,
        is_email_verified: true,
        is_approved: true,
        status: true,
        created_at: true,
        updated_at: true,
        roles: {
          include: {
            role: true
          }
        }
      }
    });

    // Transform roles to simple array
    updatedUser.roles = updatedUser.roles.map(userRole => userRole.role.name);

    // Get buyer statistics
    const stats = await BuyerModel.getBuyerStatistics(buyerId);

    return {
      ...updatedUser,
      stats
    };
  }

  /**
   * Get buyer requests
   * @param {string} buyerId - Buyer ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated buyer requests
   */
  static async getBuyerRequests(buyerId, filters = {}, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    // Build the where clause
    const whereClause = {
      buyer_id: buyerId,
      is_deleted: false
    };

    // Add status filter if provided
    if (filters.status) {
      whereClause.status = filters.status;
    }

    // Add search filter if provided
    if (filters.search) {
      whereClause.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { short_description: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } }
      ];
    }

    // Get requests with pagination
    const [requests, totalRequests] = await Promise.all([
      prisma.requests.findMany({
        where: whereClause,
        include: {
          category: true,
          sub_category: true,
          request_attachments: true,
          assigned_sellers: {
            include: {
              seller: {
                select: {
                  id: true,
                  first_name: true,
                  last_name: true,
                  email: true,
                  profile_picture_url: true
                }
              }
            }
          },
          offers: {
            where: {
              is_deleted: false
            },
            include: {
              seller: {
                select: {
                  id: true,
                  first_name: true,
                  last_name: true,
                  email: true,
                  profile_picture_url: true
                }
              }
            }
          }
        },
        orderBy: {
          created_at: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.requests.count({
        where: whereClause
      })
    ]);

    const totalPages = Math.ceil(totalRequests / limit);

    return {
      data: requests,
      meta: {
        total: totalRequests,
        pages: totalPages,
        current_page: page,
        per_page: limit
      }
    };
  }

  /**
   * Get buyer offers
   * @param {string} buyerId - Buyer ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated buyer offers
   */
  static async getBuyerOffers(buyerId, filters = {}, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    // Build the where clause
    const whereClause = {
      request: {
        buyer_id: buyerId
      },
      is_deleted: false
    };

    // Add status filter if provided
    if (filters.status) {
      whereClause.status = filters.status;
    }

    // Add request filter if provided
    if (filters.request_id) {
      whereClause.request_id = filters.request_id;
    }

    // Get offers with pagination
    const [offers, totalOffers] = await Promise.all([
      prisma.offers.findMany({
        where: whereClause,
        include: {
          request: {
            include: {
              category: true,
              sub_category: true
            }
          },
          seller: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
              profile_picture_url: true
            }
          },
          offer_attachments: true,
          offer_status_changes: {
            orderBy: {
              created_at: 'desc'
            },
            take: 1
          }
        },
        orderBy: {
          created_at: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.offers.count({
        where: whereClause
      })
    ]);

    const totalPages = Math.ceil(totalOffers / limit);

    return {
      data: offers,
      meta: {
        total: totalOffers,
        pages: totalPages,
        current_page: page,
        per_page: limit
      }
    };
  }

  /**
   * Get accepted offers for a buyer without seller information
   * @param {string} buyerId - Buyer ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated accepted offers
   */
  static async getBuyerAcceptedOffers(buyerId, filters = {}, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    // Build the where clause
    const whereClause = {
      request: {
        buyer_id: buyerId
      },
      status: 'Approved', // Only get approved offers
      is_deleted: false
    };

    // Add request filter if provided
    if (filters.request_id) {
      whereClause.request_id = filters.request_id;
    }

    // Get offers with pagination, excluding seller information
    const [offers, totalOffers] = await Promise.all([
      prisma.offers.findMany({
        where: whereClause,
        include: {
          request: {
            include: {
              category: true,
              sub_category: true
            }
          },
          // Exclude seller information
          offer_attachments: true,
          offer_status_changes: {
            orderBy: {
              created_at: 'desc'
            },
            take: 1
          }
        },
        orderBy: {
          created_at: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.offers.count({
        where: whereClause
      })
    ]);

    const totalPages = Math.ceil(totalOffers / limit);

    return {
      data: offers,
      meta: {
        total: totalOffers,
        pages: totalPages,
        current_page: page,
        per_page: limit
      }
    };
  }

  /**
   * Get a specific offer by ID for a buyer without seller information
   * @param {string} buyerId - Buyer ID
   * @param {string} offerId - Offer ID
   * @returns {Promise<Object>} Offer details
   */
  static async getBuyerOfferById(buyerId, offerId) {
    // Check if the offer exists and belongs to the buyer's request
    const offer = await prisma.offers.findFirst({
      where: {
        id: offerId,
        request: {
          buyer_id: buyerId
        },
        is_deleted: false
      },
      include: {
        request: {
          include: {
            category: true,
            sub_category: true,
            request_attachments: true
          }
        },
        // Exclude seller information
        offer_attachments: true,
        offer_status_changes: {
          orderBy: {
            created_at: 'desc'
          },
          include: {
            updated_by_user: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                roles: {
                  include: {
                    role: true
                  }
                }
              }
            }
          }
        },
        offer_negotiations: {
          where: {
            OR: [
              { sender_id: buyerId },
              { recipient_id: buyerId }
            ]
          },
          orderBy: {
            created_at: 'desc'
          }
        }
      }
    });

    if (!offer) {
      return null;
    }

    // Format the offer data to include only necessary information
    const formattedOffer = {
      id: offer.id,
      request_id: offer.request_id,
      price: offer.price,
      delivery_time: offer.delivery_time,
      message: offer.message,
      description: offer.description,
      status: offer.status,
      created_at: offer.created_at,
      updated_at: offer.updated_at,
      request: {
        id: offer.request.id,
        title: offer.request.title,
        short_description: offer.request.short_description,
        description: offer.request.description,
        quantity: offer.request.quantity,
        budget_min: offer.request.budget_min,
        budget_max: offer.request.budget_max,
        deadline: offer.request.deadline,
        urgency: offer.request.urgency,
        status: offer.request.status,
        category: offer.request.category,
        sub_category: offer.request.sub_category,
        attachments: offer.request.request_attachments
      },
      attachments: offer.offer_attachments,
      status_history: offer.offer_status_changes.map(status => ({
        id: status.id,
        status: status.status,
        previous_status: status.previous_status,
        reason: status.reason,
        created_at: status.created_at,
        updated_by: status.updated_by_user ? {
          id: status.updated_by_user.id,
          name: `${status.updated_by_user.first_name} ${status.updated_by_user.last_name}`,
          roles: status.updated_by_user.roles.map(r => r.role.name)
        } : null
      })),
      negotiations: offer.offer_negotiations.map(negotiation => ({
        id: negotiation.id,
        message: negotiation.message,
        proposed_price: negotiation.proposed_price,
        proposed_delivery_time: negotiation.proposed_delivery_time,
        status: negotiation.status,
        created_at: negotiation.created_at,
        is_from_buyer: negotiation.sender_id === buyerId
      }))
    };

    return formattedOffer;
  }
}

module.exports = BuyerModel;
