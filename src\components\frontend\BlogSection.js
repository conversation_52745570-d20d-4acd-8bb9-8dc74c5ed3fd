import Image from "next/image";
import React, { useEffect } from "react";
import BlogCard from "./BlogCard";
import "aos/dist/aos.css";
import Aos from "aos";
const BlogSection = () => {
  const blogData = [
    {
      id: 1,
      img: "/assets/frontend_assets/blog-1.png",
      title: "How to create an awesome supply chain.",
      designation: "UI/UX Designing",
    },
    {
      id: 2,
      img: "/assets/frontend_assets/blog-2.png",
      title: "How to create an awesome supply chain.",
      designation: "Digital Marketing",
    },
    {
      id: 3,
      img: "/assets/frontend_assets/blog-3.png",
      title: "How to create an awesome supply chain.",
      designation: "Social Media",
    },
    {
      id: 4,
      img: "/assets/frontend_assets/blog-4.png",
      title: "How to create an awesome supply chain.",
      designation: "Social Media",
    },
  ];
  useEffect(() => {
    Aos.init();
  }, []);
  return (
    <>
      <div className="relative py-10 overflow-hidden">
        <img
          src="/assets/frontend_assets/blog-bg.png"
          alt=""
          className="absolute h-[50vh] lg:h-auto top-0 left-0 w-full z-[-1]"
        />

        <div className="">
          <div className="max-w-7xl mx-auto px-4">
            <div className="text-center py-10">
              <h3 className="archivo text-[32px] font-bold text-white">
                Blogs &{" "}
                <span className="ml-1 text-transparent bg-clip-text bg-linear-to-r from-[#FD2692]   to-[#0A67F2]">
                  Articles
                </span>
              </h3>
              <p className="text-[#C4C4C4] archivo font-normal text-base max-w-xl mx-auto">
                Service categories help organize and structure the offerings on
                a marketplace, making it easier for users to find what they
                need.{" "}
              </p>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
              {blogData.map((item, index) => (
                <BlogCard
                  index={index}
                  key={item.id}
                  img={item.img}
                  title={item.title}
                  designation={item.rating}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BlogSection;
