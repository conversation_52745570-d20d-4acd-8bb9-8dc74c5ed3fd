# Authentication Flow Documentation

## Overview

This document explains how authentication works in the Marktzoom frontend application, including token refresh and handling of authentication errors.

## Authentication Flow

1. **Login/Registration**: When a user logs in or registers, the server returns an access token and a refresh token.
2. **Token Storage**: Both tokens are stored in cookies with a long expiration time.
3. **API Requests**: The access token is included in the Authorization header for all API requests.
4. **Token Expiration**: When the access token expires, the server returns a 401 Unauthorized response.
5. **Token Refresh**: When a 401 response is received, the application automatically attempts to refresh the token.
6. **Re-authentication**: If token refresh fails, the user is redirected to the login page.

## Handling Authentication Errors

### Automatic Token Refresh

The application automatically handles 401 Unauthorized responses by:

1. Attempting to refresh the token using the refresh token
2. If successful, retrying the original request with the new token
3. If unsuccessful, redirecting to the login page

### 403 Forbidden Responses

For 403 Forbidden responses (e.g., when a user tries to access a resource they don't have permission for):

1. All cookies are cleared
2. The user is redirected to the login page
3. A toast notification is shown explaining that the session has expired

## How to Use in Components

### Using the AuthErrorHandler Component

For components that make API calls, include the `AuthErrorHandler` component:

```jsx
import AuthErrorHandler from '@/components/AuthErrorHandler';

const MyComponent = () => {
  const { data, error } = useFetchApiQuery({
    endpoint: "/some-endpoint",
  });

  return (
    <div>
      {/* This handles 401/403 errors automatically */}
      <AuthErrorHandler error={error} />
      
      {/* Rest of your component */}
    </div>
  );
};
```

### Manual Error Handling

If you need more control over error handling, you can use the `handleUnauthorized` function directly:

```jsx
import { handleUnauthorized } from '@/utils/auth';
import { useRouter } from 'next/navigation';

const MyComponent = () => {
  const router = useRouter();
  
  // In your error handling code:
  if (error) {
    // Returns true if it was a 401/403 error and was handled
    const wasAuthError = handleUnauthorized(error, router);
    
    if (!wasAuthError) {
      // Handle other types of errors
    }
  }
};
```

## Token Refresh Implementation

The token refresh functionality is implemented in:

1. `src/utils/auth.js` - Contains the `refreshToken` function
2. `src/redux/services/api.js` - Implements the token refresh logic in API requests

The implementation automatically handles token refresh without requiring changes to individual components.
