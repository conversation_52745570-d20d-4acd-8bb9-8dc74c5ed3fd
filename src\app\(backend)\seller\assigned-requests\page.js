"use client";
export const dynamic = "force-dynamic";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import Pagination from "@/components/backend/Pagination";
import SelectInput from "@/components/backend/SelectInput";
import Button from "@/components/backend/Button";
import Link from "next/link";
import React, { useState } from "react";
import { useFetchApiQuery, useMutateApiMutation } from "@/redux/services/api";
import toast from 'react-hot-toast';
import { handleUnauthorized } from '@/utils/auth';
import { useRouter } from 'next/navigation';
import AcceptModal from "@/components/backend/AcceptModal";

const AssignedRequests = () => {
  const router = useRouter();
  const [selectedFilter, setSelectedFilter] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [subCategories, setSubCategories] = useState([]);
  const [selectedSubCategory, setSelectedSubCategory] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [rejectReason, setRejectReason] = useState("");
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedAssignmentId, setSelectedAssignmentId] = useState(null);
  const [showAcceptModal, setShowAcceptModal] = useState(false);
  const [selectedAcceptId, setSelectedAcceptId] = useState(null);

  // Fetch assigned requests
  const { data: assignedRequestsData, isLoading, refetch } = useFetchApiQuery({
    endpoint: "/seller/assigned-requests",
    skip: false, // Ensure the query always runs with a valid endpoint
  });

  // Fetch categories for filtering
  const { data: categoriesData } = useFetchApiQuery({
    endpoint: "/seller/categories",
    skip: false, // Ensure the query always runs with a valid endpoint
  });

  // Mutation for accepting a request
  const [acceptRequest, { isLoading: isAccepting }] = useMutateApiMutation();

  // Mutation for rejecting a request
  const [rejectRequest, { isLoading: isRejecting }] = useMutateApiMutation();

  const assignedRequests = assignedRequestsData?.data || [];
  const totalCount = assignedRequestsData?.count || 0;
  const categories = categoriesData?.data || [];

  // Handle category change
  const handleCategoryChange = (e) => {
    const categoryId = e.target.value;
    setSelectedCategory(categoryId);

    // Find the selected category and get its subcategories
    if (categoryId && categories) {
      const category = categories.find(cat => cat.id === categoryId);
      if (category && category.sub_categories) {
        setSubCategories(category.sub_categories);
      } else {
        setSubCategories([]);
      }
    } else {
      setSubCategories([]);
    }

    setSelectedSubCategory("");
  };

  // Handle accept request
  const handleAcceptRequest = async () => {
    try {
      const loadingToast = toast.loading('Accepting request...');

      await acceptRequest({
        endpoint: `/seller/assigned-requests/${selectedAcceptId}/accept`,
        method: 'POST'
      });

      toast.dismiss(loadingToast);
      toast.success('Request accepted successfully');
      setShowAcceptModal(false);
      setSelectedAcceptId(null);
      refetch(); // Refresh the list
    } catch (error) {
      toast.dismiss(loadingToast);

      if (!handleUnauthorized(error, router)) {
        toast.error(error?.data?.message || 'Failed to accept request');
      }
    }
  };

  // Handle reject request
  const handleRejectRequest = async () => {
    if (!rejectReason.trim()) {
      toast.error('Please provide a reason for rejection');
      return;
    }

    try {
      const loadingToast = toast.loading('Rejecting request...');

      await rejectRequest({
        endpoint: `/seller/assigned-requests/${selectedAssignmentId}/reject`,
        method: 'POST',
        data: { reason: rejectReason }
      });

      toast.dismiss(loadingToast);
      toast.success('Request rejected successfully');
      setShowRejectModal(false);
      setRejectReason("");
      setSelectedAssignmentId(null);
      refetch(); // Refresh the list
    } catch (error) {
      toast.dismiss(loadingToast);

      if (!handleUnauthorized(error, router)) {
        toast.error(error?.data?.message || 'Failed to reject request');
      }
    }
  };

  // Open reject modal
  const openRejectModal = (assignmentId) => {
    setSelectedAssignmentId(assignmentId);
    setShowRejectModal(true);
  };

  // Open accept modal
  const openAcceptModal = (assignmentId) => {
    setSelectedAcceptId(assignmentId);
    setShowAcceptModal(true);
  };

  // Format date to "17th January, 2025 2:00 pm" format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString('default', { month: 'long' });
    const year = date.getFullYear();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'pm' : 'am';
    const formattedHours = hours % 12 || 12;

    // Add ordinal suffix to day
    const suffixes = ['th', 'st', 'nd', 'rd'];
    const relevantDigits = (day < 30) ? day % 20 : day % 30;
    const suffix = (relevantDigits <= 3) ? suffixes[relevantDigits] : suffixes[0];

    return `${day}${suffix} ${month}, ${year} ${formattedHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  };

  // Filter options
  const filterOptions = [
    { id: "newest", title: "Newest" },
    { id: "oldest", title: "Oldest" },
    { id: "urgent", title: "Urgent First" },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold text-[#343A40] text-xl poppins">
          Assigned Requests
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4">
          <div className="mb-4 flex flex-wrap items-center gap-2">
            <div>
              <SelectInput
                options={categories}
                value={selectedCategory}
                onChange={handleCategoryChange}
                placeholder="Select Category"
                className="w-[170px]"
              />
            </div>
            <div>
              <SelectInput
                options={subCategories}
                value={selectedSubCategory}
                onChange={(e) => setSelectedSubCategory(e.target.value)}
                placeholder="Select Sub Category"
                className="w-[200px]"
                disabled={!selectedCategory}
              />
            </div>
            <div>
              <SelectInput
                options={filterOptions}
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                placeholder="Filter"
                className="w-[120px]"
              />
            </div>
            <div className="flex-grow md:flex-grow-0">
              <div className="relative">
                <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                  <svg
                    className="w-4 h-4 text-[#374151]"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 20 20"
                  >
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                    />
                  </svg>
                </div>
                <input
                  type="search"
                  id="default-search"
                  className="ps-10 border inter font-medium border-[#D1D5DB] placeholder:text-[#374151] text-[#374151] text-sm rounded-lg outline-0 block p-2 h-10 w-full"
                  placeholder="Search by title"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>

          {assignedRequests.length === 0 ? (
            <div className="text-center py-10">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              <p className="mt-4 text-lg text-gray-600">No assigned requests found</p>
              <p className="text-gray-500">You don&apos;t have any requests assigned to you at the moment.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left rtl:text-right text-gray-500 inter">
                <thead className="text-xs text-[#4C596E] capitalize bg-[#E2E8F0]">
                  <tr>
                    <th scope="col" className="px-3 py-3 font-medium">
                      #
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Request Title
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Category
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Sub Category
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Urgency Level
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Deadline
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Status
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {assignedRequests.map((request, index) => (
                    <tr key={request.id} className="bg-white border-b border-gray-200">
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        {index + 1}
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        <Link
                          href={`/seller/requests/${request.request?.id}`}
                          className="text-indigo-600 hover:text-indigo-800 hover:underline"
                        >
                          {request.request?.title || 'N/A'}
                        </Link>
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        {request.request?.category?.title || 'N/A'}
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        {request.request?.sub_category?.title || 'N/A'}
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          request.request?.urgency === 'High' ? 'bg-red-100 text-red-800' :
                          request.request?.urgency === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {request.request?.urgency || 'N/A'}
                        </span>
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        {request.request?.deadline ? formatDate(request.request.deadline) : 'N/A'}
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        {request.status}
                      </td>
                      <td className="px-3 py-4 font-medium inter text-[#4A5568] whitespace-nowrap">
                        <div className="flex space-x-2">
                          {request.status === "Pending" && (
                            <>
                              <Button
                                onClick={() => openAcceptModal(request.id)}
                                disabled={isAccepting}
                                className="text-sm font-medium px-3 py-1 bg-indigo-600 text-white rounded-sm hover:bg-indigo-700 transition-colors"
                              >
                                Accept
                              </Button>
                              <Button
                                onClick={() => openRejectModal(request.id)}
                                disabled={isRejecting}
                                className="text-sm font-medium px-3 py-1 bg-red-500 text-white rounded-sm hover:bg-red-600 transition-colors"
                              >
                                Reject
                              </Button>
                            </>
                          )}
                          <Link
                            href={`/seller/requests/${request.request?.id}`}
                            className="text-sm font-medium px-3 py-2 bg-[#FC791A] text-white rounded-sm hover:bg-[#e66c13] transition-colors"
                          >
                            View
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          <Pagination
            totalItems={totalCount}
            pageSize={10}
            currentPage={currentPage}
            onPageChange={(page) => setCurrentPage(page)}
          />
        </div>
      </div>

      {/* Reject Modal */}
      {showRejectModal && (
        <div
        className="fixed inset-0 backdrop-blur-sm bg-black/70 flex items-center justify-center z-50 transition-opacity duration-300">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Reject Request</h3>
            <p className="text-gray-600 mb-4">Please provide a reason for rejecting this request:</p>

            <textarea
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              className="w-full border border-gray-300 rounded-lg p-3 mb-4 min-h-[100px]"
              placeholder="Enter reason for rejection..."
            ></textarea>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectReason("");
                  setSelectedAssignmentId(null);
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleRejectRequest}
                disabled={isRejecting || !rejectReason.trim()}
                className={`px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 ${
                  isRejecting || !rejectReason.trim() ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Reject
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Accept Modal */}
      <AcceptModal
        isOpen={showAcceptModal}
        onClose={() => setShowAcceptModal(false)}
        onAccept={handleAcceptRequest}
        isLoading={isAccepting}
      />
    </>
  );
};

export default AssignedRequests;
