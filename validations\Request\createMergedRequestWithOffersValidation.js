const { body } = require('express-validator');
const { validationResult } = require('express-validator');
const sendResponse = require('../../utils/sendResponse');

/**
 * Validation middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = {};
    errors.array().forEach(error => {
      if (!errorMessages[error.path]) {
        errorMessages[error.path] = [];
      }
      errorMessages[error.path].push(error.msg);
    });

    return sendResponse(
      res,
      false,
      'Validation failed',
      null,
      errorMessages,
      null,
      400
    );
  }
  next();
};

/**
 * Validation for creating merged requests with offers for children
 */
const createMergedRequestWithOffersValidation = [
  // Message and description validation
  body('message')
    .notEmpty()
    .withMessage('Message is required')
    .isLength({ min: 3, max: 500 })
    .withMessage('Message must be between 3 and 500 characters'),

  body('description')
    .notEmpty()
    .withMessage('Description is required')
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),

  // Merged children offers validation
  body('merged_children_offers')
    .isArray({ min: 1 })
    .withMessage('Merged children offers must be a non-empty array'),

  body('merged_children_offers.*.request_id')
    .notEmpty()
    .withMessage('Request ID is required for each child offer')
    .isUUID()
    .withMessage('Request ID must be a valid UUID'),

  body('merged_children_offers.*.price')
    .notEmpty()
    .withMessage('Price is required for each child offer')
    .isFloat({ min: 0.01 })
    .withMessage('Price must be a positive number greater than 0'),

  body('merged_children_offers.*.delivery_time')
    .notEmpty()
    .withMessage('Delivery time is required for each child offer')
    .isInt({ min: 1 })
    .withMessage('Delivery time must be a positive integer (days)'),

  // Custom validation to ensure no duplicate request IDs in merged_children_offers
  body('merged_children_offers')
    .custom((value) => {
      const requestIds = value.map(offer => offer.request_id);
      const uniqueIds = [...new Set(requestIds)];
      if (uniqueIds.length !== requestIds.length) {
        throw new Error('Duplicate request IDs are not allowed in merged children offers');
      }
      return true;
    }),

  // Custom validation to ensure all prices are reasonable
  body('merged_children_offers.*.price')
    .custom((value) => {
      if (value > 1000000) {
        throw new Error('Price cannot exceed 1,000,000');
      }
      return true;
    }),

  // Custom validation to ensure delivery time is reasonable
  body('merged_children_offers.*.delivery_time')
    .custom((value) => {
      if (value > 365) {
        throw new Error('Delivery time cannot exceed 365 days');
      }
      return true;
    }),

  handleValidationErrors
];

module.exports = {
  createMergedRequestWithOffersValidation
};
