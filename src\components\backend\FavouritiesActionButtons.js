"use client";
import { Menu, Transition } from "@headlessui/react";
import Link from "next/link";
import { Fragment, useEffect, useRef, useState } from "react";

export default function FavouritiesActionButtons() {
  return (
    <div className="">
      <Menu as="div" className="relative inline-block text-left">
        <div>
          <Menu.Button className="inline-flex w-full justify-center rounded-md px-4 py-2 text-sm font-medium text-black cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
              />
            </svg>
          </Menu.Button>
        </div>
        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="absolute  right-0 mt-2 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 inter ring-black/5 focus:outline-none">
            <div className="py-1 ">
              <Menu.Item>
                <Link
                  href=""
                  className={`text-sm block font-medium mb-1 last:mb-0 px-3 py-1 transition-colors duration-300 ease-linear hover:text-blue-500 text-[#374151] border-b border-[#D1D5DB]`}
                >
                  Buy again
                </Link>
              </Menu.Item>
              <Menu.Item>
                <Link
                  href=""
                  className={` text-sm block inter font-medium mb-1 px-3 transition-colors duration-300 ease-linear hover:text-blue-500 text-[#374151] last:mb-0 py-1 text-[#374151] border-b border-[#D1D5DB]`}
                >
                  Request again
                </Link>
              </Menu.Item>
              <Menu.Item>
                <Link
                  href=""
                  className={` text-sm inter block font-medium mb-1 transition-colors duration-300 ease-linear hover:text-blue-500 text-[#374151] px-3 last:mb-0 py-1 text-[#374151] border-b border-[#D1D5DB]`}
                >
                  Share{" "}
                </Link>
              </Menu.Item>
            </div>
          </Menu.Items>
        </Transition>
      </Menu>
    </div>
  );
}
