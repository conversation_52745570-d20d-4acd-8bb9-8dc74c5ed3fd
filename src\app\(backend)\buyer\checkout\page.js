"use client";
export const dynamic = "force-dynamic";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import Button from "@/components/backend/Button";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import { useFetchApiQuery, useMutateApiMutation } from "@/redux/services/api";
import toast from 'react-hot-toast';
import { handleUnauthorized } from '@/utils/auth';
import { useRouter } from 'next/navigation';
import { clearCart, placeOrder } from '@/utils/cart';
import { getUserRole } from '@/utils/user';

const Checkout = () => {
  const router = useRouter();
  const [paymentMethod, setPaymentMethod] = useState('credit_card');
  const [billingInfo, setBillingInfo] = useState({
    fullName: '',
    email: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
    cardNumber: '',
    cardExpiry: '',
    cardCvc: ''
  });
  const [errors, setErrors] = useState({});
  const [isProcessing, setIsProcessing] = useState(false);

  // Fetch cart data
  const { data: cartData, isLoading, error } = useFetchApiQuery({
    endpoint: "/buyer/cart",
    skip: false,
  });

  // Fetch user profile data
  const role = getUserRole();
  const { data: profileData } = useFetchApiQuery({
    endpoint: `/buyer/profile`,
    skip: !role,
  });

  // Mutation hook for cart operations
  const [mutateApi] = useMutateApiMutation();

  // Populate billing information from user profile
  useEffect(() => {
    if (profileData?.data) {
      const user = profileData.data;
      setBillingInfo(prevState => ({
        ...prevState,
        fullName: user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : user.first_name || '',
        email: user.email || '',
        address: user.address || '',
        city: user.city || '',
        state: user.state || '',
        zipCode: user.zip_code || user.postal_code || '',
        country: user.country || ''
      }));
    }
  }, [profileData]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setBillingInfo({
      ...billingInfo,
      [name]: value
    });

    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Billing information validation
    // Full name validation
    if (!billingInfo.fullName) {
      newErrors.fullName = 'Full name is required';
    } else if (billingInfo.fullName.length < 3 || billingInfo.fullName.length > 100) {
      newErrors.fullName = 'Full name must be between 3 and 100 characters';
    }

    // Email validation
    if (!billingInfo.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(billingInfo.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // City validation
    if (!billingInfo.city) {
      newErrors.city = 'City is required';
    }

    // State validation
    if (!billingInfo.state) {
      newErrors.state = 'State is required';
    }

    // Credit card validation
    if (paymentMethod === 'credit_card') {
      // Card number validation
      if (!billingInfo.cardNumber) {
        newErrors.cardNumber = 'Card number is required';
      } else {
        // Use a more comprehensive credit card validation
        const cardNumber = billingInfo.cardNumber.replace(/\s+/g, '');
        if (!/^\d{13,19}$/.test(cardNumber) || !isValidCreditCard(cardNumber)) {
          newErrors.cardNumber = 'Please enter a valid credit card number';
        }
      }

      // Card expiry validation
      if (!billingInfo.cardExpiry) {
        newErrors.cardExpiry = 'Expiry date is required';
      } else if (!/^\d{2}\/\d{2}$/.test(billingInfo.cardExpiry)) {
        newErrors.cardExpiry = 'Please enter a valid expiry date (MM/YY)';
      } else {
        const [month, year] = billingInfo.cardExpiry.split('/').map(part => parseInt(part, 10));
        const currentYear = new Date().getFullYear() % 100; // Get last 2 digits of current year

        if (month < 1 || month > 12) {
          newErrors.cardExpiry = 'Month must be between 1 and 12';
        } else if (year < currentYear || year > currentYear + 20) {
          newErrors.cardExpiry = 'Invalid expiry year';
        } else if (year === currentYear && month < new Date().getMonth() + 1) {
          newErrors.cardExpiry = 'Card has expired';
        }
      }

      // CVC validation
      if (!billingInfo.cardCvc) {
        newErrors.cardCvc = 'CVV is required';
      } else if (!/^\d{3,4}$/.test(billingInfo.cardCvc)) {
        newErrors.cardCvc = 'CVV must be 3 or 4 digits';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Helper function to validate credit card using Luhn algorithm
  const isValidCreditCard = (cardNumber) => {
    // Remove all non-digit characters
    cardNumber = cardNumber.replace(/\D/g, '');

    // Check if the card number is of valid length
    if (cardNumber.length < 13 || cardNumber.length > 19) {
      return false;
    }

    // Luhn algorithm
    let sum = 0;
    let shouldDouble = false;

    // Loop through the card number from right to left
    for (let i = cardNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cardNumber.charAt(i));

      if (shouldDouble) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      shouldDouble = !shouldDouble;
    }

    return sum % 10 === 0;
  };

  // Handle place order
  const handlePlaceOrder = async () => {
    if (!validateForm()) {
      toast.error('Please fill in all required fields correctly');
      return;
    }

    setIsProcessing(true);

    try {
      // Extract card information based on payment method
      const cardInfo = paymentMethod === 'credit_card' ? {
        cardNumber: billingInfo.cardNumber,
        cardExpiry: billingInfo.cardExpiry,
        cardCvc: billingInfo.cardCvc
      } : null;

      // Get cart ID and amount from cart data
      const cartId = cart?.id;

      if (!cartId) {
        toast.error('Invalid cart information. Please try again.');
        setIsProcessing(false);
        return;
      }

      // Place the order using the utility function
      const orderResult = await placeOrder(
        mutateApi,
        billingInfo,
        cardInfo,
        cartId,
        totalPrice
      );

      if (orderResult) {
        // Clear cart after successful order
        await clearCart(mutateApi);

        // Redirect to orders page
        router.push('/buyer/orders');
      }
    } catch (error) {
      if (!handleUnauthorized(error, router)) {
        console.error('Error placing order:', error);
        toast.error('Failed to place order. Please try again.');
      }
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    // Handle unauthorized access
    handleUnauthorized(error, router);

    return (
      <div className="text-center py-10">
        <p className="text-red-500">Error loading checkout</p>
      </div>
    );
  }

  const cart = cartData?.data || {};
  const cartItems = cart?.cart_items || [];
  const totalPrice = cart?.total_price || 0;

  // If cart is empty, redirect to cart page
  if (cartItems.length === 0) {
    return (
      <>
        <GoBack />
        <Breadcrumb />
        <div className="mt-5">
          <h5 className="font-semibold text-[#343A40] text-xl poppins">
            Checkout
          </h5>
          <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-8">
            <div className="text-center py-10">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              <p className="mt-4 text-lg text-gray-600">Your cart is empty</p>
              <p className="text-gray-500">Add items to your cart before proceeding to checkout.</p>
              <Link
                href="/buyer/cart"
                className="mt-4 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Return to Cart
              </Link>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold text-[#343A40] text-xl poppins">
          Checkout
        </h5>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-5">
          {/* Billing Information */}
          <div className="lg:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h2 className="text-lg font-semibold mb-4">Billing Information</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                  <input
                    type="text"
                    name="fullName"
                    value={billingInfo.fullName}
                    onChange={handleInputChange}
                    className={`w-full p-2 border rounded-md ${errors.fullName ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="John Doe"
                  />
                  {errors.fullName && <p className="text-red-500 text-xs mt-1">{errors.fullName}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                  <input
                    type="email"
                    name="email"
                    value={billingInfo.email}
                    onChange={handleInputChange}
                    className={`w-full p-2 border rounded-md ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address *</label>
                  <input
                    type="text"
                    name="address"
                    value={billingInfo.address}
                    onChange={handleInputChange}
                    className={`w-full p-2 border rounded-md ${errors.address ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="123 Main St"
                  />
                  {errors.address && <p className="text-red-500 text-xs mt-1">{errors.address}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">City *</label>
                  <input
                    type="text"
                    name="city"
                    value={billingInfo.city}
                    onChange={handleInputChange}
                    className={`w-full p-2 border rounded-md ${errors.city ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="New York"
                  />
                  {errors.city && <p className="text-red-500 text-xs mt-1">{errors.city}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">State/Province *</label>
                  <input
                    type="text"
                    name="state"
                    value={billingInfo.state}
                    onChange={handleInputChange}
                    className={`w-full p-2 border rounded-md ${errors.state ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="NY"
                  />
                  {errors.state && <p className="text-red-500 text-xs mt-1">{errors.state}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ZIP/Postal Code *</label>
                  <input
                    type="text"
                    name="zipCode"
                    value={billingInfo.zipCode}
                    onChange={handleInputChange}
                    className={`w-full p-2 border rounded-md ${errors.zipCode ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="10001"
                  />
                  {errors.zipCode && <p className="text-red-500 text-xs mt-1">{errors.zipCode}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Country *</label>
                  <input
                    type="text"
                    name="country"
                    value={billingInfo.country}
                    onChange={handleInputChange}
                    className={`w-full p-2 border rounded-md ${errors.country ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="United States"
                  />
                  {errors.country && <p className="text-red-500 text-xs mt-1">{errors.country}</p>}
                </div>
              </div>
            </div>

            {/* Payment Method */}
            <div className="bg-white p-6 rounded-lg shadow-sm mt-6">
              <h2 className="text-lg font-semibold mb-4">Payment Method</h2>

              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    id="credit_card"
                    name="paymentMethod"
                    type="radio"
                    checked={paymentMethod === 'credit_card'}
                    onChange={() => setPaymentMethod('credit_card')}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                  />
                  <label htmlFor="credit_card" className="ml-3 block text-sm font-medium text-gray-700">
                    Credit Card
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    id="paypal"
                    name="paymentMethod"
                    type="radio"
                    checked={paymentMethod === 'paypal'}
                    onChange={() => setPaymentMethod('paypal')}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                  />
                  <label htmlFor="paypal" className="ml-3 block text-sm font-medium text-gray-700">
                    PayPal
                  </label>
                </div>

                {paymentMethod === 'credit_card' && (
                  <div className="mt-4 p-4 border border-gray-200 rounded-md">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">Card Number *</label>
                        <input
                          type="text"
                          name="cardNumber"
                          value={billingInfo.cardNumber}
                          onChange={handleInputChange}
                          className={`w-full p-2 border rounded-md ${errors.cardNumber ? 'border-red-500' : 'border-gray-300'}`}
                          placeholder="1234 5678 9012 3456"
                        />
                        {errors.cardNumber && <p className="text-red-500 text-xs mt-1">{errors.cardNumber}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Expiry Date *</label>
                        <input
                          type="text"
                          name="cardExpiry"
                          value={billingInfo.cardExpiry}
                          onChange={handleInputChange}
                          className={`w-full p-2 border rounded-md ${errors.cardExpiry ? 'border-red-500' : 'border-gray-300'}`}
                          placeholder="MM/YY"
                        />
                        {errors.cardExpiry && <p className="text-red-500 text-xs mt-1">{errors.cardExpiry}</p>}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">CVC *</label>
                        <input
                          type="text"
                          name="cardCvc"
                          value={billingInfo.cardCvc}
                          onChange={handleInputChange}
                          className={`w-full p-2 border rounded-md ${errors.cardCvc ? 'border-red-500' : 'border-gray-300'}`}
                          placeholder="123"
                        />
                        {errors.cardCvc && <p className="text-red-500 text-xs mt-1">{errors.cardCvc}</p>}
                      </div>
                    </div>
                  </div>
                )}

                {paymentMethod === 'paypal' && (
                  <div className="mt-4 p-4 border border-gray-200 rounded-md">
                    <p className="text-sm text-gray-600">You will be redirected to PayPal to complete your payment.</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white p-6 rounded-lg shadow-sm sticky top-6">
              <h2 className="text-lg font-semibold mb-4">Order Summary</h2>

              <div className="space-y-4">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex justify-between pb-4 border-b border-gray-200">
                    <div>
                      <p className="font-medium text-gray-800">{item.offer?.request?.title || 'N/A'}</p>
                      <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                    </div>
                    <p className="font-medium">${(item.price * item.quantity).toFixed(2)}</p>
                  </div>
                ))}

                <div className="flex justify-between pt-2">
                  <p className="text-gray-600">Subtotal</p>
                  <p className="font-medium">${totalPrice.toFixed(2)}</p>
                </div>

                <div className="flex justify-between">
                  <p className="text-gray-600">Tax</p>
                  <p className="font-medium">$0.00</p>
                </div>

                <div className="flex justify-between border-t border-gray-200 pt-4 mt-4">
                  <p className="font-semibold">Total</p>
                  <p className="font-semibold text-lg">${totalPrice.toFixed(2)}</p>
                </div>

                <Button
                  onClick={handlePlaceOrder}
                  disabled={isProcessing}
                  className="w-full text-sm font-medium px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors mt-4"
                >
                  {isProcessing ? 'Processing...' : 'Place Order'}
                </Button>

                <p className="text-xs text-gray-500 text-center mt-2">
                  By placing your order, you agree to our Terms of Service and Privacy Policy.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Checkout;
