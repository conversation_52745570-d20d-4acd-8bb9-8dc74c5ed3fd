import { getUserRole } from '@/utils/user';

/**
 * Get the appropriate API endpoint prefix based on user role
 * @returns {string} 'buyer' or 'seller'
 */
export const getSubscriptionEndpointPrefix = () => {
  const role = getUserRole();
  return role === 'Seller' ? 'seller' : 'buyer';
};

/**
 * Subscription API endpoints
 */
export const subscriptionEndpoints = {
  // Get available subscription plans
  getAvailablePlans: () => `/${getSubscriptionEndpointPrefix()}/subscriptions/available`,
  
  // Subscribe to a plan
  subscribe: () => `/${getSubscriptionEndpointPrefix()}/subscriptions/subscribe`,
  
  // Get active subscription
  getActiveSubscription: () => `/${getSubscriptionEndpointPrefix()}/subscriptions/active`,
  
  // Get subscription history
  getSubscriptionHistory: (page = 1, limit = 10) => 
    `/${getSubscriptionEndpointPrefix()}/subscriptions/history?page=${page}&limit=${limit}`,
  
  // Get usage status
  getUsageStatus: () => `/${getSubscriptionEndpointPrefix()}/subscriptions/usage`,
  
  // Cancel subscription
  cancelSubscription: (subscriptionId) => 
    `/${getSubscriptionEndpointPrefix()}/subscriptions/${subscriptionId}/cancel`,
};

/**
 * Subscription service functions
 */
export const subscriptionService = {
  /**
   * Get available subscription plans
   * @param {Function} fetchApi - RTK Query fetch function
   * @returns {Promise} API response
   */
  getAvailablePlans: (fetchApi) => {
    return fetchApi({
      endpoint: subscriptionEndpoints.getAvailablePlans()
    });
  },

  /**
   * Subscribe to a plan
   * @param {Function} mutateApi - RTK Query mutation function
   * @param {Object} subscriptionData - Subscription data
   * @returns {Promise} API response
   */
  subscribe: (mutateApi, subscriptionData) => {
    return mutateApi({
      endpoint: subscriptionEndpoints.subscribe(),
      method: 'POST',
      data: subscriptionData
    });
  },

  /**
   * Get active subscription
   * @param {Function} fetchApi - RTK Query fetch function
   * @returns {Promise} API response
   */
  getActiveSubscription: (fetchApi) => {
    return fetchApi({
      endpoint: subscriptionEndpoints.getActiveSubscription()
    });
  },

  /**
   * Get subscription history
   * @param {Function} fetchApi - RTK Query fetch function
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise} API response
   */
  getSubscriptionHistory: (fetchApi, page = 1, limit = 10) => {
    return fetchApi({
      endpoint: subscriptionEndpoints.getSubscriptionHistory(page, limit)
    });
  },

  /**
   * Get usage status
   * @param {Function} fetchApi - RTK Query fetch function
   * @returns {Promise} API response
   */
  getUsageStatus: (fetchApi) => {
    return fetchApi({
      endpoint: subscriptionEndpoints.getUsageStatus()
    });
  },

  /**
   * Cancel subscription
   * @param {Function} mutateApi - RTK Query mutation function
   * @param {string} subscriptionId - Subscription ID
   * @returns {Promise} API response
   */
  cancelSubscription: (mutateApi, subscriptionId) => {
    return mutateApi({
      endpoint: subscriptionEndpoints.cancelSubscription(subscriptionId),
      method: 'PUT'
    });
  }
};

/**
 * Format subscription data for display
 * @param {Object} subscription - Raw subscription data
 * @returns {Object} Formatted subscription data
 */
export const formatSubscriptionData = (subscription) => {
  if (!subscription) return null;

  return {
    ...subscription,
    formattedStartDate: subscription.start_date 
      ? new Date(subscription.start_date).toLocaleDateString() 
      : 'N/A',
    formattedEndDate: subscription.end_date 
      ? new Date(subscription.end_date).toLocaleDateString() 
      : 'N/A',
    daysRemaining: subscription.end_date 
      ? Math.max(0, Math.ceil((new Date(subscription.end_date) - new Date()) / (1000 * 60 * 60 * 24)))
      : 0,
    isExpiringSoon: subscription.end_date 
      ? Math.ceil((new Date(subscription.end_date) - new Date()) / (1000 * 60 * 60 * 24)) <= 7
      : false
  };
};

/**
 * Get status color class for subscription status
 * @param {string} status - Subscription status
 * @returns {string} CSS class string
 */
export const getStatusColorClass = (status) => {
  switch (status?.toLowerCase()) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'expired':
      return 'bg-red-100 text-red-800';
    case 'cancelled':
      return 'bg-gray-100 text-gray-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

/**
 * Calculate usage percentage
 * @param {number} used - Used amount
 * @param {number} limit - Total limit
 * @returns {number} Percentage (0-100)
 */
export const calculateUsagePercentage = (used, limit) => {
  if (!limit || limit === 0) return 0;
  return Math.min(100, Math.round((used / limit) * 100));
};

/**
 * Get usage color class based on percentage
 * @param {number} percentage - Usage percentage
 * @returns {string} CSS class string
 */
export const getUsageColorClass = (percentage) => {
  if (percentage >= 90) return 'bg-red-500';
  if (percentage >= 75) return 'bg-yellow-500';
  if (percentage >= 50) return 'bg-blue-500';
  return 'bg-green-500';
};
