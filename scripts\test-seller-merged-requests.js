/**
 * Test script for SellerModel.getRequestDetailsForSeller merged requests functionality
 * This script tests the new merged request feature without requiring Jest setup
 */

const { PrismaClient } = require('@prisma/client');
const SellerModel = require('../models/sellerModel');

const prisma = new PrismaClient();

// Test data
const TEST_SELLER_ID = 'test-seller-123';
const TEST_REQUEST_ID = 'test-request-456';
const TEST_CHILD_REQUEST_1_ID = 'test-child-1';
const TEST_CHILD_REQUEST_2_ID = 'test-child-2';

async function setupTestData() {
  console.log('🔧 Setting up test data...');
  
  try {
    // Clean up any existing test data
    await cleanupTestData();

    // Create test seller
    await prisma.users.create({
      data: {
        id: TEST_SELLER_ID,
        email: '<EMAIL>',
        password: 'hashedpassword',
        first_name: 'Test',
        last_name: '<PERSON><PERSON>',
        roles: {
          create: {
            role: {
              connectOrCreate: {
                where: { name: 'Seller' },
                create: { name: 'Seller' }
              }
            }
          }
        }
      }
    });

    // Create test categories
    const category = await prisma.categories.create({
      data: {
        title: 'Test Category',
        description: 'Test category for merged requests',
        is_active: true
      }
    });

    const subCategory = await prisma.sub_categories.create({
      data: {
        category_id: category.id,
        title: 'Test Subcategory',
        description: 'Test subcategory',
        is_active: true
      }
    });

    // Create test buyer
    const buyer = await prisma.users.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        first_name: 'Test',
        last_name: 'Buyer',
        roles: {
          create: {
            role: {
              connectOrCreate: {
                where: { name: 'Buyer' },
                create: { name: 'Buyer' }
              }
            }
          }
        }
      }
    });

    // Create parent request
    const parentRequest = await prisma.requests.create({
      data: {
        id: TEST_REQUEST_ID,
        buyer_id: buyer.id,
        category_id: category.id,
        sub_category_id: subCategory.id,
        title: 'Parent Merged Request',
        description: 'This is a parent request with merged children',
        status: 'Merged',
        is_merged: true
      }
    });

    // Create child requests
    const childRequest1 = await prisma.requests.create({
      data: {
        id: TEST_CHILD_REQUEST_1_ID,
        buyer_id: buyer.id,
        category_id: category.id,
        sub_category_id: subCategory.id,
        title: 'Child Request 1',
        description: 'First child request',
        status: 'Merged',
        is_merged: true,
        is_child: true,
        custom_fields: {
          preferred_language: 'JavaScript',
          experience_level: 'Senior'
        }
      }
    });

    const childRequest2 = await prisma.requests.create({
      data: {
        id: TEST_CHILD_REQUEST_2_ID,
        buyer_id: buyer.id,
        category_id: category.id,
        sub_category_id: subCategory.id,
        title: 'Child Request 2',
        description: 'Second child request',
        status: 'Merged',
        is_merged: true,
        is_child: true,
        custom_fields: {
          project_type: 'E-commerce',
          budget_range: '$5000-$10000'
        }
      }
    });

    // Create merged request relationships
    await prisma.request_merged_items.createMany({
      data: [
        {
          request_id: TEST_REQUEST_ID,
          merged_item_id: TEST_CHILD_REQUEST_1_ID,
          merged_by: buyer.id
        },
        {
          request_id: TEST_REQUEST_ID,
          merged_item_id: TEST_CHILD_REQUEST_2_ID,
          merged_by: buyer.id
        }
      ]
    });

    // Create attachments for parent request
    await prisma.request_attachments.createMany({
      data: [
        {
          request_id: TEST_REQUEST_ID,
          file_path: '/uploads/parent-file1.jpg',
          file_type: 'image/jpeg',
          file_size: 1024,
          description: 'Parent attachment 1'
        },
        {
          request_id: TEST_REQUEST_ID,
          file_path: '/uploads/parent-file2.pdf',
          file_type: 'application/pdf',
          file_size: 2048,
          description: 'Parent attachment 2'
        }
      ]
    });

    // Create attachments for child requests
    await prisma.request_attachments.createMany({
      data: [
        {
          request_id: TEST_CHILD_REQUEST_1_ID,
          file_path: '/uploads/child1-file1.jpg',
          file_type: 'image/jpeg',
          file_size: 512,
          description: 'Child 1 attachment 1'
        },
        {
          request_id: TEST_CHILD_REQUEST_1_ID,
          file_path: '/uploads/child1-file2.doc',
          file_type: 'application/msword',
          file_size: 1536,
          description: 'Child 1 attachment 2'
        },
        {
          request_id: TEST_CHILD_REQUEST_2_ID,
          file_path: '/uploads/child2-file1.png',
          file_type: 'image/png',
          file_size: 768,
          description: 'Child 2 attachment 1'
        }
      ]
    });

    // Assign seller to the parent request
    await prisma.request_assigned_sellers.create({
      data: {
        request_id: TEST_REQUEST_ID,
        seller_id: TEST_SELLER_ID,
        assigned_by: buyer.id,
        status: 'Active'
      }
    });

    console.log('✅ Test data setup completed');
    return { category, subCategory, buyer, parentRequest, childRequest1, childRequest2 };

  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  
  try {
    // Delete in reverse order of dependencies
    await prisma.request_assigned_sellers.deleteMany({
      where: { seller_id: TEST_SELLER_ID }
    });

    await prisma.request_attachments.deleteMany({
      where: {
        request_id: {
          in: [TEST_REQUEST_ID, TEST_CHILD_REQUEST_1_ID, TEST_CHILD_REQUEST_2_ID]
        }
      }
    });

    await prisma.request_merged_items.deleteMany({
      where: { request_id: TEST_REQUEST_ID }
    });

    await prisma.requests.deleteMany({
      where: {
        id: {
          in: [TEST_REQUEST_ID, TEST_CHILD_REQUEST_1_ID, TEST_CHILD_REQUEST_2_ID]
        }
      }
    });

    await prisma.user_roles.deleteMany({
      where: {
        user_id: {
          in: [TEST_SELLER_ID]
        }
      }
    });

    await prisma.users.deleteMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>']
        }
      }
    });

    // Clean up categories and subcategories
    await prisma.sub_categories.deleteMany({
      where: { title: 'Test Subcategory' }
    });

    await prisma.categories.deleteMany({
      where: { title: 'Test Category' }
    });

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
}

async function testMergedRequestFunctionality() {
  console.log('🧪 Testing merged request functionality...\n');

  try {
    // Setup test data
    await setupTestData();

    // Test the getRequestDetailsForSeller method
    console.log('📋 Testing getRequestDetailsForSeller with merged requests...');
    const result = await SellerModel.getRequestDetailsForSeller(TEST_REQUEST_ID, TEST_SELLER_ID);

    // Verify the results
    console.log('\n📊 Test Results:');
    console.log('================');

    // Check basic request info
    console.log(`✓ Request ID: ${result.id}`);
    console.log(`✓ Request Title: ${result.title}`);
    console.log(`✓ Is Merged: ${result.is_merged}`);

    // Check child requests
    console.log(`✓ Number of child requests: ${result.child_requests?.length || 0}`);
    if (result.child_requests && result.child_requests.length > 0) {
      result.child_requests.forEach((child, index) => {
        console.log(`  Child ${index + 1}:`);
        console.log(`    - ID: ${child.id}`);
        console.log(`    - Title: ${child.title}`);
        console.log(`    - Form Fields: ${JSON.stringify(child.form_fields)}`);
      });
    }

    // Check combined attachments
    console.log(`✓ Total attachments (parent + children): ${result.request_attachments?.length || 0}`);
    if (result.request_attachments && result.request_attachments.length > 0) {
      result.request_attachments.forEach((attachment, index) => {
        console.log(`  Attachment ${index + 1}: ${attachment.file_path} (${attachment.description})`);
      });
    }

    // Verify expected results
    const expectedAttachmentCount = 5; // 2 parent + 2 child1 + 1 child2
    const expectedChildCount = 2;

    console.log('\n🔍 Verification:');
    console.log('================');

    if (result.is_merged === true) {
      console.log('✅ is_merged flag is correctly set to true');
    } else {
      console.log('❌ is_merged flag should be true');
    }

    if (result.child_requests && result.child_requests.length === expectedChildCount) {
      console.log(`✅ Correct number of child requests (${expectedChildCount})`);
    } else {
      console.log(`❌ Expected ${expectedChildCount} child requests, got ${result.child_requests?.length || 0}`);
    }

    if (result.request_attachments && result.request_attachments.length === expectedAttachmentCount) {
      console.log(`✅ Correct number of combined attachments (${expectedAttachmentCount})`);
    } else {
      console.log(`❌ Expected ${expectedAttachmentCount} attachments, got ${result.request_attachments?.length || 0}`);
    }

    // Check if child request form fields are correctly returned
    const child1 = result.child_requests?.find(c => c.id === TEST_CHILD_REQUEST_1_ID);
    const child2 = result.child_requests?.find(c => c.id === TEST_CHILD_REQUEST_2_ID);

    if (child1 && child1.form_fields.preferred_language === 'JavaScript') {
      console.log('✅ Child 1 form fields correctly returned');
    } else {
      console.log('❌ Child 1 form fields not correctly returned');
    }

    if (child2 && child2.form_fields.project_type === 'E-commerce') {
      console.log('✅ Child 2 form fields correctly returned');
    } else {
      console.log('❌ Child 2 form fields not correctly returned');
    }

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    // Cleanup test data
    await cleanupTestData();
  }
}

async function testNonMergedRequest() {
  console.log('\n🧪 Testing non-merged request functionality...\n');

  try {
    // Create a simple non-merged request for comparison
    const buyer = await prisma.users.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        first_name: 'Simple',
        last_name: 'Buyer',
        roles: {
          create: {
            role: {
              connectOrCreate: {
                where: { name: 'Buyer' },
                create: { name: 'Buyer' }
              }
            }
          }
        }
      }
    });

    const category = await prisma.categories.create({
      data: {
        title: 'Simple Category',
        description: 'Simple test category',
        is_active: true
      }
    });

    const subCategory = await prisma.sub_categories.create({
      data: {
        category_id: category.id,
        title: 'Simple Subcategory',
        description: 'Simple subcategory',
        is_active: true
      }
    });

    const simpleRequestId = 'simple-request-789';
    const simpleRequest = await prisma.requests.create({
      data: {
        id: simpleRequestId,
        buyer_id: buyer.id,
        category_id: category.id,
        sub_category_id: subCategory.id,
        title: 'Simple Non-Merged Request',
        description: 'This is a simple request without merging'
      }
    });

    // Add some attachments
    await prisma.request_attachments.createMany({
      data: [
        {
          request_id: simpleRequestId,
          file_path: '/uploads/simple-file1.jpg',
          file_type: 'image/jpeg',
          file_size: 1024,
          description: 'Simple attachment 1'
        }
      ]
    });

    // Assign seller to the request
    await prisma.request_assigned_sellers.create({
      data: {
        request_id: simpleRequestId,
        seller_id: TEST_SELLER_ID,
        assigned_by: buyer.id,
        status: 'Active'
      }
    });

    // Test the method
    const result = await SellerModel.getRequestDetailsForSeller(simpleRequestId, TEST_SELLER_ID);

    console.log('📊 Non-Merged Request Results:');
    console.log('==============================');
    console.log(`✓ Request ID: ${result.id}`);
    console.log(`✓ Request Title: ${result.title}`);
    console.log(`✓ Is Merged: ${result.is_merged}`);
    console.log(`✓ Child Requests: ${result.child_requests?.length || 0}`);
    console.log(`✓ Attachments: ${result.request_attachments?.length || 0}`);

    // Verify non-merged behavior
    if (result.is_merged === false) {
      console.log('✅ Non-merged request correctly identified');
    } else {
      console.log('❌ Non-merged request should have is_merged = false');
    }

    if (!result.child_requests || result.child_requests.length === 0) {
      console.log('✅ No child requests for non-merged request');
    } else {
      console.log('❌ Non-merged request should not have child requests');
    }

    // Cleanup
    await prisma.request_assigned_sellers.deleteMany({
      where: { request_id: simpleRequestId }
    });
    await prisma.request_attachments.deleteMany({
      where: { request_id: simpleRequestId }
    });
    await prisma.requests.delete({ where: { id: simpleRequestId } });
    await prisma.user_roles.deleteMany({ where: { user_id: buyer.id } });
    await prisma.users.delete({ where: { id: buyer.id } });
    await prisma.sub_categories.delete({ where: { id: subCategory.id } });
    await prisma.categories.delete({ where: { id: category.id } });

    console.log('✅ Non-merged request test completed successfully!');

  } catch (error) {
    console.error('❌ Non-merged request test failed:', error);
    throw error;
  }
}

async function main() {
  console.log('🚀 Starting Seller Merged Requests Test Suite');
  console.log('==============================================\n');

  try {
    await testMergedRequestFunctionality();
    await testNonMergedRequest();
    
    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Merged request functionality working correctly');
    console.log('- ✅ Child request details properly returned');
    console.log('- ✅ Combined attachments from parent and children');
    console.log('- ✅ Form fields from child requests included');
    console.log('- ✅ Non-merged requests work as before');
    console.log('- ✅ Backward compatibility maintained');

  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testMergedRequestFunctionality,
  testNonMergedRequest,
  setupTestData,
  cleanupTestData
};
