/**
 * Test script to verify blog post creation with proper category handling
 */

const { prisma } = require('../config/dbConfig');
const BlogPostModel = require('../models/blogPostModel');
const BlogService = require('../services/blogService');

// Test data IDs
const TEST_ADMIN_ID = 'test-admin-blog-creation-123';

async function setupTestData() {
  console.log('🔧 Setting up test data...');
  
  try {
    // Clean up any existing test data
    await cleanupTestData();

    // Create test admin
    const admin = await prisma.users.create({
      data: {
        id: TEST_ADMIN_ID,
        email: '<EMAIL>',
        password_hash: 'hashedpassword',
        first_name: 'Blog',
        last_name: 'Admin',
        roles: {
          create: {
            role: {
              connectOrCreate: {
                where: { name: 'Admin' },
                create: { name: 'Admin' }
              }
            }
          }
        }
      }
    });

    console.log('✅ Test data setup completed');
    return { admin };

  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  
  try {
    await prisma.blog_posts.deleteMany({
      where: {
        OR: [
          { author_id: TEST_ADMIN_ID },
          { title: { contains: 'Test Blog Post Creation' } }
        ]
      }
    });

    await prisma.user_roles.deleteMany({
      where: { user_id: TEST_ADMIN_ID }
    });

    await prisma.users.deleteMany({
      where: { id: TEST_ADMIN_ID }
    });

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
}

async function testBlogPostCreation() {
  console.log('🧪 Testing blog post creation with category handling...\n');

  try {
    // Setup test data
    const { admin } = await setupTestData();

    // Get existing categories
    const categories = await prisma.blog_categories.findMany({
      where: { is_active: true },
      take: 2
    });

    if (categories.length === 0) {
      console.log('❌ No categories found. Please run: npm run seed:blog-categories');
      return;
    }

    console.log(`📋 Found ${categories.length} categories to test with:`);
    categories.forEach(cat => {
      console.log(`   - ${cat.name} (${cat.id})`);
    });

    // Test 1: Create blog post with valid category
    console.log('\n📋 Test 1: Creating blog post with valid category...');
    const postData1 = {
      title: 'Test Blog Post Creation - With Category',
      content: '<h2>Introduction</h2><p>This is a test blog post with a valid category.</p>',
      excerpt: 'A test blog post to verify category handling works correctly.',
      status: 'published',
      author_id: admin.id,
      category_id: categories[0].id
    };

    const processedData1 = await BlogService.processBlogPostData(postData1);
    const blogPost1 = await BlogPostModel.createBlogPost(processedData1);
    console.log(`✅ Created blog post with category: ${blogPost1.title}`);
    console.log(`   Category: ${blogPost1.category?.name || 'None'}`);

    // Test 2: Create blog post without category
    console.log('\n📋 Test 2: Creating blog post without category...');
    const postData2 = {
      title: 'Test Blog Post Creation - Without Category',
      content: '<h2>Introduction</h2><p>This is a test blog post without a category.</p>',
      excerpt: 'A test blog post without any category assigned.',
      status: 'draft',
      author_id: admin.id
      // No category_id provided
    };

    const processedData2 = await BlogService.processBlogPostData(postData2);
    const blogPost2 = await BlogPostModel.createBlogPost(processedData2);
    console.log(`✅ Created blog post without category: ${blogPost2.title}`);
    console.log(`   Category: ${blogPost2.category?.name || 'None'}`);

    // Test 3: Try to create blog post with invalid category (should fail)
    console.log('\n📋 Test 3: Testing invalid category handling...');
    const postData3 = {
      title: 'Test Blog Post Creation - Invalid Category',
      content: '<h2>Introduction</h2><p>This should fail due to invalid category.</p>',
      excerpt: 'A test blog post with invalid category.',
      status: 'draft',
      author_id: admin.id,
      category_id: '00000000-0000-0000-0000-000000000000' // Invalid UUID
    };

    try {
      const processedData3 = await BlogService.processBlogPostData(postData3);
      await BlogPostModel.createBlogPost(processedData3);
      console.log('❌ Should have failed with invalid category');
    } catch (error) {
      if (error.message.includes('does not exist')) {
        console.log('✅ Correctly rejected invalid category');
        console.log(`   Error: ${error.message}`);
      } else {
        console.log('❌ Failed with unexpected error:', error.message);
      }
    }

    // Test 4: Update blog post category
    console.log('\n📋 Test 4: Updating blog post category...');
    const updateData = {
      category_id: categories[1]?.id || categories[0].id
    };

    const updatedPost = await BlogPostModel.updateBlogPost(blogPost1.id, updateData);
    console.log(`✅ Updated blog post category: ${updatedPost.title}`);
    console.log(`   New category: ${updatedPost.category?.name || 'None'}`);

    // Verification
    console.log('\n🔍 Verification:');
    console.log('================');

    if (blogPost1 && blogPost1.category) {
      console.log('✅ Blog post with category created successfully');
    } else {
      console.log('❌ Blog post with category creation failed');
    }

    if (blogPost2 && !blogPost2.category) {
      console.log('✅ Blog post without category created successfully');
    } else {
      console.log('❌ Blog post without category creation failed');
    }

    if (updatedPost && updatedPost.category) {
      console.log('✅ Blog post category update working');
    } else {
      console.log('❌ Blog post category update failed');
    }

    console.log('\n🎉 Blog post creation test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    // Cleanup test data
    await cleanupTestData();
  }
}

async function main() {
  console.log('🚀 Blog Post Creation Test');
  console.log('==========================\n');

  try {
    await testBlogPostCreation();
    
    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Blog post creation with valid category');
    console.log('- ✅ Blog post creation without category');
    console.log('- ✅ Invalid category rejection');
    console.log('- ✅ Category update functionality');
    console.log('- ✅ Proper error handling');

  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testBlogPostCreation,
  setupTestData,
  cleanupTestData
};
