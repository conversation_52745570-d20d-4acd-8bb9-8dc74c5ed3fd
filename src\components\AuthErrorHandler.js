"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { handleUnauthorized } from '@/utils/auth';

/**
 * Component to handle authentication errors in API responses
 * @param {Object} props - Component props
 * @param {Object} props.error - Error object from API call
 * @param {boolean} props.showToast - Whether to show a toast message (default: true)
 * @returns {null} - This component doesn't render anything
 */
const AuthErrorHandler = ({ error, showToast = true }) => {
  const router = useRouter();

  useEffect(() => {
    if (error) {
      // Handle 401 and 403 errors
      handleUnauthorized(error, router, showToast);
    }
  }, [error, router, showToast]);

  return null; // This component doesn't render anything
};

export default AuthErrorHandler;
