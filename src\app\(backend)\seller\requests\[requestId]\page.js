"use client";

import React, { useState, useEffect } from "react";
import { useFetchApiQuery, useMutateApiMutation } from "../../../../../redux/services/api";
import { useRouter } from "next/navigation";
import GoBack from "@/components/backend/GoBack";
import Breadcrumb from "@/components/backend/Breadcrumb";
import { toast } from "react-hot-toast";
import { asseturl } from "@/config";
import Image from 'next/image';
import { Formik } from 'formik';
import * as Yup from 'yup';
import AcceptModal from "@/components/backend/AcceptModal";

const RequestDetailsPage = ({ params }) => {
  // Unwrap the params promise
  const unwrappedParams = React.use(params);
  const requestId = unwrappedParams?.requestId;

  const router = useRouter();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const [rejectAttempted, setRejectAttempted] = useState(false);
  const [isEditingOffer, setIsEditingOffer] = useState(false);

  return (
    <RequestDetailsContent
      requestId={requestId}
      router={router}
      selectedImageIndex={selectedImageIndex}
      setSelectedImageIndex={setSelectedImageIndex}
      showRejectModal={showRejectModal}
      setShowRejectModal={setShowRejectModal}
      rejectReason={rejectReason}
      setRejectReason={setRejectReason}
      rejectAttempted={rejectAttempted}
      setRejectAttempted={setRejectAttempted}
      isEditingOffer={isEditingOffer}
      setIsEditingOffer={setIsEditingOffer}
    />
  );
};

const RequestDetailsContent = ({
  requestId,
  router,
  selectedImageIndex,
  setSelectedImageIndex,
  showRejectModal,
  setShowRejectModal,
  rejectReason,
  setRejectReason,
  rejectAttempted,
  setRejectAttempted,
  isEditingOffer,
  setIsEditingOffer
}) => {
  const [showAcceptModal, setShowAcceptModal] = useState(false);
  const [selectedAcceptId, setSelectedAcceptId] = useState(null);
  // Fetch request details

  const [acceptRequest, { isLoading: isAccepting }] = useMutateApiMutation();
  const { data: responseData, isLoading, error, refetch } = useFetchApiQuery({
    endpoint: requestId ? `/seller/requests/${requestId}` : null,
    skip: !requestId,
  });

  // Mutation hook for form submission
  const [mutateApi] = useMutateApiMutation();

  // Add AuthErrorHandler for handling 401/403 errors
  const AuthErrorHandler = ({ error }) => {
    useEffect(() => {
      if (error?.status === 401 || error?.status === 403) {
        // Import and use the handleUnauthorized function
        import('@/utils/auth').then(({ handleUnauthorized }) => {
          handleUnauthorized(error, router);
        });
      }
    }, [error]);

    return null;
  };


  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <GoBack />
        <Breadcrumb />
        <div className="mt-5 bg-white p-6 rounded-lg shadow-sm">
          <div className="text-center py-10">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-16 w-16 mx-auto text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Request not found</h3>
            <p className="mt-1 text-sm text-gray-500">The request you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.</p>
            <div className="mt-6">
              <button
                onClick={() => router.push("/seller/requests")}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Go back to requests
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const request = responseData?.data || {};
  const attachments = request.request_attachments || [];
  const statusHistory = request.request_statuses || [];

  const isImageFile = (filePath) => {
    if (!filePath) return false;
    const extension = filePath.split('.').pop().toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension);
  };

  const getFileTypeColor = (filePath) => {
    if (!filePath) return "#95a5a6";

    const extension = filePath.split('.').pop().toLowerCase();

    switch (extension) {
      case 'pdf': return "#e74c3c";
      case 'doc': case 'docx': return "#2980b9";
      case 'xls': case 'xlsx': return "#27ae60";
      case 'ppt': case 'pptx': return "#e67e22";
      case 'jpg': case 'jpeg': case 'png': case 'gif': case 'bmp': case 'webp': case 'svg': return "#3498db";
      case 'mp4': case 'avi': case 'mov': case 'wmv': return "#9b59b6";
      case 'mp3': case 'wav': case 'ogg': return "#f39c12";
      case 'csv': return "#16a085";
      case 'zip': case 'rar': case '7z': return "#7f8c8d";
      case 'html': case 'css': case 'js': case 'json': case 'xml': return "#2c3e50";
      default: return "#95a5a6";
    }
  };

  const imageAttachments = attachments.filter(att => isImageFile(att.file_path));

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getStatusColor = (status) => {
    if (!status) return 'gray';

    switch (status.toLowerCase()) {
      case 'active': case 'approved': case 'completed': return 'green';
      case 'inactive': case 'in progress': case 'processing': return 'yellow';
      case 'suspended': case 'rejected': case 'cancelled': return 'red';
      case 'pending': return 'blue';
      default: return 'gray';
    }
  };

  const previewImage = imageAttachments.length > 0 && imageAttachments[selectedImageIndex]?.file_path
    ? imageAttachments[selectedImageIndex].file_path
    : "/placeholder.jpg";

  const handleThumbnailClick = (index) => {
    setSelectedImageIndex(index);
  };

  const handleDocumentClick = (filePath) => {
    if (!filePath) return;
    window.open(asseturl + filePath, '_blank');
  };

  const handleRejectRequest = async () => {
    setRejectAttempted(true);
    if (!rejectReason.trim()) {
      toast.error('Please provide a reason for rejection');
      return;
    }

    try {
      const loadingToast = toast.loading('Rejecting request...');

      await mutateApi({
        endpoint: `/seller/requests/${requestId}/reject`,
        data: {
          reason: rejectReason
        },
        headers: {
          "Content-Type": "application/json"
        }
      });

      toast.dismiss(loadingToast);
      toast.success('Request rejected successfully');
      setShowRejectModal(false);
      setRejectReason("");
      setRejectAttempted(false);

      // Refresh the data using refetch instead of router.refresh()
      refetch();
    } catch (error) {
      toast.dismiss(toast.loading());

      // Handle authentication errors
      if (error?.status === 401 || error?.status === 403) {
        import('@/utils/auth').then(({ handleUnauthorized }) => {
          handleUnauthorized(error, router);
        });
      } else {
        toast.error(
          error?.data?.message ||
            "Failed to reject request. Please try again."
        );
      }
    }
  };

  const openAcceptModal = (id) => {
    setSelectedAcceptId(id);
    setShowAcceptModal(true);
  };


   const handleAcceptRequest = async () => {
     try {
       const loadingToast = toast.loading('Accepting request...');

       await acceptRequest({
         endpoint: `/seller/assigned-requests/${selectedAcceptId}/accept`,
         method: 'POST'
       });

       toast.dismiss(loadingToast);
       toast.success('Request accepted successfully');
       setShowAcceptModal(false);
       setSelectedAcceptId(null);

       // Refresh the data
       refetch();
     } catch (error) {
       toast.dismiss(loadingToast);

       // Handle authentication errors
       if (error?.status === 401 || error?.status === 403) {
         import('@/utils/auth').then(({ handleUnauthorized }) => {
           handleUnauthorized(error, router);
         });
       } else {
         toast.error(error?.data?.message || 'Failed to accept request');
       }
     }
   };


  return (
    <div className="p-6">
      {/* Handle authentication errors */}
      <AuthErrorHandler error={error} />

      <GoBack />
      <Breadcrumb />

      <div className="bg-white rounded-xl shadow-md p-6 mt-5 grid md:grid-cols-3 gap-6">
        <div className="col-span-1">
          {imageAttachments.length > 0 ? (
            <div className="relative w-full h-100">
              <Image
                src={asseturl + previewImage}
                alt="Preview"
                width={800}
                height={600}
                className="rounded-lg border object-cover h-full w-full"
                unoptimized={process.env.NODE_ENV === 'development'}
              />
            </div>
          ) : (
            <div className="w-full h-80 flex items-center justify-center bg-gray-100 rounded-lg border">
              <p className="text-gray-500">No image attachments available</p>
            </div>
          )}

          {imageAttachments.length > 0 && (
            <div className="mt-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Images</h3>
              <div className="flex gap-2 overflow-x-auto pb-2">
                {imageAttachments.map((att, index) => (
                  <div
                    key={index}
                    className="relative w-20 h-20"
                    onClick={() => handleThumbnailClick(index)}
                  >
                    <Image
                      src={asseturl + att.file_path}
                      alt={`Thumbnail ${index + 1}`}
                      width={80}
                      height={80}
                      className={`rounded-md border cursor-pointer object-cover h-full w-full ${
                        selectedImageIndex === index
                          ? 'border-2 border-blue-500'
                          : 'hover:border-gray-400'
                      }`}
                      unoptimized={process.env.NODE_ENV === 'development'}
                    />
                  </div>
                ))}

                {request.file && (() => {
                  const filePath = request.file;
                  const fileName = filePath.split('/').pop();

                  return (
                    <div
                      className="flex-shrink-0 flex flex-col items-center justify-center p-2 border border-indigo-200 rounded-md cursor-pointer hover:bg-gray-50 transition w-24"
                      onClick={() => handleDocumentClick(filePath)}
                      title={`Main File: ${fileName}`}
                    >
                      <div
                        className="w-10 h-10 flex items-center justify-center rounded-full"
                        style={{ backgroundColor: `${getFileTypeColor(filePath)}20` }}
                      >
                        <span className="text-sm font-bold" style={{ color: getFileTypeColor(filePath) }}>
                          {filePath.split('.').pop().toUpperCase()}
                        </span>
                      </div>
                    </div>
                  );
                })()}
              </div>
            </div>
          )}
        </div>

        <div className="col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6 space-y-3 border border-gray-200 pl-6 rounded-lg">
          <div className="p-4">
            <div className="flex justify-between items-start">
              <h2 className="text-2xl font-bold text-gray-800">{request.title || "Request Title"}</h2>
            </div>

            <div className="text-sm text-gray-500 space-x-4">
              <span>{request?.category?.title || "Category"}</span> -
              <span>{request?.sub_category?.title || "Subcategory"}</span>
            </div>

            <div className="text-xl font-semibold text-gray-700 mt-2">
              ${request.budget_min || 0} - ${request.budget_max || 0}
            </div>

            <div className="text-sm text-gray-600 mt-2">{request.quantity || 0} Unit</div>

            <div className="text-sm text-gray-600 mt-2">
              {request.deadline ? new Date(request.deadline).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }) : "No deadline specified"}
            </div>

            <div className="mt-3">
              <p className="text-gray-800 font-semibold">Short Description:</p>
              <p className="text-gray-600">{request.short_description || "N/A"}</p>
            </div>

            <div className="mt-3">
              <p className="text-gray-800 font-semibold">Details Descriptions:</p>
              <p className="text-gray-600 whitespace-pre-line">{request.description || "N/A"}</p>
            </div>

            <div className="mt-3">
              <p className="text-gray-800 font-semibold">Additional Criteria:</p>
              <p className="text-gray-600">{request.additional_info || "N/A"}</p>
            </div>
          </div>

          {/* Vertical divider */}
          <div className="space-y-3 p-4 w-full">
            {request?.assignment?.status === "Accepted" ? (
              request?.offer ? (
                // Show offer details with edit button or edit form
                isEditingOffer ? (
                  // Edit offer form
                  <Formik
                    initialValues={{
                      request_id: request.id,
                      price: request.offer.price.toString(),
                      delivery_time: request.offer.delivery_time.toString(),
                      message: request.offer.message || "",
                      description: request.offer.description || ""
                    }}
                    validationSchema={Yup.object().shape({
                      price: Yup.number()
                        .required("The price field is required.")
                        .min(0.01, "The price must be greater than 0.")
                        .typeError("The price must be a number."),
                      delivery_time: Yup.number()
                        .required("The delivery time field is required.")
                        .integer("The delivery time must be a whole number.")
                        .min(1, "The delivery time must be at least 1 day.")
                        .typeError("The delivery time must be a number."),
                      message: Yup.string()
                        .max(1000, "The message may not be greater than 1000 characters."),
                      description: Yup.string()
                        .max(5000, "The description may not be greater than 5000 characters.")
                    })}
                    onSubmit={async (values, { setSubmitting }) => {
                      setSubmitting(true);

                      try {
                        const response = await mutateApi({
                          endpoint: `/seller/offers/${request.offer.id}`,
                          method: 'PUT',
                          data: {
                            price: values.price,
                            delivery_time: values.delivery_time,
                            message: values.message,
                            description: values.description
                          },
                          headers: {
                            "Content-Type": "application/json"
                          }
                        });
                        if (response?.data) {
                          setSubmitting(false);
                          setIsEditingOffer(false);
                          toast.success("Offer updated successfully");
                          refetch();
                        } else {
                          toast.error("Something went wrong, please try again later");
                        }
                      } catch (error) {
                        setSubmitting(false);

                        // Handle authentication errors
                        if (error?.status === 401 || error?.status === 403) {
                          import('@/utils/auth').then(({ handleUnauthorized }) => {
                            handleUnauthorized(error, router);
                          });
                        } else {
                          toast.error(
                            error?.data?.message ||
                              "Something went wrong, please try again later"
                          );
                        }
                      }
                    }}
                  >
                    {({ values, errors, touched, handleChange, handleBlur, handleSubmit, isSubmitting }) => (
                      <form onSubmit={handleSubmit}>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Edit Offer</h3>
                        <div className="mb-4 border-b border-gray-200"></div>
                        <div className="space-y-4 relative">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">
                              Price ($)
                            </label>
                            <input
                              type="number"
                              step="0.01"
                              className={`mt-1 block w-full px-3 py-2 border ${errors.price && touched.price ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500`}
                              name="price"
                              value={values.price}
                              onChange={handleChange}
                              onBlur={handleBlur}
                              placeholder="Enter offer price"
                            />
                            {errors.price && touched.price && (
                              <p className="mt-1 text-red-600 text-sm">{errors.price}</p>
                            )}
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700">
                              Delivery Time (Days)
                            </label>
                            <input
                              type="number"
                              className={`mt-1 block w-full px-3 py-2 border ${errors.delivery_time && touched.delivery_time ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500`}
                              name="delivery_time"
                              value={values.delivery_time}
                              onChange={handleChange}
                              onBlur={handleBlur}
                              placeholder="Enter delivery time in days"
                            />
                            {errors.delivery_time && touched.delivery_time && (
                              <p className="mt-1 text-red-600 text-sm">{errors.delivery_time}</p>
                            )}
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700">
                              Message (Optional)
                            </label>
                            <textarea
                              className={`mt-1 block w-full px-3 py-2 border ${errors.message && touched.message ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 min-h-[100px]`}
                              name="message"
                              value={values.message}
                              onChange={handleChange}
                              onBlur={handleBlur}
                              placeholder="Enter a brief message about your offer (max 1000 characters)"
                              maxLength={1000}
                            />
                            {errors.message && touched.message && (
                              <p className="mt-1 text-red-600 text-sm">{errors.message}</p>
                            )}
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700">
                              Description (Optional)
                            </label>
                            <textarea
                              className={`mt-1 block w-full px-3 py-2 border ${errors.description && touched.description ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 min-h-[100px]`}
                              name="description"
                              value={values.description}
                              onChange={handleChange}
                              onBlur={handleBlur}
                              placeholder="Provide detailed information about your offer (max 5000 characters)"
                              maxLength={5000}
                            />
                            {errors.description && touched.description && (
                              <p className="mt-1 text-red-600 text-sm">{errors.description}</p>
                            )}
                          </div>
                        </div>

                        {/* Divider before submit button */}
                        <div className="my-6 border-t border-gray-200"></div>

                        <div className="flex justify-end space-x-3">
                          <button
                            type="button"
                            onClick={() => setIsEditingOffer(false)}
                            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 cursor-pointer"
                          >
                            Cancel
                          </button>
                          <button
                            type="submit"
                            className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer"
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? "Updating..." : "Update Offer"}
                          </button>
                        </div>
                      </form>
                    )}
                  </Formik>
                ) : (
                  // Show offer details
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Your Offer</h3>
                    <div className="mb-4 border-b border-gray-200"></div>

                    <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <div>
                          <span className="text-gray-700 font-medium">Price:</span>
                          <span className="ml-2 text-gray-900">${request.offer.price}</span>
                        </div>
                        <div>
                          <span className="text-gray-700 font-medium">Delivery Time:</span>
                          <span className="ml-2 text-gray-900">{request.offer.delivery_time} days</span>
                        </div>
                      </div>

                      {request.offer.message && (
                        <div>
                          <p className="text-gray-700 font-medium">Message:</p>
                          <p className="text-gray-600 mt-1">{request.offer.message}</p>
                        </div>
                      )}

                      {request.offer.description && (
                        <div>
                          <p className="text-gray-700 font-medium">Description:</p>
                          <p className="text-gray-600 mt-1">{request.offer.description}</p>
                        </div>
                      )}

                      <div>
                        <p className="text-gray-700 font-medium">Status:</p>
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-${getStatusColor(request.offer.status)}-100 text-${getStatusColor(request.offer.status)}-800`}>
                          {request.offer.status}
                        </span>
                      </div>

                      <div className="flex justify-end pt-3">
                        <button
                          onClick={() => setIsEditingOffer(true)}
                          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer"
                        >
                          Edit Offer
                        </button>
                      </div>
                    </div>
                  </div>
                )
              ) : (
                // Show create offer form
                <Formik
              initialValues={{
                request_id: request.id,
                price: "",
                delivery_time: "",
                message: "",
                description: ""
              }}
              validationSchema={Yup.object().shape({
                price: Yup.number()
                  .required("The price field is required.")
                  .min(0.01, "The price must be greater than 0.")
                  .typeError("The price must be a number."),
                delivery_time: Yup.number()
                  .required("The delivery time field is required.")
                  .integer("The delivery time must be a whole number.")
                  .min(1, "The delivery time must be at least 1 day.")
                  .typeError("The delivery time must be a number."),
                message: Yup.string()
                  .max(1000, "The message may not be greater than 1000 characters."),
                description: Yup.string()
                  .max(5000, "The description may not be greater than 5000 characters.")
              })}
              onSubmit={async (values, { setSubmitting, resetForm }) => {
                setSubmitting(true);

                try {
                  await mutateApi({
                    endpoint: `/seller/offers`,
                    data: {
                      request_id: values.request_id,
                      price: values.price,
                      delivery_time: values.delivery_time,
                      message: values.message,
                      description: values.description
                    },
                    headers: {
                      "Content-Type": "application/json"
                    }
                  });

                  setSubmitting(false);
                  resetForm();
                  toast.success("Offer submitted successfully");
                  refetch();
                } catch (error) {
                  setSubmitting(false);

                  // Handle authentication errors
                  if (error?.status === 401 || error?.status === 403) {
                    import('@/utils/auth').then(({ handleUnauthorized }) => {
                      handleUnauthorized(error, router);
                    });
                  } else {
                    toast.error(
                      error?.data?.message ||
                        "Something went wrong, please try again later"
                    );
                  }
                }
              }}
            >
              {({ values, errors, touched, handleChange, handleBlur, handleSubmit, isSubmitting }) => (
                <form onSubmit={handleSubmit}>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Make an Offer</h3>
                  <div className="mb-4 border-b border-gray-200"></div>
                  <div className="space-y-4 relative">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Price ($)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        className={`mt-1 block w-full px-3 py-2 border ${errors.price && touched.price ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500`}
                        name="price"
                        value={values.price}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        placeholder="Enter offer price"
                      />
                      {errors.price && touched.price && (
                        <p className="mt-1 text-red-600 text-sm">{errors.price}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Delivery Time (Days)
                      </label>
                      <input
                        type="number"
                        className={`mt-1 block w-full px-3 py-2 border ${errors.delivery_time && touched.delivery_time ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500`}
                        name="delivery_time"
                        value={values.delivery_time}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        placeholder="Enter delivery time in days"
                      />
                      {errors.delivery_time && touched.delivery_time && (
                        <p className="mt-1 text-red-600 text-sm">{errors.delivery_time}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Message (Optional)
                      </label>
                      <textarea
                        className={`mt-1 block w-full px-3 py-2 border ${errors.message && touched.message ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 min-h-[100px]`}
                        name="message"
                        value={values.message}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        placeholder="Enter a brief message about your offer (max 1000 characters)"
                        maxLength={1000}
                      />
                      {errors.message && touched.message && (
                        <p className="mt-1 text-red-600 text-sm">{errors.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Description (Optional)
                      </label>
                      <textarea
                        className={`mt-1 block w-full px-3 py-2 border ${errors.description && touched.description ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 min-h-[100px]`}
                        name="description"
                        value={values.description}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        placeholder="Provide detailed information about your offer (max 5000 characters)"
                        maxLength={5000}
                      />
                      {errors.description && touched.description && (
                        <p className="mt-1 text-red-600 text-sm">{errors.description}</p>
                      )}
                    </div>
                  </div>

                  {/* Divider before submit button */}
                  <div className="my-6 border-t border-gray-200"></div>

                  <div className="flex justify-end">
                    <button
                      type="submit"
                      className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Creating Offer..." : "Create Offer"}
                    </button>
                  </div>
                </form>
              )}
            </Formik>
              )
            ) : (
              <div className="space-y-3 p-4 w-full">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Request Actions</h3>
                {request?.assignment?.status === "Pending" && (
                  <div className="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-4">
                    <button
                      onClick={() => openAcceptModal(request?.assigned_seller?.id)}
                      className="w-full md:w-1/2 py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors cursor-pointer"
                    >
                      Accept Request
                    </button>
                    <button
                      onClick={() => setShowRejectModal(true)}
                      className="w-full md:w-1/2 py-3 px-4 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors cursor-pointer"
                    >
                      Reject Request
                    </button>
                  </div>
                )}
                {request?.assignment?.status !== "Pending" && (
                  <div className="text-gray-600">
                    This request has already been {request?.assignment?.status.toLowerCase()}.
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {statusHistory && statusHistory.length > 0 && (
        <div className="mt-6 bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Status History</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Previous Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Updated By
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reason
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {statusHistory.map((history, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDateTime(history.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${getStatusColor(history.status)}-100 text-${getStatusColor(history.status)}-800`}>
                        {history.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {history.previous_status ? (
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${getStatusColor(history.previous_status)}-100 text-${getStatusColor(history.previous_status)}-800`}>
                          {history.previous_status}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {history.updated_by_user ? (
                        <div className="flex items-center">
                          <div className="ml-1">
                            <div className="text-sm font-medium text-gray-900">
                              {history.updated_by_user.first_name} {history.updated_by_user.last_name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {history.updated_by_user.email}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {history.reason || <span className="text-gray-400">-</span>}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Reject Request</h3>
            <p className="text-gray-600 mb-4">Please provide a reason for rejecting this request:</p>

            <textarea
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              className={`w-full border ${!rejectReason.trim() && rejectAttempted ? 'border-red-500' : 'border-gray-300'} rounded-lg p-3 mb-4 min-h-[100px]`}
              placeholder="Enter reason for rejection..."
            ></textarea>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectReason("");
                  setRejectAttempted(false);
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 cursor-pointer"
              >
                Cancel
              </button>
              <button
                onClick={handleRejectRequest}
                disabled={!rejectReason.trim()}
                className={`px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 ${
                  !rejectReason.trim() ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                }`}
              >
                Reject
              </button>
            </div>
          </div>
        </div>
      )}

            {/* Accept Modal */}
            <AcceptModal
              isOpen={showAcceptModal}
              onClose={() => setShowAcceptModal(false)}
              onAccept={handleAcceptRequest}
              isLoading={isAccepting}
            />
    </div>
  );
};

export default RequestDetailsPage;