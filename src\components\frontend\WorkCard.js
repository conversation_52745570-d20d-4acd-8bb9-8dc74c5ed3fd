import Image from "next/image";
import Link from "next/link";
import React from "react";

const WorkCard = ({ imgSrc, title, content, buttonText }) => {
  return (
    <>
      <div className="flex flex-col items-center justify-center">
        <Image
          src={imgSrc}
          width={1000}
          height={1000}
          quality={100}
          className="object-contain w-[250px] h-[100px] mx-auto"
          alt=""
        />
        <div className="flex flex-col items-center justify-center py-2">
          <h6 className="text-[#393F4D] inter font-semibold text-xl mb-1">
            {title}{" "}
          </h6>
          <p className="text-[#656B76] archivo font-normal text-base mb-1">
            {content}
          </p>
          <Link
            href=""
            className="text-base inline-block dm_sans px-5 py-2 mt-2 text-white font-normal bg-[#336AEA] text-center rounded-[8px] w-[200px]"
          >
            {buttonText}
          </Link>
        </div>
      </div>
    </>
  );
};

export default WorkCard;
