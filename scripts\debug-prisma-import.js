/**
 * Debug script to check Prisma import issues
 */

console.log('🔍 Debugging Prisma import...\n');

try {
  console.log('1. Testing direct import of config/dbConfig...');
  const dbConfig = require('../config/dbConfig');
  console.log('dbConfig keys:', Object.keys(dbConfig));
  console.log('dbConfig.prisma type:', typeof dbConfig.prisma);
  console.log('dbConfig.prisma:', dbConfig.prisma ? 'exists' : 'undefined');
  
  if (dbConfig.prisma) {
    console.log('dbConfig.prisma.seller_interested_categories:', typeof dbConfig.prisma.seller_interested_categories);
  }

  console.log('\n2. Testing destructured import...');
  const { prisma } = require('../config/dbConfig');
  console.log('prisma type:', typeof prisma);
  console.log('prisma:', prisma ? 'exists' : 'undefined');
  
  if (prisma) {
    console.log('prisma.seller_interested_categories:', typeof prisma.seller_interested_categories);
    console.log('prisma.$connect:', typeof prisma.$connect);
  }

  console.log('\n3. Testing environment variables...');
  console.log('DB_USERNAME:', process.env.DB_USERNAME ? 'set' : 'not set');
  console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? 'set' : 'not set');
  console.log('DB_HOST:', process.env.DB_HOST ? 'set' : 'not set');
  console.log('DB_PORT:', process.env.DB_PORT ? 'set' : 'not set');
  console.log('DB_DATABASE:', process.env.DB_DATABASE ? 'set' : 'not set');
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'set' : 'not set');

  console.log('\n4. Testing direct PrismaClient creation...');
  const { PrismaClient } = require('@prisma/client');
  const testPrisma = new PrismaClient();
  console.log('Direct PrismaClient type:', typeof testPrisma);
  console.log('Direct PrismaClient.seller_interested_categories:', typeof testPrisma.seller_interested_categories);
  
  console.log('\n5. Testing SellerInterestModel import...');
  const SellerInterestModel = require('../models/sellerInterestModel');
  console.log('SellerInterestModel type:', typeof SellerInterestModel);
  console.log('SellerInterestModel.getInterestedCategories:', typeof SellerInterestModel.getInterestedCategories);

} catch (error) {
  console.error('❌ Error during debugging:', error);
  console.error('Stack trace:', error.stack);
}
