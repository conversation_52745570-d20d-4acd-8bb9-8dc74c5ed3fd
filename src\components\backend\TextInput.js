import React from "react";

const TextInput = ({
  multiline = false,
  label,
  name,
  className,
  error = null,
  id = null,
  wrapperClassName = null,
  row = null,
  required = false,
  ...props
}) => {
  return (
    <>
      <div className={wrapperClassName}>
        {label && (
          <label
            className="text-sm archivo font-medium mb-2 text-[#374151] inline-block"
            htmlFor={name}
          >
            {label} {required && <span className="text-red-500"> *</span>}
          </label>
        )}
        {multiline ? (
          <textarea
            id={id || name}
            name={name}
            rows={row}
            {...props}
            className={`border inter font-medium ${error ? 'border-red-500' : 'border-[#D1D5DB]'} text-[#374151] text-sm rounded-lg outline-0 block p-2.5 ${className}`}
          />
        ) : (
          <input
            id={id || name}
            name={name}
            {...props}
            className={`border inter font-medium ${error ? 'border-red-500' : 'border-[#D1D5DB]'} text-[#374151] text-sm rounded-lg outline-0 block p-2.5 h-10 ${className}`}
          />
        )}

        {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
      </div>
    </>
  );
};

export default TextInput;
