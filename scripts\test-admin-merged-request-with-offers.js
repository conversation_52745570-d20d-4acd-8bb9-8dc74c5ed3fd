/**
 * Test script for admin merged request with offers functionality
 * This script tests the new admin endpoint for creating merged requests with offers
 */

const { prisma } = require('../config/dbConfig');
const RequestService = require('../services/requestService');

// Test data IDs
const TEST_ADMIN_ID = 'test-admin-merge-123';
const TEST_BUYER_1_ID = 'test-buyer-merge-1';
const TEST_BUYER_2_ID = 'test-buyer-merge-2';
const TEST_REQUEST_1_ID = 'test-request-merge-1';
const TEST_REQUEST_2_ID = 'test-request-merge-2';

async function setupTestData() {
  console.log('🔧 Setting up test data for admin merged request with offers...');
  
  try {
    // Clean up any existing test data
    await cleanupTestData();

    // Create test admin
    await prisma.users.create({
      data: {
        id: TEST_ADMIN_ID,
        email: '<EMAIL>',
        password_hash: 'hashedpassword',
        first_name: 'Test',
        last_name: 'Admin',
        roles: {
          create: {
            role: {
              connectOrCreate: {
                where: { name: 'Admin' },
                create: { name: 'Admin' }
              }
            }
          }
        }
      }
    });

    // Create test buyers
    const buyer1 = await prisma.users.create({
      data: {
        id: TEST_BUYER_1_ID,
        email: '<EMAIL>',
        password_hash: 'hashedpassword',
        first_name: 'John',
        last_name: 'Buyer',
        business_name: 'John\'s Business',
        roles: {
          create: {
            role: {
              connectOrCreate: {
                where: { name: 'Buyer' },
                create: { name: 'Buyer' }
              }
            }
          }
        }
      }
    });

    const buyer2 = await prisma.users.create({
      data: {
        id: TEST_BUYER_2_ID,
        email: '<EMAIL>',
        password_hash: 'hashedpassword',
        first_name: 'Jane',
        last_name: 'Buyer',
        business_name: 'Jane\'s Enterprise',
        roles: {
          create: {
            role: {
              connectOrCreate: {
                where: { name: 'Buyer' },
                create: { name: 'Buyer' }
              }
            }
          }
        }
      }
    });

    // Create test categories
    const category = await prisma.categories.create({
      data: {
        title: 'Test Admin Merge Category',
        description: 'Test category for admin merge functionality'
      }
    });

    const subCategory = await prisma.sub_categories.create({
      data: {
        category_id: category.id,
        title: 'Test Admin Merge Subcategory',
        description: 'Test subcategory'
      }
    });

    // Create test requests
    const request1 = await prisma.requests.create({
      data: {
        id: TEST_REQUEST_1_ID,
        buyer_id: buyer1.id,
        category_id: category.id,
        sub_category_id: subCategory.id,
        title: 'Web Development Request',
        description: 'Need a modern website with React',
        status: 'Active',
        budget_min: 2000,
        budget_max: 5000,
        quantity: 1,
        urgency: 'High',
        custom_fields: {
          technology: 'React',
          timeline: '2 months'
        }
      }
    });

    const request2 = await prisma.requests.create({
      data: {
        id: TEST_REQUEST_2_ID,
        buyer_id: buyer2.id,
        category_id: category.id,
        sub_category_id: subCategory.id,
        title: 'Mobile App Development',
        description: 'Need a mobile app for iOS and Android',
        status: 'Active',
        budget_min: 3000,
        budget_max: 8000,
        quantity: 2,
        urgency: 'Medium',
        custom_fields: {
          platforms: 'iOS, Android',
          features: 'Authentication, Payment'
        }
      }
    });

    console.log('✅ Test data setup completed');
    return { category, subCategory, buyer1, buyer2, request1, request2 };

  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');

  try {
    // Delete in reverse order of dependencies

    // Delete offers first
    await prisma.offers.deleteMany({
      where: {
        seller_id: TEST_ADMIN_ID
      }
    });

    // Delete offer status changes
    await prisma.offer_status_changes.deleteMany({
      where: {
        offer: {
          seller_id: TEST_ADMIN_ID
        }
      }
    });

    // Delete request statuses
    await prisma.request_statuses.deleteMany({
      where: {
        request: {
          OR: [
            { id: { in: [TEST_REQUEST_1_ID, TEST_REQUEST_2_ID] } },
            { buyer_id: { in: [TEST_BUYER_1_ID, TEST_BUYER_2_ID] } },
            { title: { contains: 'Merged Request:' } }
          ]
        }
      }
    });

    // Delete request merged items
    await prisma.request_merged_items.deleteMany({
      where: {
        merged_item_id: {
          in: [TEST_REQUEST_1_ID, TEST_REQUEST_2_ID]
        }
      }
    });

    // Delete requests
    await prisma.requests.deleteMany({
      where: {
        OR: [
          { id: { in: [TEST_REQUEST_1_ID, TEST_REQUEST_2_ID] } },
          { buyer_id: { in: [TEST_BUYER_1_ID, TEST_BUYER_2_ID] } },
          { title: { contains: 'Merged Request:' } }
        ]
      }
    });

    // Delete user roles
    await prisma.user_roles.deleteMany({
      where: {
        user_id: {
          in: [TEST_ADMIN_ID, TEST_BUYER_1_ID, TEST_BUYER_2_ID]
        }
      }
    });

    // Delete users
    await prisma.users.deleteMany({
      where: {
        id: {
          in: [TEST_ADMIN_ID, TEST_BUYER_1_ID, TEST_BUYER_2_ID]
        }
      }
    });

    // Clean up categories and subcategories
    await prisma.sub_categories.deleteMany({
      where: { title: 'Test Admin Merge Subcategory' }
    });

    await prisma.categories.deleteMany({
      where: { title: 'Test Admin Merge Category' }
    });

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
}

async function testAdminMergedRequestWithOffers() {
  console.log('🧪 Testing admin merged request with offers functionality...\n');

  try {
    // Setup test data
    const testData = await setupTestData();

    // Test data for the API call
    const requestData = {
      message: "Combined Development Project",
      description: "This is a merged request combining web and mobile development needs from multiple clients",
      merged_children_offers: [
        {
          request_id: TEST_REQUEST_1_ID,
          price: 1000,
          delivery_time: 31
        },
        {
          request_id: TEST_REQUEST_2_ID,
          price: 500,
          delivery_time: 29
        }
      ]
    };

    console.log('📋 Testing createMergedRequestWithOffers service...');
    const result = await RequestService.createMergedRequestWithOffers(
      requestData.message,
      requestData.description,
      requestData.merged_children_offers,
      TEST_ADMIN_ID
    );

    console.log('\n📊 Results:');
    console.log('===========');
    console.log(`✓ Merged Request ID: ${result.merged_request.id}`);
    console.log(`✓ Merged Request Title: ${result.merged_request.title}`);
    console.log(`✓ Total Child Requests: ${result.summary.total_child_requests}`);
    console.log(`✓ Total Offers Created: ${result.summary.total_offers_created}`);
    console.log(`✓ Total Budget Range: ${result.summary.total_budget_range}`);
    console.log(`✓ Total Quantity: ${result.summary.total_quantity}`);

    console.log('\n📋 Created Offers:');
    result.created_offers.forEach((offer, index) => {
      console.log(`  Offer ${index + 1}:`);
      console.log(`    - ID: ${offer.id}`);
      console.log(`    - Title: ${offer.offer_title}`);
      console.log(`    - Price: $${offer.price}`);
      console.log(`    - Delivery Time: ${offer.delivery_time} days`);
      console.log(`    - Original Request: ${offer.original_request.title}`);
      console.log(`    - Original Buyer: ${offer.original_request.buyer.first_name} ${offer.original_request.buyer.last_name}`);
    });

    console.log('\n📋 Merged Request Details:');
    console.log(`  - ID: ${result.merged_request.id}`);
    console.log(`  - Title: ${result.merged_request.title}`);
    console.log(`  - Description: ${result.merged_request.description}`);
    console.log(`  - Budget: $${result.merged_request.budget_min} - $${result.merged_request.budget_max}`);
    console.log(`  - Quantity: ${result.merged_request.quantity}`);
    console.log(`  - Status: ${result.merged_request.status}`);

    // Verification
    console.log('\n🔍 Verification:');
    console.log('================');

    if (result.merged_request && result.merged_request.id) {
      console.log('✅ Merged request created successfully');
    } else {
      console.log('❌ Merged request creation failed');
    }

    if (result.created_offers.length === 2) {
      console.log('✅ Correct number of offers created (2)');
    } else {
      console.log(`❌ Expected 2 offers, got ${result.created_offers.length}`);
    }

    if (result.summary.total_child_requests === 2) {
      console.log('✅ Correct number of child requests merged (2)');
    } else {
      console.log(`❌ Expected 2 child requests, got ${result.summary.total_child_requests}`);
    }

    // Check if offers have correct prices
    const offer1 = result.created_offers.find(o => o.description?.includes(TEST_REQUEST_1_ID));
    const offer2 = result.created_offers.find(o => o.description?.includes(TEST_REQUEST_2_ID));

    if (offer1 && offer1.price === 1000) {
      console.log('✅ Offer 1 has correct price ($1000)');
    } else {
      console.log('❌ Offer 1 price is incorrect');
    }

    if (offer2 && offer2.price === 500) {
      console.log('✅ Offer 2 has correct price ($500)');
    } else {
      console.log('❌ Offer 2 price is incorrect');
    }

    console.log('\n🎉 Admin merged request with offers test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    // Cleanup test data
    await cleanupTestData();
  }
}

async function main() {
  console.log('🚀 Starting Admin Merged Request with Offers Test Suite');
  console.log('======================================================\n');

  try {
    await testAdminMergedRequestWithOffers();
    
    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Admin can create merged requests with offers');
    console.log('- ✅ Multiple child requests are properly merged');
    console.log('- ✅ Offers are created for each child request');
    console.log('- ✅ Pricing and delivery times are correctly set');
    console.log('- ✅ Original request information is preserved');
    console.log('- ✅ Budget and quantity aggregation works');

  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testAdminMergedRequestWithOffers,
  setupTestData,
  cleanupTestData
};
