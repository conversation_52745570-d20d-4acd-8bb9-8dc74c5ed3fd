"use client";

import React, { useState, useEffect } from 'react';
import { useFetchApiQuery, useMutateApiMutation } from '@/redux/services/api';
import { handleUnauthorized } from '@/utils/auth';
import { useRouter } from 'next/navigation';
import { asseturl } from '@/config';
import toast from 'react-hot-toast';

const CategoryModal = ({ isOpen, onClose }) => {
  const router = useRouter();
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch categories from API
  const {
    data: categoriesData,
    isLoading,
    error
  } = useFetchApiQuery({
    endpoint: "/seller/categories",
    skip: !isOpen,
  });

  // Handle unauthorized access
  useEffect(() => {
    if (error) {
      handleUnauthorized(error, router);
    }
  }, [error, router]);

  // Extract categories from API response
  const categories = categoriesData?.data || [];

  // Filter categories based on search term
  const filteredCategories = categories.filter(category =>
    (category?.name || category?.title || '')
      .toLowerCase()
      .includes(searchTerm.toLowerCase())
  );

  // Handle category selection
  const toggleCategory = (categoryId) => {
    setSelectedCategories(prev => {
      if (prev.includes(categoryId)) {
        return prev.filter(id => id !== categoryId);
      } else {
        return [...prev, categoryId];
      }
    });
  };

    const [mutateApi, { isLoading: isSaving }] = useMutateApiMutation();

  // UUID v4 regex
  const uuidV4Regex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

  // Handle save button click
  const handleSave = async () => {
    // Frontend validation (optional, backend will also validate)
    if (!Array.isArray(selectedCategories) || selectedCategories.length === 0) {
      toast.error('Please select at least one category.');
      return;
    }
    if (!selectedCategories.every(id => typeof id === 'string' && uuidV4Regex.test(id))) {
      toast.error('All category IDs must be valid UUIDs.');
      return;
    }
    try {
      const res = await mutateApi({
        endpoint: '/seller/interested-categories',
        method: 'POST',
        data: { categoryIds: selectedCategories },
      }).unwrap();
      toast.success('Categories saved successfully!');
      onClose();
    } catch (err) {
      toast.error(err?.data?.message || 'Failed to save categories.');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity" aria-hidden="true" onClick={onClose}></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <div className="flex justify-between items-center">
                  <h3 className="text-xl leading-6 font-bold text-gray-900" id="modal-title">
                    Select Categories
                  </h3>
                  <span className="bg-indigo-100 text-indigo-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
                    {selectedCategories.length} selected
                  </span>
                </div>

                {/* Search input */}
                <div className="mt-4 mb-6 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Search categories..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 shadow-sm"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  {searchTerm && (
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                      onClick={() => setSearchTerm('')}
                    >
                      <svg className="h-5 w-5 text-gray-400 hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </button>
                  )}
                </div>

                {/* Categories list */}
                <div className="mt-2 max-h-96 overflow-y-auto">
                  {isLoading ? (
                    <div className="flex justify-center items-center h-40">
                      <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-indigo-500"></div>
                    </div>
                  ) : error ? (
                    <div className="text-center py-4">
                      <p className="text-red-500">Error loading categories</p>
                    </div>
                  ) : filteredCategories.length === 0 ? (
                    <div className="text-center py-4">
                      <p className="text-gray-500">No categories found</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {filteredCategories.map((category) => (
                        <div
                          key={category.id}
                          className={`relative rounded-lg overflow-hidden transition-all cursor-pointer ${
                            selectedCategories.includes(category.id)
                              ? 'ring-2 ring-indigo-500'
                              : 'hover:ring-1 hover:ring-gray-300'
                          }`}
                          onClick={() => toggleCategory(category.id)}
                        >
                          {/* Category Image with overlay */}
                          <div className="relative h-40 w-full">
                            {category.image ? (
                              <img
                                src={asseturl + category.image}
                                alt={category.name}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  e.target.onerror = null;
                                  e.target.src = '/assets/placeholder-image.png';
                                }}
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400">
                                <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                              </div>
                            )}

                            {/* Dark overlay */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-black/30"></div>

                            {/* Category title */}
                            <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                              <div className="flex items-center justify-between">
                                <h3 className="font-bold text-lg">{category.title}</h3>
                                {/* Checkbox indicator */}
                                <div className={`w-6 h-6 rounded-md border-2 flex items-center justify-center ${
                                  selectedCategories.includes(category.id)
                                    ? 'border-indigo-300 bg-indigo-500'
                                    : 'border-gray-300 bg-white bg-opacity-70'
                                }`}>
                                  {selectedCategories.includes(category.id) && (
                                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                  )}
                                </div>
                              </div>
                              {category.description && (
                                <p className="text-sm text-gray-200 mt-1 line-clamp-2">{category.description}</p>
                              )}
                            </div>
                          </div>

                          {/* Subcategories count */}
                          {category.subcategories && category.subcategories.length > 0 && (
                            <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs font-medium px-2 py-1 rounded-full">
                              {category.subcategories.length} subcategories
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Modal footer */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm transition-colors cursor-pointer disabled:opacity-60"
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <svg className="animate-spin mr-2 -ml-1 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                </svg>
              ) : (
                <svg className="mr-2 -ml-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
              {isSaving ? 'Saving...' : 'Save Selection'}
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition-colors cursor-pointer"
              onClick={onClose}
            >
              <svg className="mr-2 -ml-1 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              Cancel
            </button>
          </div>

          {/* Select all / Clear selection */}
          <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6 flex justify-between">
            <button
              type="button"
              className="text-sm text-indigo-600 hover:text-indigo-800 font-medium flex items-center cursor-pointer"
              onClick={() => setSelectedCategories(filteredCategories.map(cat => cat.id))}
            >
              <svg className="mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
              Select All
            </button>
            <button
              type="button"
              className="text-sm text-gray-600 hover:text-gray-800 font-medium flex items-center cursor-pointer"
              onClick={() => setSelectedCategories([])}
              disabled={selectedCategories.length === 0}
            >
              <svg className="mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              Clear Selection
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryModal;

