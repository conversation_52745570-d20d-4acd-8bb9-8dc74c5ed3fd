const { prisma } = require('../config/dbConfig');

/**
 * BlogPostModel - Handles all blog post related database operations
 */
class BlogPostModel {
  /**
   * Get all blog posts with pagination and filters (Admin)
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated blog posts with metadata
   */
  static async getAllBlogPosts(filters = {}, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    
    // Build where clause
    const whereClause = {
      is_deleted: false,
      ...(filters.status && { status: filters.status }),
      ...(filters.category_id && { category_id: filters.category_id }),
      ...(filters.author_id && { author_id: filters.author_id }),
      ...(filters.is_featured !== undefined && { is_featured: filters.is_featured }),
      ...(filters.search && {
        OR: [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { content: { contains: filters.search, mode: 'insensitive' } },
          { excerpt: { contains: filters.search, mode: 'insensitive' } }
        ]
      })
    };

    // Get total count
    const totalCount = await prisma.blog_posts.count({ where: whereClause });

    // Get paginated blog posts
    const blogPosts = await prisma.blog_posts.findMany({
      where: whereClause,
      skip,
      take: limit,
      orderBy: { created_at: 'desc' },
      include: {
        author: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        _count: {
          select: {
            comments: {
              where: { status: 'approved', is_deleted: false }
            }
          }
        }
      }
    });

    return {
      data: blogPosts,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    };
  }

  /**
   * Get published blog posts for public view with pagination
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated published blog posts
   */
  static async getPublishedBlogPosts(filters = {}, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    
    // Build where clause for published posts only
    const whereClause = {
      is_deleted: false,
      status: 'published',
      published_at: { lte: new Date() },
      ...(filters.category_id && { category_id: filters.category_id }),
      ...(filters.category_slug && {
        category: { slug: filters.category_slug }
      }),
      ...(filters.is_featured !== undefined && { is_featured: filters.is_featured }),
      ...(filters.search && {
        OR: [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { excerpt: { contains: filters.search, mode: 'insensitive' } },
          { tags: { contains: filters.search, mode: 'insensitive' } }
        ]
      })
    };

    // Get total count
    const totalCount = await prisma.blog_posts.count({ where: whereClause });

    // Get paginated blog posts
    const blogPosts = await prisma.blog_posts.findMany({
      where: whereClause,
      skip,
      take: limit,
      orderBy: { published_at: 'desc' },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        featured_image: true,
        published_at: true,
        reading_time: true,
        view_count: true,
        tags: true,
        author: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            profile_picture_url: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        _count: {
          select: {
            comments: {
              where: { status: 'approved', is_deleted: false }
            }
          }
        }
      }
    });

    return {
      data: blogPosts,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    };
  }

  /**
   * Get blog post by ID (Admin)
   * @param {string} id - Blog post ID
   * @returns {Promise<Object|null>} Blog post with full details
   */
  static async getBlogPostById(id) {
    return await prisma.blog_posts.findUnique({
      where: { id, is_deleted: false },
      include: {
        author: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        attachments: {
          where: { is_deleted: false },
          orderBy: { sort_order: 'asc' }
        },
        comments: {
          where: { is_deleted: false },
          orderBy: { created_at: 'desc' },
          take: 10
        }
      }
    });
  }

  /**
   * Get published blog post by slug (Public)
   * @param {string} slug - Blog post slug
   * @param {boolean} incrementView - Whether to increment view count
   * @returns {Promise<Object|null>} Published blog post
   */
  static async getPublishedBlogPostBySlug(slug, incrementView = true) {
    const blogPost = await prisma.blog_posts.findUnique({
      where: { 
        slug, 
        is_deleted: false,
        status: 'published',
        published_at: { lte: new Date() }
      },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        content: true,
        featured_image: true,
        meta_title: true,
        meta_description: true,
        meta_keywords: true,
        published_at: true,
        reading_time: true,
        view_count: true,
        tags: true,
        author: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            profile_picture_url: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        attachments: {
          where: { is_deleted: false },
          orderBy: { sort_order: 'asc' },
          select: {
            id: true,
            file_path: true,
            file_name: true,
            alt_text: true,
            caption: true
          }
        }
      }
    });

    // Increment view count if requested and post exists
    if (blogPost && incrementView) {
      await prisma.blog_posts.update({
        where: { id: blogPost.id },
        data: { view_count: { increment: 1 } }
      });
      blogPost.view_count += 1;
    }

    return blogPost;
  }

  /**
   * Create a new blog post (Admin)
   * @param {Object} data - Blog post data
   * @returns {Promise<Object>} Created blog post
   */
  static async createBlogPost(data) {
    const { attachments, ...blogPostData } = data;

    return await prisma.$transaction(async (tx) => {
      // Validate category exists if provided
      if (blogPostData.category_id) {
        const categoryExists = await tx.blog_categories.findUnique({
          where: { id: blogPostData.category_id }
        });

        if (!categoryExists) {
          throw new Error(`Category with ID ${blogPostData.category_id} does not exist`);
        }
      }

      // Create blog post
      const blogPost = await tx.blog_posts.create({
        data: {
          ...blogPostData,
          published_at: blogPostData.status === 'published' ? new Date() : null
        },
        include: {
          author: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true
            }
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          }
        }
      });

      // Create attachments if provided
      if (attachments && attachments.length > 0) {
        await tx.blog_post_attachments.createMany({
          data: attachments.map((attachment, index) => ({
            blog_post_id: blogPost.id,
            ...attachment,
            sort_order: index
          }))
        });
      }

      return blogPost;
    });
  }

  /**
   * Update blog post (Admin)
   * @param {string} id - Blog post ID
   * @param {Object} data - Updated blog post data
   * @returns {Promise<Object>} Updated blog post
   */
  static async updateBlogPost(id, data) {
    const { attachments, ...blogPostData } = data;

    return await prisma.$transaction(async (tx) => {
      // Validate category exists if provided
      if (blogPostData.category_id) {
        const categoryExists = await tx.blog_categories.findUnique({
          where: { id: blogPostData.category_id }
        });

        if (!categoryExists) {
          throw new Error(`Category with ID ${blogPostData.category_id} does not exist`);
        }
      }

      // Update published_at if status changed to published
      if (blogPostData.status === 'published') {
        const currentPost = await tx.blog_posts.findUnique({
          where: { id },
          select: { status: true, published_at: true }
        });

        if (currentPost?.status !== 'published' && !currentPost?.published_at) {
          blogPostData.published_at = new Date();
        }
      }

      // Update blog post
      const blogPost = await tx.blog_posts.update({
        where: { id, is_deleted: false },
        data: blogPostData,
        include: {
          author: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true
            }
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          }
        }
      });

      // Update attachments if provided
      if (attachments) {
        // Delete existing attachments
        await tx.blog_post_attachments.updateMany({
          where: { blog_post_id: id },
          data: { is_deleted: true }
        });

        // Create new attachments
        if (attachments.length > 0) {
          await tx.blog_post_attachments.createMany({
            data: attachments.map((attachment, index) => ({
              blog_post_id: id,
              ...attachment,
              sort_order: index
            }))
          });
        }
      }

      return blogPost;
    });
  }

  /**
   * Delete blog post (Admin)
   * @param {string} id - Blog post ID
   * @returns {Promise<Object>} Deleted blog post
   */
  static async deleteBlogPost(id) {
    return await prisma.blog_posts.update({
      where: { id, is_deleted: false },
      data: { is_deleted: true }
    });
  }

  /**
   * Get featured blog posts (Public)
   * @param {number} limit - Number of featured posts to get
   * @returns {Promise<Array>} Featured blog posts
   */
  static async getFeaturedBlogPosts(limit = 5) {
    return await prisma.blog_posts.findMany({
      where: {
        is_deleted: false,
        status: 'published',
        is_featured: true,
        published_at: { lte: new Date() }
      },
      take: limit,
      orderBy: { published_at: 'desc' },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        featured_image: true,
        published_at: true,
        reading_time: true,
        view_count: true,
        author: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            profile_picture_url: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        }
      }
    });
  }

  /**
   * Get related blog posts (Public)
   * @param {string} currentPostId - Current post ID to exclude
   * @param {string} categoryId - Category ID for related posts
   * @param {number} limit - Number of related posts
   * @returns {Promise<Array>} Related blog posts
   */
  static async getRelatedBlogPosts(currentPostId, categoryId, limit = 4) {
    return await prisma.blog_posts.findMany({
      where: {
        is_deleted: false,
        status: 'published',
        published_at: { lte: new Date() },
        category_id: categoryId,
        id: { not: currentPostId }
      },
      take: limit,
      orderBy: { published_at: 'desc' },
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        featured_image: true,
        published_at: true,
        reading_time: true,
        view_count: true,
        author: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });
  }
}

module.exports = BlogPostModel;
