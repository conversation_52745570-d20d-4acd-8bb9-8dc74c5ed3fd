"use client";

import { Fragment, useState } from "react";
import { Menu, Transition } from "@headlessui/react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";

const ProfileDropdown = () => {
  const router = useRouter();

  const handleLogout = () => {
    // Clear cookies by setting expiry to past date
    document.cookie = "accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "subscription=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";

    // Optional: Clear localStorage or sessionStorage if used
    localStorage.clear();
    sessionStorage.clear();

    // Redirect to login
    router.push("/login");
  };

  return (
    <div className="text-right">
      <Menu as="div" className="relative inline-block text-left">
        <div>
          <Menu.Button className="inline-flex w-full justify-center items-center rounded-md px-4 text-sm font-medium text-black cursor-pointer">
            <Image
              className="w-10 h-10 rounded-full"
              src="https://demo.readyecommerce.app/storage/users/profile/BJPdzguLDaTonVjDece4vArA2CW8SnTSVYxW72pg.jpg"
              alt="profile"
              width={40}
              height={40}
              unoptimized={true} // Disable optimization
            />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="-mr-1 ml-2 h-5 w-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m19.5 8.25-7.5 7.5-7.5-7.5"
              />
            </svg>
          </Menu.Button>
        </div>
        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="absolute z-20 right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none">
            <div className="px-1 py-1 z-20 relative">
              <Menu.Item>
                <Link
                  href="/profile"
                  className="group z-20 relative flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 cursor-pointer"
                >
                  Profile
                </Link>
              </Menu.Item>
              {/* <Menu.Item>
                <Link
                  href="/seller/business"
                  className="group z-20 relative flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 cursor-pointer"
                >
                  Business Information
                </Link>
              </Menu.Item> */}
              <Menu.Item>
                <button
                  onClick={handleLogout}
                  className="group z-20 relative flex w-full items-center rounded-md px-2 py-2 text-sm text-gray-900 cursor-pointer"
                >
                  Logout
                </button>
              </Menu.Item>
            </div>
          </Menu.Items>
        </Transition>
      </Menu>
    </div>
  );
};

export default ProfileDropdown;