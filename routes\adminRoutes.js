const express = require('express');
const adminController = require('../controllers/adminController');
const CategoryController = require('../controllers/categoryController');
const SubCategoryController = require('../controllers/subCategoryController');
const SubCategoryFormFieldController = require('../controllers/subCategoryFormFieldController');
const requestController = require('../controllers/requestController');
const offerController = require('../controllers/offerController');
const orderController = require('../controllers/orderController');
const businessInformationController = require('../controllers/businessInformationController');
const categoryValidations = require('../validations/categoryValidations');
const subCategoryValidations = require('../validations/subCategoryValidations');
const subCategoryFormFieldValidations = require('../validations/subCategoryFormFieldValidations');
const userValidations = require('../validations/userValidations');
const offerValidations = require('../validations/offerValidations');
const { createUserValidation } = require('../validations/createUserValidation');
const updateUserValidation = require('../validations/updateUserValidation');
const { updateUserPasswordValidation } = require('../validations/updateUserPasswordValidation');
const assignSellersValidation = require('../validations/Request/assignSellersValidation');
const { bulkRequestActionValidation } = require('../validations/Request/bulkRequestValidation');
const { createMergedRequestValidation } = require('../validations/Request/mergeRequestValidation');
const { createMergedRequestWithOffersValidation } = require('../validations/Request/createMergedRequestWithOffersValidation');
const {
  getBusinessInformationValidation,
  getBusinessInformationsValidation,
  updateVerificationStatusValidation
} = require('../validations/businessInformationValidation');

const {upload, uploadCategoryFiles, uploadAvatarUser, uploadBulkFile } = require('../middlewares/upload');
const subscriptionController = require('../controllers/subscriptionController');
const sortOrderController = require('../controllers/sortOrderController');
const bulkUploadController = require('../controllers/bulkUploadController');
const {
  createSubscriptionValidation,
  updateSubscriptionValidation,
  subscriptionIdValidation,
  queryValidation,
  updateTrialDurationValidation,
  assignTrialValidation
} = require('../validations/subscriptionValidation');
const bulkUploadValidations = require('../validations/bulkUploadValidations');

const authenticateToken = require('../middlewares/authMiddleware');
const authorizeRoles = require('../middlewares/authorizeMiddleware');

const router = express.Router();

// Admin Auth Route
router.post('/login', adminController.login);
router.post('/refresh-token', adminController.refreshToken);

// User Management Routes
router.get('/users', authenticateToken, authorizeRoles(['Admin']), userValidations.searchUsers, adminController.getAllUsers);
router.get('/users/:id', authenticateToken, authorizeRoles(['Admin']), userValidations.idParam, adminController.getUserByIdWithRoles);
router.put('/users/:id', authenticateToken, authorizeRoles(['Admin']), uploadAvatarUser, updateUserValidation, adminController.updateUser);
router.put('/users/:id/update-password', authenticateToken, authorizeRoles(['Admin']), updateUserPasswordValidation, adminController.updateUserPassword);
router.delete('/users/:id', authenticateToken, authorizeRoles(['Admin']), userValidations.idParam, adminController.deleteUser);
router.post('/users', authenticateToken, authorizeRoles(['Admin']), uploadAvatarUser, createUserValidation, adminController.createUser);

// Role Management Routes
router.get('/roles', authenticateToken, authorizeRoles(['Admin']), adminController.getAllRoles);
router.put('/users/:id/roles', authenticateToken, authorizeRoles(['Admin']), userValidations.updateUserRole, adminController.updateUserRole);

// Activity Logs Route
router.get('/activity-logs', authenticateToken, authorizeRoles(['Admin']), adminController.getActivityLogs);

router.post('/categories', authenticateToken, authorizeRoles(['Admin']), uploadCategoryFiles, categoryValidations.createCategory, CategoryController.createCategory);
router.get('/categories', authenticateToken, authorizeRoles(['Admin']), categoryValidations.filterCategories, CategoryController.getAllCategories);
router.get('/categories/search', authenticateToken, authorizeRoles(['Admin']), categoryValidations.searchCategories, CategoryController.searchCategories);
router.get('/categories/:id', authenticateToken, authorizeRoles(['Admin']), categoryValidations.idParam, CategoryController.getCategory);
router.put('/categories/:id', authenticateToken, authorizeRoles(['Admin']), uploadCategoryFiles, categoryValidations.updateCategory, CategoryController.updateCategory);
router.delete('/categories/:id', authenticateToken, authorizeRoles(['Admin']), categoryValidations.idParam, CategoryController.deleteCategory);

router.get('/categories/:id/subcategories', authenticateToken, authorizeRoles(['Admin']), categoryValidations.getWithSubcategories, CategoryController.getCategoryWithSubcategories);
router.post('/categories/:id/subcategories', authenticateToken, authorizeRoles(['Admin']), uploadCategoryFiles, subCategoryValidations.createSubCategory, SubCategoryController.createSubcategory);
router.get('/subcategories/:id', authenticateToken, authorizeRoles(['Admin']), categoryValidations.getWithSubcategories, SubCategoryController.getSubcategory);
router.put('/subcategories/:id', authenticateToken, authorizeRoles(['Admin']), uploadCategoryFiles, subCategoryValidations.updateSubCategory, SubCategoryController.updateSubcategory);
router.delete('/subcategories/:id', authenticateToken, authorizeRoles(['Admin']), SubCategoryController.deleteSubcategory);

// Subcategory Form Fields Routes
router.get('/subcategories/:id/form-fields', authenticateToken, authorizeRoles(['Admin']), SubCategoryController.getSubcategoryFormFields);
router.post('/subcategories/:subcategoryId/form-fields', authenticateToken, authorizeRoles(['Admin']), subCategoryFormFieldValidations.createFormField, SubCategoryFormFieldController.createFormField);
router.post('/subcategories/:subcategoryId/form-fields/batch', authenticateToken, authorizeRoles(['Admin']), subCategoryFormFieldValidations.createManyFormFields, SubCategoryFormFieldController.createManyFormFields);
router.get('/form-fields/:id', authenticateToken, authorizeRoles(['Admin']), subCategoryFormFieldValidations.idParam, SubCategoryFormFieldController.getFormFieldById);
router.put('/form-fields/:id', authenticateToken, authorizeRoles(['Admin']), subCategoryFormFieldValidations.updateFormField, SubCategoryFormFieldController.updateFormField);
router.delete('/form-fields/:id', authenticateToken, authorizeRoles(['Admin']), subCategoryFormFieldValidations.idParam, SubCategoryFormFieldController.deleteFormField);
router.delete('/subcategories/:subcategoryId/form-fields', authenticateToken, authorizeRoles(['Admin']), subCategoryFormFieldValidations.subcategoryIdParam, SubCategoryFormFieldController.deleteFormFieldsBySubcategoryId);


router.get('/requests', authenticateToken, authorizeRoles(['Admin']), requestController.getRequests);
router.get('/requests/:id', authenticateToken, authorizeRoles(['Admin']), requestController.getRequestById);
router.put('/requests/:id', authenticateToken, authorizeRoles(['Admin']), requestController.updateRequest);
router.delete('/requests/:id', authenticateToken, authorizeRoles(['Admin']), requestController.adminDeleteRequest);
router.post('/requests/assign-sellers', authenticateToken, authorizeRoles(['Admin']), assignSellersValidation, requestController.assignSellers);
router.post('/requests/bulk-action', authenticateToken, authorizeRoles(['Admin']), bulkRequestActionValidation, requestController.bulkRequestAction);
router.post('/requests/merge', authenticateToken, authorizeRoles(['Admin']), createMergedRequestValidation, requestController.createMergedRequest);
router.post('/requests/merge-with-offers', authenticateToken, authorizeRoles(['Admin']), createMergedRequestWithOffersValidation, requestController.createMergedRequestWithOffers);

// Offer Management Routes
router.get('/offers', authenticateToken, authorizeRoles(['Admin']), offerValidations.filterOffers, offerController.getAllOffers);
router.get('/offers/:id', authenticateToken, authorizeRoles(['Admin']), offerValidations.idParam, offerController.getOfferById);
router.put('/offers/:id/status', authenticateToken, authorizeRoles(['Admin']), offerValidations.updateOfferStatus, offerController.updateOfferStatus);

// Order Management Routes
router.get('/orders', authenticateToken, authorizeRoles(['Admin']), orderController.getBuyerOrderForAdmin);
router.get('/orders/:id', authenticateToken, authorizeRoles(['Admin']), orderController.getOrderById);

// Business Information Management Routes
router.get('/business-information', authenticateToken, authorizeRoles(['Admin']), getBusinessInformationsValidation, businessInformationController.getAllBusinessInformations);
router.get('/business-information/:id', authenticateToken, authorizeRoles(['Admin']), getBusinessInformationValidation, businessInformationController.getBusinessInformationByIdAdmin);
router.put('/business-information/:id/verification-status', authenticateToken, authorizeRoles(['Admin']), updateVerificationStatusValidation, businessInformationController.updateVerificationStatus);

// Subscription Management Routes
router.post('/subscriptions', authenticateToken, authorizeRoles(['Admin']), createSubscriptionValidation, subscriptionController.createSubscription);
router.get('/subscriptions', authenticateToken, authorizeRoles(['Admin']), queryValidation, subscriptionController.getAllSubscriptions);
router.get('/subscriptions/stats', authenticateToken, authorizeRoles(['Admin']), subscriptionController.getSubscriptionStats);
router.get('/subscriptions/:id', authenticateToken, authorizeRoles(['Admin']), subscriptionIdValidation, subscriptionController.getSubscriptionById);
router.put('/subscriptions/:id', authenticateToken, authorizeRoles(['Admin']), updateSubscriptionValidation, subscriptionController.updateSubscription);
router.delete('/subscriptions/:id', authenticateToken, authorizeRoles(['Admin']), subscriptionIdValidation, subscriptionController.deleteSubscription);

// Trial Subscription Management Routes
router.put('/subscriptions/trial/duration', authenticateToken, authorizeRoles(['Admin']), updateTrialDurationValidation, subscriptionController.updateTrialDuration);
router.post('/subscriptions/trial/assign', authenticateToken, authorizeRoles(['Admin']), assignTrialValidation, subscriptionController.assignTrialToUsers);
router.post('/subscriptions/trial/assign-all', authenticateToken, authorizeRoles(['Admin']), subscriptionController.assignTrialToAllUsers);

// Sort Order Management Routes
router.put('/categories/:id/sort-order', authenticateToken, authorizeRoles(['Admin']), sortOrderController.updateCategorySortOrder);
router.put('/subcategories/:id/sort-order', authenticateToken, authorizeRoles(['Admin']), sortOrderController.updateSubCategorySortOrder);
router.put('/form-fields/:id/sort-order', authenticateToken, authorizeRoles(['Admin']), sortOrderController.updateFormFieldSortOrder);
router.post('/categories/reorder', authenticateToken, authorizeRoles(['Admin']), sortOrderController.reorderCategories);
router.post('/categories/:categoryId/subcategories/reorder', authenticateToken, authorizeRoles(['Admin']), sortOrderController.reorderSubCategories);
router.post('/subcategories/:subcategoryId/form-fields/reorder', authenticateToken, authorizeRoles(['Admin']), sortOrderController.reorderFormFields);
router.put('/categories/bulk-sort-order', authenticateToken, authorizeRoles(['Admin']), sortOrderController.bulkUpdateCategorySortOrder);
router.put('/subcategories/bulk-sort-order', authenticateToken, authorizeRoles(['Admin']), sortOrderController.bulkUpdateSubCategorySortOrder);
router.put('/form-fields/bulk-sort-order', authenticateToken, authorizeRoles(['Admin']), sortOrderController.bulkUpdateFormFieldSortOrder);

// Bulk Upload Routes
router.get('/bulk-upload/template/:format', authenticateToken, authorizeRoles(['Admin']), bulkUploadValidations.downloadTemplate, bulkUploadController.downloadTemplate);
router.post('/bulk-upload/preview', authenticateToken, authorizeRoles(['Admin']), uploadBulkFile, bulkUploadValidations.previewUpload, bulkUploadController.previewUpload);
router.post('/bulk-upload/subcategories', authenticateToken, authorizeRoles(['Admin']), uploadBulkFile, bulkUploadValidations.uploadSubcategoriesFile, bulkUploadController.processUpload);

module.exports = router;
