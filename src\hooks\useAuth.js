import { useRouter } from "next/router";
import { useEffect, useState } from "react";

export function useAuth() {
    const [user, setUser] = useState(null);
    const router = useRouter();

    useEffect(() => {
        const token = req.cookies.get("accessToken")?.value; 

        if (token) {
            fetch("/api/auth/me")
                .then((res) => res.json())
                .then((data) => setUser(data.user))
                .catch(() => {
                    localStorage.removeItem("token");
                    router.push("/login");
                });
        } else {
            router.push("/login");
        }
    }, [router]);

    return { user };
}
