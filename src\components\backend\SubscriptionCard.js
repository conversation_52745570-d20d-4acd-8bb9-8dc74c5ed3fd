"use client";

import React from 'react';
import { 
  getSubscriptionFromCookies, 
  hasActiveSubscription, 
  isOnTrialPlan, 
  getDaysRemaining 
} from '@/utils/user';

const SubscriptionCard = () => {
  const subscription = getSubscriptionFromCookies();
  const isActive = hasActiveSubscription();
  const isTrial = isOnTrialPlan();
  const daysRemaining = getDaysRemaining();

  if (!subscription) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Subscription</h3>
          <span className="px-3 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
            No Plan
          </span>
        </div>
        <p className="text-gray-600 text-sm">
          {"You don't have an active subscription. Consider upgrading to access premium features."}
        </p>
      </div>
    );
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getPlanTypeColor = (isTrial) => {
    return isTrial ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800';
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Subscription</h3>
        <div className="flex gap-2">
          {isTrial && (
            <span className="px-3 py-1 rounded-full text-xs bg-blue-100 text-blue-800 font-medium">
              Trial
            </span>
          )}
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
            {subscription.status}
          </span>
        </div>
      </div>

      <div className="space-y-4">
        {/* Plan Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-700">Plan Name</p>
            <p className="text-sm text-gray-900 mt-1">{subscription.plan?.name || 'N/A'}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-700">Price</p>
            <p className="text-sm text-gray-900 mt-1">
              ${subscription.plan?.price || '0'} 
              {subscription.plan?.duration_days && (
                <span className="text-gray-500">
                  /{subscription.plan.duration_days} days
                </span>
              )}
            </p>
          </div>
        </div>

        {/* Dates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-700">Start Date</p>
            <p className="text-sm text-gray-900 mt-1">
              {subscription.start_date ? formatDate(subscription.start_date) : 'N/A'}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-700">End Date</p>
            <p className="text-sm text-gray-900 mt-1">
              {subscription.end_date ? formatDate(subscription.end_date) : 'N/A'}
            </p>
          </div>
        </div>

        {/* Days Remaining */}
        {daysRemaining !== null && isActive && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Days Remaining</span>
              <span className={`text-sm font-semibold ${daysRemaining <= 7 ? 'text-red-600' : 'text-green-600'}`}>
                {daysRemaining} days
              </span>
            </div>
            {daysRemaining <= 7 && (
              <p className="text-xs text-red-600 mt-1">
                Your subscription expires soon. Consider renewing to continue enjoying premium features.
              </p>
            )}
          </div>
        )}

        {/* Plan Limits */}
        {subscription.plan && (
          <div className="border-t pt-4">
            <p className="text-sm font-medium text-gray-700 mb-3">Plan Limits</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {subscription.plan.max_requests && (
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-lg font-semibold text-gray-900">{subscription.plan.max_requests}</p>
                  <p className="text-xs text-gray-600">Max Requests</p>
                </div>
              )}
              {subscription.plan.max_offers && (
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-lg font-semibold text-gray-900">{subscription.plan.max_offers}</p>
                  <p className="text-xs text-gray-600">Max Offers</p>
                </div>
              )}
              {subscription.plan.max_orders && (
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-lg font-semibold text-gray-900">{subscription.plan.max_orders}</p>
                  <p className="text-xs text-gray-600">Max Orders</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Plan Features */}
        {subscription.plan?.features && (
          <div className="border-t pt-4">
            <p className="text-sm font-medium text-gray-700 mb-3">Features</p>
            <div className="space-y-2">
              {Object.entries(subscription.plan.features).map(([key, value]) => (
                <div key={key} className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 capitalize">
                    {key.replace(/_/g, ' ')}
                  </span>
                  <span className="text-sm text-gray-900">
                    {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Auto Renewal */}
        <div className="flex items-center justify-between pt-4 border-t">
          <span className="text-sm font-medium text-gray-700">Auto Renewal</span>
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            subscription.auto_renew 
              ? 'bg-green-100 text-green-800' 
              : 'bg-gray-100 text-gray-800'
          }`}>
            {subscription.auto_renew ? 'Enabled' : 'Disabled'}
          </span>
        </div>

        {/* Description */}
        {subscription.plan?.description && (
          <div className="pt-4 border-t">
            <p className="text-sm font-medium text-gray-700 mb-2">Description</p>
            <p className="text-sm text-gray-600">{subscription.plan.description}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubscriptionCard;
