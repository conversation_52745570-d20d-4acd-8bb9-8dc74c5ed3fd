"use client";

import React from "react";
import TextInput from "./TextInput";
import SelectInput from "./SelectInput";
import DatePicker from "react-datepicker";
import { format, isValid, parseISO } from "date-fns";
import "react-datepicker/dist/react-datepicker.css";

const DynamicFormField = ({
  field,
  fieldName,
  value,
  onChange,
  error,
  setFieldValue
}) => {
  // Helper function to format date
  const formatDate = (date) => {
    if (!date) return '';
    try {
      // Handle both Date objects and ISO strings
      const dateObj = date instanceof Date ? date : parseISO(date);
      if (!isValid(dateObj)) return '';
      return format(dateObj, 'yyyy-MM-dd');
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Handle date change for date fields
  const handleDateChange = (date) => {
    setFieldValue(`custom_fields.${fieldName}`, date ? date.toISOString().split('T')[0] : null);
  };

  // Render different input types based on field.input_type
  switch (field.input_type) {
    case 'SELECT':
      return (
        <SelectInput
          label={field.label_name}
          options={field.options.map(option => ({
            value: option,
            label: option
          }))}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={field.placeholder || `Select ${field.label_name}`}
          className="w-full h-11 !font-light"
          name={`custom_fields.${fieldName}`}
          required={field.is_required}
          error={error}
        />
      );
    
    case 'MULTISELECT':
      // For multiselect, we'd need a custom component or library
      // This is a simplified version using a select with multiple attribute
      return (
        <div>
          <label className="text-sm archivo font-medium mb-2 text-[#374151] inline-block">
            {field.label_name} {field.is_required && <span className="text-red-500"> *</span>}
          </label>
          <select
            multiple
            value={value || []}
            onChange={(e) => {
              const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
              onChange(selectedOptions);
            }}
            className={`border inter font-medium ${error ? 'border-red-500' : 'border-[#D1D5DB]'} text-[#374151] text-sm rounded-lg outline-0 block p-2.5 w-full`}
          >
            {field.options.map((option, idx) => (
              <option key={idx} value={option}>
                {option}
              </option>
            ))}
          </select>
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
      );
    
    case 'TEXTAREA':
      return (
        <TextInput
          className="w-full !font-light !placeholder:text-[#6B7280]"
          label={field.label_name}
          placeholder={field.placeholder || `Enter ${field.label_name}`}
          name={`custom_fields.${fieldName}`}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          required={field.is_required}
          error={error}
          multiline={true}
          row="4"
        />
      );
    
    case 'EMAIL':
      return (
        <TextInput
          className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
          label={field.label_name}
          placeholder={field.placeholder || `Enter your email`}
          name={`custom_fields.${fieldName}`}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          required={field.is_required}
          error={error}
          type="email"
        />
      );
    
    case 'NUMBER':
      return (
        <TextInput
          className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
          label={field.label_name}
          placeholder={field.placeholder || `Enter ${field.label_name}`}
          name={`custom_fields.${fieldName}`}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          required={field.is_required}
          error={error}
          type="number"
        />
      );
    
    case 'PASSWORD':
      return (
        <TextInput
          className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
          label={field.label_name}
          placeholder={field.placeholder || `Enter password`}
          name={`custom_fields.${fieldName}`}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          required={field.is_required}
          error={error}
          type="password"
        />
      );
    
    case 'DATE':
      return (
        <div className="datepicker">
          <label className="text-sm archivo font-medium text-[#374151] inline-block">
            {field.label_name} {field.is_required && <span className="text-red-500"> *</span>}
          </label>
          <div className={`${error ? 'border-red-500' : 'border-[#D1D5DB]'} rounded-lg`}>
            <DatePicker
              selected={value ? new Date(value) : null}
              onChange={(date) => handleDateChange(date)}
              dateFormat="yyyy-MM-dd"
              placeholderText={field.placeholder || "Select date"}
              className="w-full h-11 py-2 border-0 text-[#374151] text-sm rounded-lg focus:outline-none"
              wrapperClassName="w-full"
              showMonthDropdown
              showYearDropdown
              dropdownMode="select"
              isClearable={true}
              disabledKeyboardNavigation
              strictParsing
              preventOpenOnFocus
              customInput={
                <div className="relative w-full">
                  <div className={`flex items-center border inter font-medium ${error ? 'border-red-500' : 'border-inherit'} text-[#374151] text-sm rounded-lg outline-0 block p-2.5 h-11`}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="size-5 text-[#6B7280] mr-2"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"
                      />
                    </svg>
                    <div className="w-full">
                      {value ? (
                        <span className="text-[#374151] leading-[1.7] text-sm inter font-normal">
                          {formatDate(value)}
                        </span>
                      ) : (
                        <span className="text-[#6B7280] leading-[1.7] text-sm inter font-light">
                          {field.placeholder || "Select date"}
                        </span>
                      )}
                    </div>
                  </div>
                  <input
                    type="text"
                    className="absolute inset-0 opacity-0 cursor-pointer z-10 w-full"
                  />
                </div>
              }
            />
          </div>
          {error && <div className="text-red-500 text-sm mt-2">{error}</div>}
        </div>
      );
    
    case 'TIME':
      return (
        <TextInput
          className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
          label={field.label_name}
          placeholder={field.placeholder || `Enter time (HH:MM)`}
          name={`custom_fields.${fieldName}`}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          required={field.is_required}
          error={error}
          type="time"
        />
      );
    
    case 'CHECKBOX':
      return (
        <div className="flex items-start mb-4">
          <div className="flex items-center h-5">
            <input
              type="checkbox"
              checked={value || false}
              onChange={(e) => onChange(e.target.checked)}
              className="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-blue-300"
            />
          </div>
          <label className="ml-2 text-sm font-medium text-[#374151]">
            {field.label_name} {field.is_required && <span className="text-red-500"> *</span>}
          </label>
          {error && <div className="text-red-500 text-sm ml-2">{error}</div>}
        </div>
      );
    
    case 'RADIO':
      return (
        <div>
          <label className="text-sm archivo font-medium mb-2 text-[#374151] block">
            {field.label_name} {field.is_required && <span className="text-red-500"> *</span>}
          </label>
          <div className="space-y-2">
            {field.options.map((option, idx) => (
              <div key={idx} className="flex items-center">
                <input
                  type="radio"
                  id={`${fieldName}-${idx}`}
                  name={`custom_fields.${fieldName}`}
                  value={option}
                  checked={value === option}
                  onChange={() => onChange(option)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300"
                />
                <label htmlFor={`${fieldName}-${idx}`} className="ml-2 text-sm font-medium text-[#374151]">
                  {option}
                </label>
              </div>
            ))}
          </div>
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
      );
    
    case 'FILE':
      return (
        <div>
          <p className="text-sm archivo font-medium mb-2 text-[#374151] inline-block">
            {field.label_name} {field.is_required && <span className="text-red-500"> *</span>}
          </p>
          <div className="flex items-center space-x-4 h-11 w-full border inter font-medium border-[#D1D5DB] text-[#374151] text-sm rounded-lg relative outline-0 overflow-hidden">
            <input
              type="file"
              id={`file-input-${fieldName}`}
              className="hidden"
              onChange={(e) => {
                if (e.target.files && e.target.files.length > 0) {
                  onChange(e.target.files[0]);
                }
              }}
            />
            <label
              htmlFor={`file-input-${fieldName}`}
              className="flex items-center justify-center p-2 bg-[#F3F3F3] w-20 font-semibold cursor-pointer transition-colors h-full"
            >
              <svg
                width={10}
                height={20}
                viewBox="0 0 10 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 0.625C6.15994 0.626242 7.27202 1.08757 8.09222 1.90778C8.91243 2.72798 9.37376 3.84006 9.375 5V16.25C9.375 17.0788 9.04576 17.8737 8.45971 18.4597C7.87366 19.0458 7.0788 19.375 6.25 19.375C5.4212 19.375 4.62634 19.0458 4.04029 18.4597C3.45424 17.8737 3.125 17.0788 3.125 16.25V5C3.125 4.50272 3.32254 4.02581 3.67417 3.67417C4.02581 3.32254 4.50272 3.125 5 3.125C5.49728 3.125 5.97419 3.32254 6.32583 3.67417C6.67746 4.02581 6.875 4.50272 6.875 5V13.75C6.875 13.9158 6.80915 14.0747 6.69194 14.1919C6.57473 14.3092 6.41576 14.375 6.25 14.375C6.08424 14.375 5.92527 14.3092 5.80806 14.1919C5.69085 14.0747 5.625 13.9158 5.625 13.75V5C5.625 4.83424 5.55915 4.67527 5.44194 4.55806C5.32473 4.44085 5.16576 4.375 5 4.375C4.83424 4.375 4.67527 4.44085 4.55806 4.55806C4.44085 4.67527 4.375 4.83424 4.375 5V16.25C4.375 16.7473 4.57254 17.2242 4.92417 17.5758C5.27581 17.9275 5.75272 18.125 6.25 18.125C6.74728 18.125 7.22419 17.9275 7.57583 17.5758C7.92746 17.2242 8.125 16.7473 8.125 16.25V5C8.125 4.1712 7.79576 3.37634 7.20971 2.79029C6.62366 2.20424 5.8288 1.875 5 1.875C4.1712 1.875 3.37634 2.20424 2.79029 2.79029C2.20424 3.37634 1.875 4.1712 1.875 5V13.75C1.875 13.9158 1.80915 14.0747 1.69194 14.1919C1.57473 14.3092 1.41576 14.375 1.25 14.375C1.08424 14.375 0.925269 14.3092 0.808058 14.1919C0.690848 14.0747 0.625 13.9158 0.625 13.75V5C0.626241 3.84006 1.08758 2.72798 1.90778 1.90778C2.72798 1.08757 3.84006 0.626242 5 0.625Z"
                  fill="#6B7280"
                />
              </svg>
            </label>
            <div className="w-full">
              {value?.name ? (
                <span className="text-[#6B7280] leading-[1.7] text-sm inter font-normal">
                  {value.name}
                </span>
              ) : (
                <span className="text-[#6B7280] leading-[1.7] text-sm inter font-light">
                  {field.placeholder || "Upload file"}
                </span>
              )}
            </div>
          </div>
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
      );
    
    case 'PHONE':
      return (
        <TextInput
          className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
          label={field.label_name}
          placeholder={field.placeholder || `Enter phone number`}
          name={`custom_fields.${fieldName}`}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          required={field.is_required}
          error={error}
          type="tel"
        />
      );
    
    case 'URL':
      return (
        <TextInput
          className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
          label={field.label_name}
          placeholder={field.placeholder || `Enter URL`}
          name={`custom_fields.${fieldName}`}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          required={field.is_required}
          error={error}
          type="url"
        />
      );
    
    case 'COLOR':
      return (
        <div>
          <label className="text-sm archivo font-medium mb-2 text-[#374151] block">
            {field.label_name} {field.is_required && <span className="text-red-500"> *</span>}
          </label>
          <input
            type="color"
            value={value || '#000000'}
            onChange={(e) => onChange(e.target.value)}
            className="w-full h-11 border border-[#D1D5DB] rounded-lg p-1"
          />
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
      );
    
    case 'RANGE':
      return (
        <div>
          <label className="text-sm archivo font-medium mb-2 text-[#374151] block">
            {field.label_name} {field.is_required && <span className="text-red-500"> *</span>}
          </label>
          <input
            type="range"
            min={field.min_value || 0}
            max={field.max_value || 100}
            value={value || 0}
            onChange={(e) => onChange(e.target.value)}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <div className="text-sm text-gray-500 mt-1">{value || 0}</div>
          {error && <div className="text-red-500 text-sm mt-1">{error}</div>}
        </div>
      );
    
    default: // TEXT and other types
      return (
        <TextInput
          className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
          label={field.label_name}
          placeholder={field.placeholder || `Enter ${field.label_name}`}
          name={`custom_fields.${fieldName}`}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          required={field.is_required}
          error={error}
        />
      );
  }
};

export default DynamicFormField;
