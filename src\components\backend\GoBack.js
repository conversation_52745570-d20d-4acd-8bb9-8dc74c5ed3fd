"use client";

import Link from "next/link";
import React from "react";
import { useRouter } from "next/navigation";

const GoBack = () => {
  const router = useRouter();

  const handleGoBack = (e) => {
    e.preventDefault();
    router.back();
  };

  return (
    <Link
      href="/"
      onClick={handleGoBack}
      className="flex items-center space-x-2 archivo hover:text-blue-600 transition-colors"
    >
      <svg
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="transition-transform group-hover:-translate-x-1"
      >
        <mask
          id="mask0_428_14857"
          style={{ maskType: "alpha" }}
          maskUnits="userSpaceOnUse"
          x={0}
          y={0}
          width={20}
          height={20}
        >
          <rect width={20} height={20} fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_428_14857)">
          <path
            d="M6.52075 10.8333L11.1874 15.5L9.99992 16.6667L3.33325 10L9.99992 3.33334L11.1874 4.5L6.52075 9.16667H16.6666V10.8333H6.52075Z"
            fill="currentColor"
          />
        </g>
      </svg>

      <span>Go back</span>
    </Link>
  );
};

export default GoBack;
