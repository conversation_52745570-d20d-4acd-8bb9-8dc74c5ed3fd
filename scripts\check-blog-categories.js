/**
 * Quick script to check available blog categories
 */

const { prisma } = require('../config/dbConfig');

async function checkBlogCategories() {
  console.log('📋 Checking available blog categories...\n');

  try {
    const categories = await prisma.blog_categories.findMany({
      orderBy: { sort_order: 'asc' }
    });

    if (categories.length === 0) {
      console.log('❌ No categories found in database!');
      console.log('💡 Run: npm run seed:blog-categories');
      return;
    }

    console.log(`✅ Found ${categories.length} categories:\n`);
    
    categories.forEach((cat, index) => {
      console.log(`${index + 1}. ${cat.name}`);
      console.log(`   ID: ${cat.id}`);
      console.log(`   Slug: ${cat.slug}`);
      console.log(`   Active: ${cat.is_active ? '✅' : '❌'}`);
      console.log(`   Color: ${cat.color || 'None'}`);
      console.log('');
    });

    console.log('💡 Copy one of these IDs to use in your blog post creation.');
    
    // Check if the specific ID exists
    const specificId = '886330bf-a26e-436b-b489-ea0f9bb4352c';
    const specificCategory = await prisma.blog_categories.findUnique({
      where: { id: specificId }
    });

    console.log(`\n🔍 Checking specific ID: ${specificId}`);
    if (specificCategory) {
      console.log(`✅ Category found: ${specificCategory.name}`);
    } else {
      console.log('❌ This category ID does not exist in the database');
      console.log('💡 Use one of the IDs listed above instead');
    }

  } catch (error) {
    console.error('❌ Error checking categories:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkBlogCategories().catch(console.error);
