import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { baseurl } from "@/config";
import { getCookie, refreshToken } from "@/utils/auth";

// Create a custom base query with token refresh capability
const baseQueryWithReauth = async (args, api, extraOptions) => {

  console.log(args);
  // Create the base query
  const baseQuery = fetchBaseQuery({
    baseUrl: baseurl,
    prepareHeaders: (headers) => {
      const token = getCookie("accessToken");
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }
      return headers;
    },
  });

  // Check if args is valid
  if (!args || (typeof args === 'object' && !args.url)) {
    return {
      error: {
        status: 'CUSTOM_ERROR',
        data: { message: 'Invalid endpoint provided' }
      }
    };
  }

  // Execute the base query
  let result = await baseQuery(args, api, extraOptions);

  // If we get a 401 Unauthorized response, try to refresh the token

  if (result.error && result.error.status === 403) {
    console.log('Attempting to refresh token...');
    const refreshResult = await refreshToken();

    if (refreshResult) {
      // Retry the original query with new access token
      result = await baseQuery(args, api, extraOptions);
    } else {
      // If refresh failed, we need to handle this in the component
      console.log('Token refresh failed');
      // We'll let the component handle the 401 error
    }
  }

  if (result.error && result.error.status === 413) {
    

  }
  if (result.error && result.error.status === 401) {
    console.log('Attempting to refresh token...');

    // Try to get a new token
    const refreshResult = await refreshToken();

    // If we got a new token, retry the original request
    if (refreshResult) {
      console.log('Token refreshed successfully, retrying original request');

      // Retry the original query with new access token
      result = await baseQuery(args, api, extraOptions);
    } else {
      // If refresh failed, we need to handle this in the component
      console.log('Token refresh failed');
      // We'll let the component handle the 401 error
    }
  }

  return result;
};

export const api = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    fetchApi: builder.query({
      query: (params) => {
        // Handle case where params is undefined or not an object
        if (!params || typeof params !== 'object') {
          console.warn('Invalid parameters provided to fetchApi');
          return { url: '/invalid-endpoint', skip: true };
        }

        // Handle case where endpoint is undefined
        if (!params.endpoint) {
          console.warn('Endpoint is required for fetchApi');
          return { url: '/invalid-endpoint', skip: true };
        }

        return params.endpoint;
      },
      // Add keepUnusedDataFor to prevent refetching too often
      keepUnusedDataFor: 60, // Keep data for 60 seconds
    }),
    mutateApi: builder.mutation({
      query: (params) => {
        // Ensure required parameters are present
        if (!params.endpoint) {
          throw new Error('Endpoint is required');
        }

        return {
          url: params.endpoint,
          method: params.method || "POST", // Default to POST if not specified
          body: params.data || {},
          headers: params.headers || {},
        };
      },
    }),
    putApi: builder.mutation({
      query: ({ endpoint, data, headers }) => {
        // Create a query object with required fields
        const queryObj = {
          url: endpoint,
          method: "PUT",
          body: data,
        };

        // Only add headers if they are defined
        if (headers) {
          queryObj.headers = headers;
        }

        return queryObj;
      },
    }),
    patchApi: builder.mutation({
      query: ({ endpoint, data, headers }) => {
        // Create a query object with required fields
        const queryObj = {
          url: endpoint,
          method: "PATCH",
          body: data,
        };

        // Only add headers if they are defined
        if (headers) {
          queryObj.headers = headers;
        }

        return queryObj;
      },
    }),
    deleteApi: builder.mutation({
      query: ({ endpoint, headers }) => {
        // Create a query object with required fields
        const queryObj = {
          url: endpoint,
          method: "DELETE",
        };

        // Only add headers if they are defined
        if (headers) {
          queryObj.headers = headers;
        }

        return queryObj;
      },
    }),
  }),
});

export const {
  useFetchApiQuery,
  useMutateApiMutation,
  usePutApiMutation,
  usePatchApiMutation,
  useDeleteApiMutation,
} = api;
