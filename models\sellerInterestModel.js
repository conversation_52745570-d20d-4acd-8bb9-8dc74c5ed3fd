const { prisma } = require('../config/dbConfig');

class SellerInterestModel {
  /**
   * Get seller interested categories
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Array>} List of interested categories
   */
  static async getInterestedCategories(sellerId) {
    try {
      // Use Prisma's findMany with include to get the related categories
      const interests = await prisma.seller_interested_categories.findMany({
        where: {
          seller_id: sellerId,
          category: {
            is_deleted: false
          }
        },
        include: {
          category: true
        },
        orderBy: {
          category: {
            title: 'asc'
          }
        }
      });

      // Map the results to return only the category data
      return interests
        .filter(interest => interest.category) // Filter out any null categories
        .map(interest => ({
          id: interest.category.id,
          title: interest.category.title,
          description: interest.category.description,
          color: interest.category.color,
          image: interest.category.image,
          thumbnail: interest.category.thumbnail
        }));
    } catch (error) {
      console.error('Error getting seller interested categories:', error);
      throw error;
    }
  }

  /**
   * Get seller interested subcategories
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Array>} List of interested subcategories
   */
  static async getInterestedSubcategories(sellerId) {
    try {
      // First get the subcategory IDs that the seller is interested in
      const interests = await prisma.seller_interested_subcategories.findMany({
        where: {
          seller_id: sellerId
        },
        select: {
          subcategory_id: true
        }
      });

      // Extract the subcategory IDs
      const subcategoryIds = interests.map(interest => interest.subcategory_id);

      // If there are no interested subcategories, return an empty array
      if (subcategoryIds.length === 0) {
        return [];
      }

      // Get the subcategories with their categories
      const subcategories = await prisma.sub_categories.findMany({
        where: {
          id: {
            in: subcategoryIds
          },
          is_deleted: false
        },
        include: {
          category: {
            select: {
              id: true,
              title: true
            }
          }
        },
        orderBy: [
          {
            category: {
              title: 'asc'
            }
          },
          {
            title: 'asc'
          }
        ]
      });

      // Transform the result to match the expected format
      return subcategories.map(sc => ({
        id: sc.id,
        title: sc.title,
        description: sc.description,
        color: sc.color,
        image: sc.image,
        thumbnail: sc.thumbnail,
        category_id: sc.category.id,
        category_title: sc.category.title
      }));
    } catch (error) {
      console.error('Error getting seller interested subcategories:', error);
      throw error;
    }
  }

  /**
   * Add seller interested category
   * @param {string} sellerId - Seller ID
   * @param {string} categoryId - Category ID
   * @returns {Promise<Object>} Created interest
   */
  static async addInterestedCategory(sellerId, categoryId) {
    try {
      // Check if the category exists
      const category = await prisma.categories.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        throw new Error('Category not found');
      }

      // Check if the interest already exists
      const existingInterest = await prisma.seller_interested_categories.findUnique({
        where: {
          seller_id_category_id: {
            seller_id: sellerId,
            category_id: categoryId
          }
        }
      });

      if (existingInterest) {
        return existingInterest;
      }

      // Create the interest
      const interest = await prisma.seller_interested_categories.create({
        data: {
          seller_id: sellerId,
          category_id: categoryId
        }
      });

      return interest;
    } catch (error) {
      console.error('Error adding seller interested category:', error);
      throw error;
    }
  }

  /**
   * Add seller interested subcategory
   * @param {string} sellerId - Seller ID
   * @param {string} subcategoryId - Subcategory ID
   * @returns {Promise<Object>} Created interest
   */
  static async addInterestedSubcategory(sellerId, subcategoryId) {
    try {
      // Check if the subcategory exists
      const subcategory = await prisma.sub_categories.findUnique({
        where: { id: subcategoryId }
      });

      if (!subcategory) {
        throw new Error('Subcategory not found');
      }

      // Check if the interest already exists
      const existingInterest = await prisma.seller_interested_subcategories.findUnique({
        where: {
          seller_id_subcategory_id: {
            seller_id: sellerId,
            subcategory_id: subcategoryId
          }
        }
      });

      if (existingInterest) {
        return existingInterest;
      }

      // Create the interest
      const interest = await prisma.seller_interested_subcategories.create({
        data: {
          seller_id: sellerId,
          subcategory_id: subcategoryId
        }
      });

      return interest;
    } catch (error) {
      console.error('Error adding seller interested subcategory:', error);
      throw error;
    }
  }

  /**
   * Remove seller interested category
   * @param {string} sellerId - Seller ID
   * @param {string} categoryId - Category ID
   * @returns {Promise<boolean>} Success status
   */
  static async removeInterestedCategory(sellerId, categoryId) {
    try {
      await prisma.seller_interested_categories.deleteMany({
        where: {
          seller_id: sellerId,
          category_id: categoryId
        }
      });

      return true;
    } catch (error) {
      console.error('Error removing seller interested category:', error);
      throw error;
    }
  }

  /**
   * Remove seller interested subcategory
   * @param {string} sellerId - Seller ID
   * @param {string} subcategoryId - Subcategory ID
   * @returns {Promise<boolean>} Success status
   */
  static async removeInterestedSubcategory(sellerId, subcategoryId) {
    try {
      await prisma.seller_interested_subcategories.deleteMany({
        where: {
          seller_id: sellerId,
          subcategory_id: subcategoryId
        }
      });

      return true;
    } catch (error) {
      console.error('Error removing seller interested subcategory:', error);
      throw error;
    }
  }

  /**
   * Get subcategories by category IDs
   * @param {Array} categoryIds - Array of category IDs
   * @returns {Promise<Array>} List of subcategories
   */
  static async getSubcategoriesByCategoryIds(categoryIds) {
    try {
      // Use Prisma's findMany method which is safer and handles the IN clause properly
      const subcategories = await prisma.sub_categories.findMany({
        where: {
          category_id: {
            in: categoryIds
          },
          is_deleted: false
        },
        include: {
          category: {
            select: {
              id: true,
              title: true
            }
          }
        },
        orderBy: [
          {
            category: {
              title: 'asc'
            }
          },
          {
            title: 'asc'
          }
        ]
      });

      // Transform the result to match the expected format
      return subcategories.map(sc => ({
        id: sc.id,
        title: sc.title,
        description: sc.description,
        color: sc.color,
        image: sc.image,
        thumbnail: sc.thumbnail,
        category_id: sc.category.id,
        category_title: sc.category.title
      }));
    } catch (error) {
      console.error('Error getting subcategories by category IDs:', error);
      throw error;
    }
  }

  /**
   * Get subcategories for categories that the seller is interested in
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Array>} List of subcategories
   * @deprecated Use getInterestedCategories and getSubcategoriesByCategoryIds instead
   */
  static async getSubcategoriesForInterestedCategories(sellerId) {
    try {
      // Get the list of category IDs that the seller is interested in
      const interestedCategories = await this.getInterestedCategories(sellerId);
      const categoryIds = interestedCategories.map(category => category.id);

      // If there are no interested categories, return an empty array
      if (categoryIds.length === 0) {
        return [];
      }

      // Get subcategories for these category IDs
      return await this.getSubcategoriesByCategoryIds(categoryIds);
    } catch (error) {
      console.error('Error getting subcategories for interested categories:', error);
      throw error;
    }
  }
}

module.exports = SellerInterestModel;
