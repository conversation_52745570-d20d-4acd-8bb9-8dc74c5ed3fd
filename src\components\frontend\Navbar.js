"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { isLoggedIn, getUserName } from "@/utils/user";

const Navbar = () => {
  const pathname = usePathname();
  const [userLoggedIn, setUserLoggedIn] = useState(false);
  const [userName, setUserName] = useState("");

  useEffect(() => {
    // Check if user is logged in
    const loggedIn = isLoggedIn();
    setUserLoggedIn(loggedIn);
    if (loggedIn) {
      setUserName(getUserName());
    }
  }, []);
  return (
    <>
      <nav className="sticky top-0 z-50 bg-white w-full shadow-sm shadow-[#C5C5C545] border-b border-[#DCDCDC]">
        <div className="max-w-7xl flex flex-wrap items-center justify-between mx-auto p-4">
          <Link
            href="/"
            className="flex items-center space-x-3 rtl:space-x-reverse"
          >
            <img
              src="/assets/frontend_assets/markzoom_final_02.svg"
              alt="Company Logo"
              className="w-[250px]"
            />
          </Link>
          <div className="flex md:order-2 space-x-2 rtl:space-x-reverse">
            <button
              type="button"
              className="text-[#242B3A] flex items-center rounded-sm archivo font-medium text-sm px-4 py-2"
            >
              <svg
                width={15}
                height={15}
                className="mr-2"
                viewBox="0 0 15 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M7.81424 0.132812C3.89312 0.132812 0.703125 3.32281 0.703125 7.24392C0.703125 11.1651 3.89312 14.355 7.81424 14.355C11.7354 14.355 14.9253 11.1651 14.9253 7.24392C14.9253 3.32281 11.7354 0.132812 7.81424 0.132812ZM7.39757 3.62327C6.76368 3.59603 6.13617 3.50695 5.537 3.36208C5.63271 3.13531 5.73655 2.91753 5.84852 2.71115C6.30794 1.86458 6.87196 1.27051 7.39757 1.05534V3.62327ZM7.39757 4.45725V6.82726H4.79503C4.8317 5.90223 4.98936 4.98394 5.25011 4.14865C5.94108 4.3227 6.66645 4.42806 7.39757 4.45725ZM7.39757 7.66059V10.0306C6.66645 10.0597 5.94108 10.1652 5.25011 10.3392C4.98936 9.50392 4.8317 8.58561 4.79503 7.66059H7.39757ZM7.39757 10.8645V13.4325C6.87196 13.2173 6.30794 12.6233 5.84852 11.7767C5.73655 11.5703 5.63271 11.3525 5.537 11.1258C6.13617 10.9809 6.76356 10.8918 7.39757 10.8645ZM8.2309 10.8645C8.86481 10.8918 9.49228 10.9809 10.0915 11.1258C9.99575 11.3525 9.89192 11.5703 9.77993 11.7767C9.32055 12.6233 8.75655 13.2173 8.2309 13.4325V10.8645ZM8.2309 10.0306V7.66059H10.8334C10.7967 8.58561 10.6391 9.50392 10.3783 10.3392C9.68739 10.1652 8.96206 10.0597 8.2309 10.0306ZM8.2309 6.82726V4.45725C8.96206 4.42806 9.68739 4.3227 10.3783 4.14865C10.6391 4.98394 10.7967 5.90223 10.8334 6.82726H8.2309ZM8.2309 3.62327V1.05534C8.75655 1.27051 9.32055 1.86458 9.77993 2.71115C9.89192 2.91753 9.99575 3.13531 10.0915 3.36208C9.49228 3.50695 8.86481 3.59603 8.2309 3.62327ZM10.5124 2.31369C10.3037 1.92914 10.0771 1.59017 9.83815 1.301C10.6712 1.58551 11.4259 2.04069 12.0605 2.62446C11.6986 2.81836 11.3085 2.98676 10.8981 3.1275C10.7817 2.84472 10.653 2.5727 10.5124 2.31369ZM5.1161 2.31369C4.97548 2.5727 4.84679 2.84472 4.73036 3.1275C4.31999 2.98676 3.92991 2.81825 3.56792 2.62446C4.20258 2.04069 4.95725 1.58551 5.79036 1.301C5.55144 1.59017 5.32487 1.92903 5.1161 2.31369ZM4.44846 3.9107C4.16406 4.82053 3.99685 5.81239 3.96083 6.82726H1.55067C1.63986 5.47298 2.15994 4.23448 2.97548 3.24859C3.42806 3.50814 3.92372 3.73015 4.44846 3.9107ZM3.96083 7.66059C3.99685 8.67557 4.16406 9.6673 4.44846 10.5772C3.92372 10.7577 3.42806 10.9797 2.97548 11.2393C2.15994 10.2533 1.63986 9.01486 1.55067 7.66059H3.96083ZM4.73036 11.3604C4.84679 11.6431 4.97548 11.9151 5.1161 12.1741C5.32487 12.5587 5.55144 12.8977 5.79036 13.1869C4.95725 12.9023 4.20258 12.4471 3.56803 11.8634C3.92979 11.6696 4.31999 11.5011 4.73036 11.3604ZM10.5124 12.1741C10.653 11.9151 10.7817 11.6431 10.8981 11.3604C11.3085 11.5011 11.6986 11.6696 12.0605 11.8634C11.4259 12.4471 10.6712 12.9023 9.83815 13.1869C10.0771 12.8977 10.3036 12.5588 10.5124 12.1741ZM11.18 10.5772C11.4644 9.6673 11.6317 8.67548 11.6677 7.66059H14.0778C13.9886 9.01486 13.4685 10.2533 12.653 11.2393C12.2004 10.9797 11.7047 10.7577 11.18 10.5772ZM11.6677 6.82726C11.6317 5.81228 11.4644 4.82053 11.18 3.9107C11.7047 3.73015 12.2004 3.50814 12.653 3.24859C13.4685 4.23448 13.9886 5.47298 14.0778 6.82726H11.6677Z"
                  fill="black"
                />
              </svg>
              English{" "}
            </button>

            {userLoggedIn ? (
              <>
                <div className="text-[#242B3A] bg-[#EBECED] hover:bg-[#DCDCDC] transition-colors duration-300 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-sm archivo font-medium text-sm px-4 py-2 inline-flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-4 h-4 mr-1.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                  {userName}
                </div>
                <Link
                  href="/dashboard"
                  className="text-white bg-[#0A67F2] hover:bg-blue-700 transition-colors duration-300 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-sm archivo font-medium text-sm px-4 py-2 inline-flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-4 h-4 mr-1.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                    />
                  </svg>
                  Dashboard
                </Link>
              </>
            ) : (
              <>
                <Link
                  href="/register"
                  className="text-[#242B3A] bg-[#EBECED] hover:bg-[#DCDCDC] transition-colors duration-300 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-sm archivo font-medium text-sm px-4 py-2 inline-flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-4 h-4 mr-1.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
                    />
                  </svg>
                  Sign Up
                </Link>
                <Link
                  href="/login"
                  className="text-white bg-[#0A67F2] hover:bg-blue-700 transition-colors duration-300 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-sm archivo font-medium text-sm px-4 py-2 inline-flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-4 h-4 mr-1.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                    />
                  </svg>
                  Sign In
                </Link>
                <Link
                  href="/login"
                  className="text-[#0A67F2] hover:text-white hover:bg-[#0A67F2] border border-[#0A67F2] transition-colors duration-300 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-sm archivo font-medium text-sm px-4 py-2 inline-flex items-center"
                >
                  Post A Request
                </Link>
              </>
            )}
            <button
              data-collapse-toggle="navbar-sticky"
              type="button"
              className="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-sm md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200"
              aria-controls="navbar-sticky"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              <svg
                className="w-5 h-5"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 17 14"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M1 1h15M1 7h15M1 13h15"
                />
              </svg>
            </button>
          </div>
          <div
            className="items-center justify-between hidden w-full md:flex md:w-auto md:order-1"
            id="navbar-sticky"
          >
            <ul className="flex text-base archivo flex-col p-4 md:p-0 mt-4 font-medium border border-gray-100 rounded-lg bg-gray-50 md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white">
              <li>
                <Link
                  href="/home"
                  className={`block py-2 px-3 rounded-sm md:bg-transparent md:p-0 transition-colors duration-300 ${
                    pathname === "/home"
                      ? "text-white md:text-[#0A67F2]"
                      : "text-gray-900 hover:bg-gray-100 md:hover:bg-transparent md:hover:text-[#0A67F2]"
                  }`}
                >
                  Home
                </Link>
              </li>
              <li>
                <Link
                  href="/services"
                  className={`block py-2 px-3 rounded-sm md:bg-transparent md:p-0 transition-colors duration-300 ${
                    pathname === "/services"
                      ? "text-white md:text-[#0A67F2]"
                      : "text-gray-900 hover:bg-gray-100 md:hover:bg-transparent md:hover:text-[#0A67F2]"
                  }`}
                >
                  Service
                </Link>
              </li>
              <li>
                <Link
                  href="/about"
                  className={`block py-2 px-3 rounded-sm md:bg-transparent md:p-0 transition-colors duration-300 ${
                    pathname === "/about"
                      ? "text-white md:text-[#0A67F2]"
                      : "text-gray-900 hover:bg-gray-100 md:hover:bg-transparent md:hover:text-[#0A67F2]"
                  }`}
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="/blog"
                  className={`block py-2 px-3 rounded-sm md:bg-transparent md:p-0 transition-colors duration-300 ${
                    pathname === "/blog"
                      ? "text-white md:text-[#0A67F2]"
                      : "text-gray-900 hover:bg-gray-100 md:hover:bg-transparent md:hover:text-[#0A67F2]"
                  }`}
                >
                  Blog
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Navbar;
