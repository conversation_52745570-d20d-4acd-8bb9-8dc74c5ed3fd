/**
 * Test script for offer merged requests functionality
 * This script tests that offers return merged request children when applicable
 */

const { prisma } = require('../config/dbConfig');
const OfferModel = require('../models/offerModel');

// Test data IDs
const TEST_SELLER_ID = 'test-seller-offer-123';
const TEST_BUYER_ID = 'test-buyer-offer-123';
const TEST_REQUEST_ID = 'test-request-offer-456';
const TEST_CHILD_REQUEST_1_ID = 'test-child-offer-1';
const TEST_CHILD_REQUEST_2_ID = 'test-child-offer-2';
const TEST_OFFER_ID = 'test-offer-789';

async function setupTestData() {
  console.log('🔧 Setting up test data for offer merged requests...');
  
  try {
    // Clean up any existing test data
    await cleanupTestData();

    // Create test seller
    await prisma.users.create({
      data: {
        id: TEST_SELLER_ID,
        email: '<EMAIL>',
        password_hash: 'hashedpassword',
        first_name: 'Test',
        last_name: 'Seller',
        roles: {
          create: {
            role: {
              connectOrCreate: {
                where: { name: 'Seller' },
                create: { name: 'Seller' }
              }
            }
          }
        }
      }
    });

    // Create test buyer
    const buyer = await prisma.users.create({
      data: {
        id: TEST_BUYER_ID,
        email: '<EMAIL>',
        password_hash: 'hashedpassword',
        first_name: 'Test',
        last_name: 'Buyer',
        roles: {
          create: {
            role: {
              connectOrCreate: {
                where: { name: 'Buyer' },
                create: { name: 'Buyer' }
              }
            }
          }
        }
      }
    });

    // Create test categories
    const category = await prisma.categories.create({
      data: {
        title: 'Test Offer Category',
        description: 'Test category for offer merged requests'
      }
    });

    const subCategory = await prisma.sub_categories.create({
      data: {
        category_id: category.id,
        title: 'Test Offer Subcategory',
        description: 'Test subcategory'
      }
    });

    // Create parent request
    const parentRequest = await prisma.requests.create({
      data: {
        id: TEST_REQUEST_ID,
        buyer_id: buyer.id,
        category_id: category.id,
        sub_category_id: subCategory.id,
        title: 'Parent Request for Offer Test',
        description: 'This is a parent request with merged children for offer testing',
        status: 'Active',
        budget_min: 1000,
        budget_max: 5000,
        quantity: 1,
        urgency: 'Medium'
      }
    });

    // Create child requests
    const childRequest1 = await prisma.requests.create({
      data: {
        id: TEST_CHILD_REQUEST_1_ID,
        buyer_id: buyer.id,
        category_id: category.id,
        sub_category_id: subCategory.id,
        title: 'Child Request 1 for Offer',
        description: 'First child request for offer testing',
        status: 'Merged',
        budget_min: 500,
        budget_max: 1500,
        quantity: 2,
        urgency: 'High',
        custom_fields: {
          technology: 'React',
          experience_level: 'Senior'
        }
      }
    });

    const childRequest2 = await prisma.requests.create({
      data: {
        id: TEST_CHILD_REQUEST_2_ID,
        buyer_id: buyer.id,
        category_id: category.id,
        sub_category_id: subCategory.id,
        title: 'Child Request 2 for Offer',
        description: 'Second child request for offer testing',
        status: 'Merged',
        budget_min: 800,
        budget_max: 2000,
        quantity: 1,
        urgency: 'Low',
        custom_fields: {
          framework: 'Node.js',
          database: 'PostgreSQL'
        }
      }
    });

    // Create merged request relationships
    await prisma.request_merged_items.createMany({
      data: [
        {
          request_id: TEST_REQUEST_ID,
          merged_item_id: TEST_CHILD_REQUEST_1_ID,
          merged_by: buyer.id
        },
        {
          request_id: TEST_REQUEST_ID,
          merged_item_id: TEST_CHILD_REQUEST_2_ID,
          merged_by: buyer.id
        }
      ]
    });

    // Create attachments for child requests
    await prisma.request_attachments.createMany({
      data: [
        {
          request_id: TEST_CHILD_REQUEST_1_ID,
          file_path: '/uploads/child1-offer-file.jpg',
          file_type: 'image/jpeg',
          file_size: 512,
          description: 'Child 1 attachment for offer test'
        },
        {
          request_id: TEST_CHILD_REQUEST_2_ID,
          file_path: '/uploads/child2-offer-file.pdf',
          file_type: 'application/pdf',
          file_size: 1024,
          description: 'Child 2 attachment for offer test'
        }
      ]
    });

    // Create an offer for the parent request
    const offer = await prisma.offers.create({
      data: {
        id: TEST_OFFER_ID,
        request_id: TEST_REQUEST_ID,
        seller_id: TEST_SELLER_ID,
        offer_title: 'Test Offer for Merged Request',
        description: 'This is a test offer for a merged request',
        price: 3000,
        delivery_time: 14,
        status: 'Pending'
      }
    });

    console.log('✅ Test data setup completed');
    return { category, subCategory, buyer, parentRequest, childRequest1, childRequest2, offer };

  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  
  try {
    // Delete in reverse order of dependencies
    await prisma.offers.deleteMany({
      where: { id: TEST_OFFER_ID }
    });

    await prisma.request_attachments.deleteMany({
      where: {
        request_id: {
          in: [TEST_REQUEST_ID, TEST_CHILD_REQUEST_1_ID, TEST_CHILD_REQUEST_2_ID]
        }
      }
    });

    await prisma.request_merged_items.deleteMany({
      where: { request_id: TEST_REQUEST_ID }
    });

    await prisma.requests.deleteMany({
      where: {
        id: {
          in: [TEST_REQUEST_ID, TEST_CHILD_REQUEST_1_ID, TEST_CHILD_REQUEST_2_ID]
        }
      }
    });

    await prisma.user_roles.deleteMany({
      where: {
        user_id: {
          in: [TEST_SELLER_ID, TEST_BUYER_ID]
        }
      }
    });

    await prisma.users.deleteMany({
      where: {
        id: {
          in: [TEST_SELLER_ID, TEST_BUYER_ID]
        }
      }
    });

    // Clean up categories and subcategories
    await prisma.sub_categories.deleteMany({
      where: { title: 'Test Offer Subcategory' }
    });

    await prisma.categories.deleteMany({
      where: { title: 'Test Offer Category' }
    });

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
}

async function testOfferMergedRequests() {
  console.log('🧪 Testing offer merged requests functionality...\n');

  try {
    // Setup test data
    await setupTestData();

    // Test 1: Get offer by ID and check for merged children
    console.log('📋 Testing getOfferById with merged request...');
    const offer = await OfferModel.getOfferById(TEST_OFFER_ID);

    if (!offer) {
      throw new Error('Offer not found');
    }

    console.log('\n📊 Offer Results:');
    console.log('================');
    console.log(`✓ Offer ID: ${offer.id}`);
    console.log(`✓ Offer Title: ${offer.title}`);
    console.log(`✓ Request ID: ${offer.request.id}`);
    console.log(`✓ Request Title: ${offer.request.title}`);
    console.log(`✓ Request Is Merged: ${offer.request.is_merged}`);
    console.log(`✓ Number of Merged Children: ${offer.request.merged_children?.length || 0}`);

    // Verify merged children details
    if (offer.request.merged_children && offer.request.merged_children.length > 0) {
      console.log('\n📋 Merged Children Details:');
      offer.request.merged_children.forEach((child, index) => {
        console.log(`  Child ${index + 1}:`);
        console.log(`    - ID: ${child.id}`);
        console.log(`    - Title: ${child.title}`);
        console.log(`    - Description: ${child.description}`);
        console.log(`    - Budget: $${child.budget_min} - $${child.budget_max}`);
        console.log(`    - Quantity: ${child.quantity}`);
        console.log(`    - Urgency: ${child.urgency}`);
        console.log(`    - Custom Fields: ${JSON.stringify(child.custom_fields)}`);
        console.log(`    - Attachments: ${child.attachments?.length || 0}`);
        console.log(`    - Buyer: ${child.buyer.first_name} ${child.buyer.last_name} (${child.buyer.email})`);
        console.log(`    - Buyer Status: ${child.buyer.status} (Approved: ${child.buyer.is_approved})`);
        if (child.buyer.business_name) {
          console.log(`    - Business: ${child.buyer.business_name}`);
        }
      });
    }

    // Test 2: Get all offers and check for merged children
    console.log('\n📋 Testing getAllOffers with merged request...');
    const offersResult = await OfferModel.getAllOffers({ request_id: TEST_REQUEST_ID }, 1, 10);

    console.log('\n📊 All Offers Results:');
    console.log('======================');
    console.log(`✓ Total Offers: ${offersResult.meta.total}`);
    
    if (offersResult.data.length > 0) {
      const firstOffer = offersResult.data[0];
      console.log(`✓ First Offer Request Is Merged: ${firstOffer.request.is_merged}`);
      console.log(`✓ First Offer Merged Children: ${firstOffer.request.merged_children?.length || 0}`);
    }

    // Verification
    console.log('\n🔍 Verification:');
    console.log('================');

    if (offer.request.is_merged === true) {
      console.log('✅ is_merged flag is correctly set to true');
    } else {
      console.log('❌ is_merged flag should be true');
    }

    if (offer.request.merged_children && offer.request.merged_children.length === 2) {
      console.log('✅ Correct number of merged children (2)');
    } else {
      console.log(`❌ Expected 2 merged children, got ${offer.request.merged_children?.length || 0}`);
    }

    // Check if child request details are correctly returned
    const child1 = offer.request.merged_children?.find(c => c.id === TEST_CHILD_REQUEST_1_ID);
    const child2 = offer.request.merged_children?.find(c => c.id === TEST_CHILD_REQUEST_2_ID);

    if (child1 && child1.custom_fields.technology === 'React') {
      console.log('✅ Child 1 custom fields correctly returned');
    } else {
      console.log('❌ Child 1 custom fields not correctly returned');
    }

    if (child2 && child2.custom_fields.framework === 'Node.js') {
      console.log('✅ Child 2 custom fields correctly returned');
    } else {
      console.log('❌ Child 2 custom fields not correctly returned');
    }

    if (child1 && child1.attachments && child1.attachments.length > 0) {
      console.log('✅ Child 1 attachments correctly returned');
    } else {
      console.log('❌ Child 1 attachments not correctly returned');
    }

    if (child1 && child1.buyer && child1.buyer.email === '<EMAIL>') {
      console.log('✅ Child 1 buyer information correctly returned');
    } else {
      console.log('❌ Child 1 buyer information not correctly returned');
    }

    if (child2 && child2.buyer && child2.buyer.first_name === 'Test') {
      console.log('✅ Child 2 buyer information correctly returned');
    } else {
      console.log('❌ Child 2 buyer information not correctly returned');
    }

    console.log('\n🎉 Offer merged requests test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    // Cleanup test data
    await cleanupTestData();
  }
}

async function main() {
  console.log('🚀 Starting Offer Merged Requests Test Suite');
  console.log('=============================================\n');

  try {
    await testOfferMergedRequests();
    
    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Offer getOfferById returns merged children');
    console.log('- ✅ Offer getAllOffers returns merged children');
    console.log('- ✅ Child request details properly included');
    console.log('- ✅ Child request attachments included');
    console.log('- ✅ Custom fields from child requests included');
    console.log('- ✅ Budget, quantity, and urgency details included');
    console.log('- ✅ Buyer information for each child request included');

  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testOfferMergedRequests,
  setupTestData,
  cleanupTestData
};
