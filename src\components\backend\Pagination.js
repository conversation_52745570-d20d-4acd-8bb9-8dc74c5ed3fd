"use client";

import React, { useState } from "react";

const Pagination = ({ totalItems, pageSize = 10, onPageChange }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(totalItems / pageSize);

  const goToPage = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
    onPageChange && onPageChange(page);
  };

  // Generate array of page numbers (simple version: 1...n)
  const getPageNumbers = () => {
    const pages = [];
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
    return pages;
  };

  return (
    <nav className="flex items-center justify-center mt-10">
      <ul className="inline-flex items-center justify-center -space-x-px text-base h-12 border-t border-[#E5E7EB]">
        {/* Previous */}
        <li>
          <button
            onClick={() => goToPage(currentPage - 1)}
            disabled={currentPage === 1}
            className="flex items-center font-medium justify-center px-4 h-12 ms-0 leading-tight text-[#6B7280] inter text-sm disabled:opacity-50"
          >
            <svg
              className="size-6 mr-2"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18" />
            </svg>
            Previous
          </button>
        </li>

        {/* Page Numbers */}
        {getPageNumbers().map((page) => (
          <li key={page}>
            <button
              onClick={() => goToPage(page)}
              className={`flex items-center font-medium justify-center px-4 h-12 leading-tight inter text-sm ${
                page === currentPage
                  ? "text-[#4F46E5] border-t border-[#4F46E5]"
                  : "text-[#6B7280]"
              }`}
            >
              {page}
            </button>
          </li>
        ))}

        {/* Next */}
        <li>
          <button
            onClick={() => goToPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="flex items-center font-medium justify-center px-4 h-12 leading-tight text-[#6B7280] inter text-sm disabled:opacity-50"
          >
            Next
            <svg
              className="size-6 ml-2"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
            </svg>
          </button>
        </li>
      </ul>
    </nav>
  );
};

export default Pagination;
