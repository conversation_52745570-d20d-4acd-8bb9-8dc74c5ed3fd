import React from "react";
import ProfileDropdown from "./ProfileDropdown";
import CartIcon from "./CartIcon";
import { getUserRole } from "@/utils/user";

const Navbar = () => {
  const userRole = getUserRole();
  const isBuyer = userRole === 'Buyer';

  return (
    <>
      <header className="sticky archivo z-20 top-0 bg-white border-b border-[#d7dae0] p-2 flex items-center justify-between shadow-sm">
        <div className="flex items-center space-x-2 ml-auto">
          <div
            className="bg-white text-[#28283C] flex items-center space-x-1 p-1 rounded-full shadow-md"
            style={{
              boxShadow: "0px 4px 16px 0px rgba(171, 190, 209, 0.16)",
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="h-5 w-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0M3.124 7.5A8.969 8.969 0 0 1 5.292 3m13.416 0a8.969 8.969 0 0 1 2.168 4.5"
              />
            </svg>
            <span className="text-base font-medium">Notifications</span>
          </div>

          {/* Only show cart icon for buyers */}
          {isBuyer && <CartIcon />}

          <div
            className="bg-white text-[#28283C] flex items-center space-x-1 p-1 rounded-full shadow-md"
            style={{
              boxShadow: "0px 4px 16px 0px rgba(171, 190, 209, 0.16)",
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-5 h-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"
              />
            </svg>

            <span className="text-base font-medium">English</span>
          </div>
          <ProfileDropdown />
        </div>
      </header>
    </>
  );
};

export default Navbar;
