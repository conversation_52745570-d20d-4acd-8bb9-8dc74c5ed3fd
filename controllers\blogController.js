const BlogPostModel = require('../models/blogPostModel');
const BlogCategoryModel = require('../models/blogCategoryModel');
const BlogService = require('../services/blogService');
const sendResponse = require('../utils/sendResponse');

/**
 * Public Blog Controller - Handles public blog operations
 */
class BlogController {
  /**
   * Get published blog posts (Public)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getPublishedBlogPosts(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        category_id,
        category_slug,
        is_featured,
        search
      } = req.query;

      const filters = {
        ...(category_id && { category_id }),
        ...(category_slug && { category_slug }),
        ...(is_featured !== undefined && { is_featured: is_featured === 'true' }),
        ...(search && { search })
      };

      const result = await BlogPostModel.getPublishedBlogPosts(
        filters,
        parseInt(page),
        parseInt(limit)
      );

      // Format tags for all posts
      result.data = result.data.map(post => ({
        ...post,
        tags: BlogService.formatTags(post.tags)
      }));

      return sendResponse(
        res,
        true,
        'Blog posts retrieved successfully',
        result,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve blog posts',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Get published blog post by slug (Public)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBlogPostBySlug(req, res) {
    try {
      const { slug } = req.params;
      const { increment_view = 'true' } = req.query;

      const blogPost = await BlogPostModel.getPublishedBlogPostBySlug(
        slug,
        increment_view === 'true'
      );

      if (!blogPost) {
        return sendResponse(
          res,
          false,
          'Blog post not found',
          null,
          { general: ['Blog post not found'] },
          null,
          404
        );
      }

      // Format tags for response
      if (blogPost.tags) {
        blogPost.tags = BlogService.formatTags(blogPost.tags);
      }

      // Get related posts
      if (blogPost.category?.id) {
        const relatedPosts = await BlogPostModel.getRelatedBlogPosts(
          blogPost.id,
          blogPost.category.id,
          4
        );
        blogPost.related_posts = relatedPosts;
      }

      return sendResponse(
        res,
        true,
        'Blog post retrieved successfully',
        blogPost,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve blog post',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Get featured blog posts (Public)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getFeaturedBlogPosts(req, res) {
    try {
      const { limit = 5 } = req.query;

      const featuredPosts = await BlogPostModel.getFeaturedBlogPosts(
        parseInt(limit)
      );

      // Format tags for all posts
      const formattedPosts = featuredPosts.map(post => ({
        ...post,
        tags: BlogService.formatTags(post.tags)
      }));

      return sendResponse(
        res,
        true,
        'Featured blog posts retrieved successfully',
        formattedPosts,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve featured blog posts',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Get active blog categories (Public)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getActiveBlogCategories(req, res) {
    try {
      const categories = await BlogCategoryModel.getActiveBlogCategories();

      return sendResponse(
        res,
        true,
        'Blog categories retrieved successfully',
        categories,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve blog categories',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Get blog category by slug (Public)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBlogCategoryBySlug(req, res) {
    try {
      const { slug } = req.params;

      const category = await BlogCategoryModel.getBlogCategoryBySlug(slug);

      if (!category) {
        return sendResponse(
          res,
          false,
          'Blog category not found',
          null,
          { general: ['Blog category not found'] },
          null,
          404
        );
      }

      return sendResponse(
        res,
        true,
        'Blog category retrieved successfully',
        category,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve blog category',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Search blog posts (Public)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async searchBlogPosts(req, res) {
    try {
      const {
        q: search,
        page = 1,
        limit = 10,
        category_id,
        category_slug
      } = req.query;

      if (!search || search.trim().length < 2) {
        return sendResponse(
          res,
          false,
          'Search query must be at least 2 characters',
          null,
          { search: ['Search query must be at least 2 characters'] },
          null,
          400
        );
      }

      const filters = {
        search: search.trim(),
        ...(category_id && { category_id }),
        ...(category_slug && { category_slug })
      };

      const result = await BlogPostModel.getPublishedBlogPosts(
        filters,
        parseInt(page),
        parseInt(limit)
      );

      // Format tags for all posts
      result.data = result.data.map(post => ({
        ...post,
        tags: BlogService.formatTags(post.tags)
      }));

      return sendResponse(
        res,
        true,
        `Found ${result.meta.total} blog posts matching "${search}"`,
        result,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to search blog posts',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Get blog posts by category (Public)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBlogPostsByCategory(req, res) {
    try {
      const { slug } = req.params;
      const { page = 1, limit = 10 } = req.query;

      // First, verify category exists
      const category = await BlogCategoryModel.getBlogCategoryBySlug(slug);
      if (!category) {
        return sendResponse(
          res,
          false,
          'Blog category not found',
          null,
          { general: ['Blog category not found'] },
          null,
          404
        );
      }

      // Get posts for this category
      const result = await BlogPostModel.getPublishedBlogPosts(
        { category_id: category.id },
        parseInt(page),
        parseInt(limit)
      );

      // Format tags for all posts
      result.data = result.data.map(post => ({
        ...post,
        tags: BlogService.formatTags(post.tags)
      }));

      // Add category info to response
      result.category = category;

      return sendResponse(
        res,
        true,
        `Blog posts for category "${category.name}" retrieved successfully`,
        result,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve blog posts by category',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Get recent blog posts (Public)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getRecentBlogPosts(req, res) {
    try {
      const { limit = 5 } = req.query;

      const result = await BlogPostModel.getPublishedBlogPosts(
        {},
        1,
        parseInt(limit)
      );

      // Format tags for all posts
      const recentPosts = result.data.map(post => ({
        ...post,
        tags: BlogService.formatTags(post.tags)
      }));

      return sendResponse(
        res,
        true,
        'Recent blog posts retrieved successfully',
        recentPosts,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve recent blog posts',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }
}

module.exports = BlogController;
