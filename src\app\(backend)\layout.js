import Loading from "@/components/backend/Loading";
import Navbar from "@/components/backend/Navbar";
import Sidebar from "@/components/backend/Sidebar";
import { Suspense } from "react";

export default function Layout({ children }) {
  return (
    <Suspense fallback={<div> Loading... </div>}> 
      <div className="flex min-h-screen bg-gray-100">
        <div className="z-50 w-64 fixed inset-y-0 left-0">
          <Sidebar />
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col h-full ml-64">
          <Navbar />
          <main className="flex-1 p-6 w-full h-full">
            <div className="border border-[#f4f4f4] transition-all ease-in-out duration-300 rounded-xl">
              {children}
            </div>
          </main>
        </div>
      </div>
    </Suspense>
  );
}
