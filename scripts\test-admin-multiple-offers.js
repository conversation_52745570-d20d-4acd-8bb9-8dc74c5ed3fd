/**
 * Test script for admin multiple offers functionality (no merging)
 * This script tests the new admin endpoint for creating offers for multiple requests
 */

const { prisma } = require('../config/dbConfig');
const RequestService = require('../services/requestService');

// Test data IDs
const TEST_ADMIN_ID = 'test-admin-offers-123';
const TEST_BUYER_1_ID = 'test-buyer-offers-1';
const TEST_BUYER_2_ID = 'test-buyer-offers-2';
const TEST_BUYER_3_ID = 'test-buyer-offers-3';
const TEST_REQUEST_1_ID = 'test-request-offers-1';
const TEST_REQUEST_2_ID = 'test-request-offers-2';
const TEST_REQUEST_3_ID = 'test-request-offers-3';

async function setupTestData() {
  console.log('🔧 Setting up test data for admin multiple offers...');
  
  try {
    // Clean up any existing test data
    await cleanupTestData();

    // Create test admin
    await prisma.users.create({
      data: {
        id: TEST_ADMIN_ID,
        email: '<EMAIL>',
        password_hash: 'hashedpassword',
        first_name: 'Test',
        last_name: 'Admin',
        roles: {
          create: {
            role: {
              connectOrCreate: {
                where: { name: 'Admin' },
                create: { name: 'Admin' }
              }
            }
          }
        }
      }
    });

    // Create test buyers
    const buyers = await Promise.all([
      prisma.users.create({
        data: {
          id: TEST_BUYER_1_ID,
          email: '<EMAIL>',
          password_hash: 'hashedpassword',
          first_name: 'Alice',
          last_name: 'Johnson',
          business_name: 'Alice Tech Solutions',
          roles: {
            create: {
              role: {
                connectOrCreate: {
                  where: { name: 'Buyer' },
                  create: { name: 'Buyer' }
                }
              }
            }
          }
        }
      }),
      prisma.users.create({
        data: {
          id: TEST_BUYER_2_ID,
          email: '<EMAIL>',
          password_hash: 'hashedpassword',
          first_name: 'Bob',
          last_name: 'Smith',
          business_name: 'Bob Digital Agency',
          roles: {
            create: {
              role: {
                connectOrCreate: {
                  where: { name: 'Buyer' },
                  create: { name: 'Buyer' }
                }
              }
            }
          }
        }
      }),
      prisma.users.create({
        data: {
          id: TEST_BUYER_3_ID,
          email: '<EMAIL>',
          password_hash: 'hashedpassword',
          first_name: 'Carol',
          last_name: 'Davis',
          business_name: 'Carol Innovations',
          roles: {
            create: {
              role: {
                connectOrCreate: {
                  where: { name: 'Buyer' },
                  create: { name: 'Buyer' }
                }
              }
            }
          }
        }
      })
    ]);

    // Create test categories
    const category = await prisma.categories.create({
      data: {
        title: 'Test Multiple Offers Category',
        description: 'Test category for multiple offers functionality'
      }
    });

    const subCategory = await prisma.sub_categories.create({
      data: {
        category_id: category.id,
        title: 'Test Multiple Offers Subcategory',
        description: 'Test subcategory'
      }
    });

    // Create test requests
    const requests = await Promise.all([
      prisma.requests.create({
        data: {
          id: TEST_REQUEST_1_ID,
          buyer_id: buyers[0].id,
          category_id: category.id,
          sub_category_id: subCategory.id,
          title: 'E-commerce Website Development',
          description: 'Need a modern e-commerce website with payment integration',
          status: 'Active',
          budget_min: 3000,
          budget_max: 8000,
          quantity: 1,
          urgency: 'High'
        }
      }),
      prisma.requests.create({
        data: {
          id: TEST_REQUEST_2_ID,
          buyer_id: buyers[1].id,
          category_id: category.id,
          sub_category_id: subCategory.id,
          title: 'Mobile App UI/UX Design',
          description: 'Need professional UI/UX design for mobile application',
          status: 'Active',
          budget_min: 1500,
          budget_max: 4000,
          quantity: 1,
          urgency: 'Medium'
        }
      }),
      prisma.requests.create({
        data: {
          id: TEST_REQUEST_3_ID,
          buyer_id: buyers[2].id,
          category_id: category.id,
          sub_category_id: subCategory.id,
          title: 'Digital Marketing Campaign',
          description: 'Need comprehensive digital marketing strategy and execution',
          status: 'Active',
          budget_min: 2000,
          budget_max: 6000,
          quantity: 1,
          urgency: 'Low'
        }
      })
    ]);

    console.log('✅ Test data setup completed');
    return { category, subCategory, buyers, requests };

  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  
  try {
    // Delete in reverse order of dependencies
    
    // Delete offers first
    await prisma.offers.deleteMany({
      where: {
        seller_id: TEST_ADMIN_ID
      }
    });

    // Delete offer status changes
    await prisma.offer_status_changes.deleteMany({
      where: {
        offer: {
          seller_id: TEST_ADMIN_ID
        }
      }
    });

    // Delete request statuses
    await prisma.request_statuses.deleteMany({
      where: {
        request: {
          OR: [
            { id: { in: [TEST_REQUEST_1_ID, TEST_REQUEST_2_ID, TEST_REQUEST_3_ID] } },
            { buyer_id: { in: [TEST_BUYER_1_ID, TEST_BUYER_2_ID, TEST_BUYER_3_ID] } }
          ]
        }
      }
    });

    // Delete requests
    await prisma.requests.deleteMany({
      where: {
        OR: [
          { id: { in: [TEST_REQUEST_1_ID, TEST_REQUEST_2_ID, TEST_REQUEST_3_ID] } },
          { buyer_id: { in: [TEST_BUYER_1_ID, TEST_BUYER_2_ID, TEST_BUYER_3_ID] } }
        ]
      }
    });

    // Delete user roles
    await prisma.user_roles.deleteMany({
      where: {
        user_id: {
          in: [TEST_ADMIN_ID, TEST_BUYER_1_ID, TEST_BUYER_2_ID, TEST_BUYER_3_ID]
        }
      }
    });

    // Delete users
    await prisma.users.deleteMany({
      where: {
        id: {
          in: [TEST_ADMIN_ID, TEST_BUYER_1_ID, TEST_BUYER_2_ID, TEST_BUYER_3_ID]
        }
      }
    });

    // Clean up categories and subcategories
    await prisma.sub_categories.deleteMany({
      where: { title: 'Test Multiple Offers Subcategory' }
    });

    await prisma.categories.deleteMany({
      where: { title: 'Test Multiple Offers Category' }
    });

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
}

async function testAdminMultipleOffers() {
  console.log('🧪 Testing admin multiple offers functionality...\n');

  try {
    // Setup test data
    const testData = await setupTestData();

    // Test data for the API call
    const requestData = {
      message: "Bulk Offer Creation",
      description: "Admin-generated offers for multiple client requests across different projects",
      offers: [
        {
          request_id: TEST_REQUEST_1_ID,
          price: 5500,
          delivery_time: 45,
          offer_title: "Premium E-commerce Solution",
          offer_description: "Complete e-commerce website with advanced features and payment integration"
        },
        {
          request_id: TEST_REQUEST_2_ID,
          price: 2800,
          delivery_time: 20,
          offer_title: "Professional UI/UX Design Package",
          offer_description: "Modern and user-friendly mobile app design with prototypes"
        },
        {
          request_id: TEST_REQUEST_3_ID,
          price: 4200,
          delivery_time: 30,
          offer_title: "Comprehensive Marketing Strategy",
          offer_description: "Full digital marketing campaign with analytics and reporting"
        }
      ]
    };

    console.log('📋 Testing createMultipleOffers service...');
    const result = await RequestService.createMultipleOffers(
      requestData.message,
      requestData.description,
      requestData.offers,
      TEST_ADMIN_ID
    );

    console.log('\n📊 Results:');
    console.log('===========');
    console.log(`✓ Total Requests Processed: ${result.summary.total_requests_processed}`);
    console.log(`✓ Total Offers Created: ${result.summary.total_offers_created}`);
    console.log(`✓ Total Offers Failed: ${result.summary.total_offers_failed}`);
    console.log(`✓ Total Value: ${result.summary.total_value}`);
    console.log(`✓ Average Delivery Time: ${result.summary.average_delivery_time}`);
    console.log(`✓ Success Rate: ${result.summary.success_rate}`);

    console.log('\n📋 Created Offers:');
    result.created_offers.forEach((offer, index) => {
      console.log(`  Offer ${index + 1}:`);
      console.log(`    - ID: ${offer.id}`);
      console.log(`    - Title: ${offer.offer_title}`);
      console.log(`    - Price: $${offer.price}`);
      console.log(`    - Delivery Time: ${offer.delivery_time} days`);
      console.log(`    - Request: ${offer.request.title}`);
      console.log(`    - Buyer: ${offer.request.buyer.first_name} ${offer.request.buyer.last_name}`);
      console.log(`    - Status: ${offer.status}`);
    });

    if (result.failed_offers.length > 0) {
      console.log('\n❌ Failed Offers:');
      result.failed_offers.forEach((failed, index) => {
        console.log(`  Failed ${index + 1}:`);
        console.log(`    - Request ID: ${failed.request_id}`);
        console.log(`    - Error: ${failed.error}`);
      });
    }

    // Verification
    console.log('\n🔍 Verification:');
    console.log('================');

    if (result.created_offers.length === 3) {
      console.log('✅ Correct number of offers created (3)');
    } else {
      console.log(`❌ Expected 3 offers, got ${result.created_offers.length}`);
    }

    if (result.failed_offers.length === 0) {
      console.log('✅ No failed offers');
    } else {
      console.log(`❌ ${result.failed_offers.length} offers failed`);
    }

    // Check if offers have correct prices
    const offer1 = result.created_offers.find(o => o.request_id === TEST_REQUEST_1_ID);
    const offer2 = result.created_offers.find(o => o.request_id === TEST_REQUEST_2_ID);
    const offer3 = result.created_offers.find(o => o.request_id === TEST_REQUEST_3_ID);

    if (offer1 && offer1.price === 5500) {
      console.log('✅ Offer 1 has correct price ($5500)');
    } else {
      console.log('❌ Offer 1 price is incorrect');
    }

    if (offer2 && offer2.price === 2800) {
      console.log('✅ Offer 2 has correct price ($2800)');
    } else {
      console.log('❌ Offer 2 price is incorrect');
    }

    if (offer3 && offer3.price === 4200) {
      console.log('✅ Offer 3 has correct price ($4200)');
    } else {
      console.log('❌ Offer 3 price is incorrect');
    }

    // Check custom titles
    if (offer1 && offer1.offer_title === "Premium E-commerce Solution") {
      console.log('✅ Offer 1 has custom title');
    } else {
      console.log('❌ Offer 1 custom title is incorrect');
    }

    console.log('\n🎉 Admin multiple offers test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    // Cleanup test data
    await cleanupTestData();
  }
}

async function main() {
  console.log('🚀 Starting Admin Multiple Offers Test Suite');
  console.log('=============================================\n');

  try {
    await testAdminMultipleOffers();
    
    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Admin can create offers for multiple requests');
    console.log('- ✅ No merging logic required');
    console.log('- ✅ Custom offer titles and descriptions supported');
    console.log('- ✅ Pricing and delivery times are correctly set');
    console.log('- ✅ Request information is preserved');
    console.log('- ✅ Comprehensive summary statistics provided');
    console.log('- ✅ Error handling for failed offers');

  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testAdminMultipleOffers,
  setupTestData,
  cleanupTestData
};
