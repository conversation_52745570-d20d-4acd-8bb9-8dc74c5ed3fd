import Image from "next/image";
import Link from "next/link";
import React from "react";

const BlogCard = ({ id, img, title, designation, index }) => {
  return (
    <>
      <div
        className="bg-[#F4F5F8] overflow-hidden mb-3"
        data-aos="fade-up"
        data-aos-offset="200"
        data-aos-delay={50 * (index + 1)}
        data-aos-duration="500"
        data-aos-easing="ease-in-out"
      >
        <div className="relative">
          <Image
            src={img}
            width={1000}
            height={1000}
            quality={100}
            className="object-contain mx-auto w-full h-auto"
            alt={title || "Blog image"}
          />
          <span className="bg-[#336AEA] w-[50px] h-[54px] text-white text-xs font-bold px-2.5 py-1 mb-2 uppercase dm_sans flex items-center justify-center absolute top-4 right-4 text-center ">
            4 May
          </span>
        </div>

        <div className="flex flex-col border border-[#EBECED] border-t-0">
          <div className="p-4">
            <div className="flex items-center justify-between">
              <span className="dm_sans inline-block text-sm text-[#6A6F78] font-medium">
                By admin
              </span>
              <span className="dm_sans inline-block text-sm text-[#6A6F78] font-medium">
                {designation}{" "}
              </span>
            </div>
            <h4 className="text-[#0F0C1D] tracking-[-0.24px] font-semibold text-2xl outfit mt-3">
              {title}{" "}
            </h4>
          </div>
          <Link
            className="dm_sans p-4 flex items-center justify-between text-sm text-[#6A6F78] font-medium bg-[#F4F5F8]"
            href={`/blog/${id || 1}`}
          >
            <span className="dm_sans inline-block uppercase text-xs text-[#6A6F78] font-medium">
              Read More{" "}
            </span>
            <span className="dm_sans inline-block text-sm text-[#6A6F78] font-medium">
              <svg
                width={19}
                height={19}
                viewBox="0 0 19 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M10.5664 1.30469H8.31641C7.16063 1.30599 6.05256 1.7657 5.2353 2.58296C4.41804 3.40022 3.95833 4.50828 3.95703 5.66406V12.4141C3.95833 13.5698 4.41804 14.6779 5.2353 15.4952C6.05256 16.3124 7.16063 16.7721 8.31641 16.7734H10.5664C11.7222 16.7721 12.8303 16.3124 13.6475 15.4952C14.4648 14.6779 14.9245 13.5698 14.9258 12.4141V5.66406C14.9245 4.50828 14.4648 3.40022 13.6475 2.58296C12.8303 1.7657 11.7222 1.30599 10.5664 1.30469ZM14.082 5.66406V7.49219H9.86328V2.14844H10.5664C11.4985 2.14955 12.392 2.52031 13.0511 3.17937C13.7102 3.83844 14.0809 4.732 14.082 5.66406ZM8.31641 2.14844H9.01953V7.49219H4.80078V5.66406C4.8019 4.732 5.17265 3.83844 5.83172 3.17937C6.49078 2.52031 7.38435 2.14955 8.31641 2.14844ZM10.5664 15.9297H8.31641C7.38435 15.9286 6.49078 15.5578 5.83172 14.8988C5.17265 14.2397 4.8019 13.3461 4.80078 12.4141V8.33594H14.082V12.4141C14.0809 13.3461 13.7102 14.2397 13.0511 14.8988C12.392 15.5578 11.4985 15.9286 10.5664 15.9297Z"
                  fill="#6A6F78"
                />
              </svg>
            </span>
          </Link>
        </div>
      </div>
    </>
  );
};

export default BlogCard;
