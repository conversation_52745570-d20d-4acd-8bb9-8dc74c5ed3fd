const { validationResult } = require("express-validator");
const authService = require('../services/authService');
const userService = require('../services/userService');
const multer = require('multer');
const upload = multer();
const sendResponse = require('../utils/sendResponse');
const { formatValidationErrors } = require('../utils/validationFormatter');
// const RecaptchaVerifier = require('../utils/recaptchaVerifier');

class AuthController {
  // Mobile registration: send OTP to email
  async mobileRegister(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = formatValidationErrors(errors.array());
      return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
    }
    try {
      const result = await authService.mobileRegister(req.body);
      sendResponse(res, true, "OTP sent to email", result, null, null, 200);
    } catch (err) {
      if (err.type === 'ValidationError') {
        sendResponse(res, false, 'Validation failed', null, err.errors, null, 422);
      } else {
        sendResponse(res, false, err.message, null, err, null, 400);
      }
    }
  }

  // Verify OTP and complete registration
  async verifyOtp(req, res) {
    const { email, otp } = req.body;
    if (!email || !otp) {
      return sendResponse(res, false, "Email and OTP are required", null, null, null, 400);
    }
    try {
      const result = await authService.verifyOtp(email, otp);
      sendResponse(res, true, "Registration successful", result, null, null, 201);
    } catch (err) {
      if (err.type === 'ValidationError') {
        sendResponse(res, false, 'Validation failed', null, err.errors, null, 422);
      } else {
        sendResponse(res, false, err.message, null, err, null, 400);
      }
    }
  }

  // Register a new Buyer
  async register(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = formatValidationErrors(errors.array());
      return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
    }

    try {
      const result = await authService.register(req.body);
      // Return the same response format as login
      sendResponse(res, true, "Registration successful", result, null, null, 201);
    } catch (err) {
      if (err.type === 'ValidationError') {
        sendResponse(res, false, 'Validation failed', null, err.errors, null, 422);
      } else {
        sendResponse(res, false, err.message, null, err, null, 400);
      }
    }
  }

  // Login as Buyer or Seller only
  async login(req, res) {
    // Debug logging
    console.log('=== LOGIN DEBUG ===');
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Raw body:', req.body);
    console.log('Body type:', typeof req.body);
    console.log('Body keys:', Object.keys(req.body || {}));
    console.log('Email:', req.body?.email, 'Type:', typeof req.body?.email);
    console.log('Password:', req.body?.password ? '[PRESENT]' : '[MISSING]', 'Type:', typeof req.body?.password);
    console.log('reCAPTCHA:', req.body?.recaptcha_token ? '[PRESENT]' : '[MISSING]', 'Type:', typeof req.body?.recaptcha_token);
    console.log('=== END DEBUG ===');

    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Validation errors:', errors.array());
      const formattedErrors = formatValidationErrors(errors.array());
      return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
    }

    try {
      const { email, password, recaptcha_token } = req.body;

      // Verify reCAPTCHA if not in test environment and not disabled
      // if (process.env.NODE_ENV !== 'test' && process.env.RECAPTCHA_DISABLED !== 'true') {
      //   console.log('Verifying reCAPTCHA token...');

      //   if (!recaptcha_token) {
      //     return sendResponse(res, false, "reCAPTCHA verification is required", null, { recaptcha_token: ["reCAPTCHA verification is required"] }, null, 400);
      //   }

      //   // Get user's IP address
      //   const userIP = RecaptchaVerifier.getUserIP(req);

      //   // Verify reCAPTCHA token
      //   const verificationResult = await RecaptchaVerifier.verifyToken(recaptcha_token, userIP);

      //   if (!verificationResult.success) {
      //     console.log('reCAPTCHA verification failed:', verificationResult.error);
      //     return sendResponse(res, false, "reCAPTCHA verification failed", null, { recaptcha_token: [verificationResult.error || 'reCAPTCHA verification failed'] }, null, 400);
      //   }

      //   console.log('reCAPTCHA verification successful:', {
      //     score: verificationResult.score,
      //     action: verificationResult.action,
      //     hostname: verificationResult.hostname
      //   });
      // } else {
      //   console.log('Skipping reCAPTCHA verification (test environment or disabled)');
      // }

      const result = await authService.login(email, password);
      return sendResponse(res, true, "Login successful", result);

    } catch (err) {
      console.error("Login error:", err.message);

      if (err.message.includes("not verified")) {
        return sendResponse(res, false, "Email not verified. Please check your email.", null, err, null, 403);
      }
      if (err.message.includes("User not found")) {
        return sendResponse(res, false, "User not found", null, err, null, 404);
      }
      if (err.message.includes("Invalid credentials")) {
        return sendResponse(res, false, "Invalid email or password", null, err, null, 401);
      }
      if (err.message.includes("Access denied")) {
        return sendResponse(res, false, err.message, null, { general: [err.message] }, null, 403);
      }

      return sendResponse(res, false, "Something went wrong, please try again later.", null, err, null, 500);
    }
  }

  // Logout user and invalidate token
  async logout(req, res) {
    try {
      const result = await authService.logout(req.user.id);
      sendResponse(res, true, "Logout successful", result);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Refresh JWT token
  async refreshToken(req, res) {
    try {
      const { token } = req.body;
      if (!token) {
        return sendResponse(res, false, 'Refresh token is required', null, null, null, 400);
      }

      const result = await authService.refreshToken(token);
      sendResponse(res, true, 'Token refreshed successfully', result);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Forgot Password (Send Reset Email)
  async forgotPassword(req, res) {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = formatValidationErrors(errors.array());
      return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
    }

    try {
      const { email } = req.body;
      const result = await authService.forgotPassword(email);
      sendResponse(res, true, 'Password reset email sent', result);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Reset Password
  async resetPassword(req, res) {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = formatValidationErrors(errors.array());
      return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
    }

    try {
      const { token, newPassword } = req.body;
      const result = await authService.resetPassword(token, newPassword);
      sendResponse(res, true, 'Password reset successful', result);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Change Password for Authenticated User
  async changePassword(req, res) {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = formatValidationErrors(errors.array());
      return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
    }

    try {
      const { currentPassword, newPassword } = req.body;
      const result = await authService.changePassword(req.user.id, currentPassword, newPassword);
      sendResponse(res, true, 'Password changed successfully', result);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Verify Email After Registration
  async verifyEmail(req, res) {
    try {
      const { token } = req.body;
      if (!token) {
        return sendResponse(res, false, 'Verification token is required', null, null, null, 400);
      }

      const result = await authService.verifyEmail(token);
      sendResponse(res, true, 'Email verified successfully', result);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Get the currently authenticated user
  async getAuthenticatedUser(req, res) {
    try {
      const result = await userService.getUserById(req.user.id);
      sendResponse(res, true, 'User retrieved successfully', result);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // New user registration with username, password, and user type
  async newUserRegister(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = formatValidationErrors(errors.array());
      return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
    }

    try {
      const result = await authService.newUserRegister(req.body);
      sendResponse(res, true, "Registration successful", result, null, null, 201);
    } catch (err) {
      if (err.type === 'ValidationError') {
        sendResponse(res, false, 'Validation failed', null, err.errors, null, 422);
      } else {
        sendResponse(res, false, err.message, null, err, null, 400);
      }
    }
  }

  // Login with username or email
  async newUserLogin(req, res) {
    try {
      const { login, password } = req.body;

      // Validate input
      if (!login || !password) {
        return sendResponse(res, false, 'Username/email and password are required', null, null, null, 400);
      }

      const result = await authService.newUserLogin(login, password);
      return sendResponse(res, true, "Login successful", result);

    } catch (err) {
      console.error("Login error:", err.message);

      if (err.message.includes("not verified")) {
        return sendResponse(res, false, "Email not verified. Please check your email.", null, err, null, 403);
      }
      if (err.message.includes("User not found")) {
        return sendResponse(res, false, "User not found", null, err, null, 404);
      }
      if (err.message.includes("Invalid credentials")) {
        return sendResponse(res, false, "Invalid username/email or password", null, err, null, 401);
      }
      if (err.message.includes("pending approval")) {
        return sendResponse(res, false, err.message, null, { general: [err.message] }, null, 403);
      }
      if (err.message.includes("not active")) {
        return sendResponse(res, false, err.message, null, { general: [err.message] }, null, 403);
      }

      return sendResponse(res, false, "Something went wrong, please try again later.", null, err, null, 500);
    }
  }

  // Complete user profile
  async completeProfile(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = formatValidationErrors(errors.array());
      return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
    }

    try {
      const userId = req.user.id;
      const result = await authService.completeProfile(userId, req.body, req.files);

      // Use the message from the service result, or fallback to default
      const message = result.message || "Profile completed successfully";

      sendResponse(res, true, message, result, null, null, 200);
    } catch (err) {
      console.error("Complete profile error:", err.message);
      sendResponse(res, false, err.message || "Failed to complete profile", null, err, null, 400);
    }
  }

  // Get profile completion status
  async getProfileStatus(req, res) {
    try {
      const userId = req.user.id;
      const result = await authService.getProfileStatus(userId);
      sendResponse(res, true, "Profile status retrieved successfully", result);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Upload profile picture
  async uploadProfilePicture(req, res) {
    try {
      const userId = req.user.id;

      if (!req.file) {
        return sendResponse(res, false, "No profile picture file provided", null, null, null, 400);
      }

      const result = await authService.uploadProfilePicture(userId, req.file);
      sendResponse(res, true, "Profile picture uploaded successfully", result, null, null, 200);
    } catch (err) {
      console.error("Upload profile picture error:", err.message);
      sendResponse(res, false, err.message || "Failed to upload profile picture", null, err, null, 400);
    }
  }
}

module.exports = new AuthController();