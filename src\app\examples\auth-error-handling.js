"use client";

import { useState, useEffect } from 'react';
import { useFetchApiQuery } from '@/redux/services/api';
import AuthErrorHandler from '@/components/AuthErrorHandler';

/**
 * Example component showing how to handle authentication errors
 */
const ExampleWithAuthErrorHandling = () => {
  // Example API call that might return 401 or 403
  const { data, error, isLoading } = useFetchApiQuery({
    endpoint: "/some-protected-endpoint",
    skip: false, // Ensure the query always runs with a valid endpoint
  });

  // Include the AuthErrorHandler component to handle auth errors
  return (
    <div>
      {/* This component will handle 401/403 errors automatically */}
      <AuthErrorHandler error={error} />

      {isLoading ? (
        <p>Loading...</p>
      ) : error ? (
        <p>Error: {error.data?.message || 'An error occurred'}</p>
      ) : (
        <div>
          <h1>Data loaded successfully</h1>
          <pre>{JSON.stringify(data, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

export default ExampleWithAuthErrorHandling;
