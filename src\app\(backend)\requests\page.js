
"use client";
export const dynamic = "force-dynamic";
import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export default function RequestsRedirect() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get the current query parameters
    const queryString = searchParams.toString();

    // Redirect to the list page with the same query parameters
    router.push(`/requests/list${queryString ? `?${queryString}` : ''}`);
  }, [router, searchParams]);

  return (
    <div className="flex justify-center items-center h-64">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
  );
}
