const RequestService = require('../services/requestService');
const { saveBufferToFile } = require('../utils/fileUpload');
const sendResponse = require('../utils/sendResponse');
const { validationResult } = require('express-validator');
const { formatValidationErrors } = require('../utils/validationFormatter');

class RequestController {
  static parseRequestFormData(data) {
    // Start with a clean object
    const parsedData = { ...data };

    // Only parse fields that are actually in the request body
    if ('quantity' in data) {
      parsedData.quantity = data.quantity ? parseInt(data.quantity) : 1;
    }

    if ('budget_min' in data) {
      parsedData.budget_min = data.budget_min ? parseFloat(data.budget_min) : null;
    }

    if ('budget_max' in data) {
      parsedData.budget_max = data.budget_max ? parseFloat(data.budget_max) : null;
    }

    // Parse removed_images if it's a string
    if (data.removed_images && typeof data.removed_images === 'string') {
      try {
        parsedData.removed_images = JSON.parse(data.removed_images);
      } catch (e) {
        console.error('Error parsing removed_images JSON:', e);
        parsedData.removed_images = [];
      }
    }

    // Parse custom fields from form data
    const customFields = {};
    Object.keys(data).forEach(key => {
      if (key.startsWith('custom_fields[') && key.endsWith(']')) {
        // Extract field name from custom_fields[field_name] format
        const fieldName = key.slice(14, -1); // Remove 'custom_fields[' and ']'
        customFields[fieldName] = data[key];
      }
    });

    // Only add custom_fields if there are any
    if (Object.keys(customFields).length > 0) {
      parsedData.custom_fields = customFields;
    }

    return parsedData;
  }

  /**
   * Create a new request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async createRequest(req, res) {
    try {
      console.log(req.user);
      if (!req.user.roles.includes('Buyer')) {
        return sendResponse(res, false, 'Only buyers can create requests', null, null, null, 403);
      }

      const parsedData = RequestController.parseRequestFormData(req.body);
      const files = req.files;

      if (files?.image) {
        parsedData.attachments = files.image.map(image => ({
          file_path: saveBufferToFile(image.buffer, image.originalname, 'uploads/request_attachments'),
          file_type: image.mimetype,
          file_size: image.size,
          description: image.originalname,
          is_public: true
        }));
      }

      if (files?.file?.[0]) {
        parsedData.file = saveBufferToFile(files.file[0].buffer, files.file[0].originalname, 'uploads/additional_files');
      }

      const request = await RequestService.createRequest(parsedData, req.user.id);
      return sendResponse(res, true, 'Request submitted successfully', request, null, null, 201);
    } catch (error) {
      return sendResponse(res, false, error.message, null, error, null, 400);
    }
  }

  /**
   * Get request by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getRequestById(req, res) {
    try {
      console.log(req.user.roles.includes('Admin'));
      const request = await RequestService.getRequestById(
        req.params.id,
        req.user.id,
        req.user.roles
      );
      return sendResponse(res, true, 'Request retrieved successfully', request);
    } catch (error) {
      if (error.message === 'Request not found') {
        return sendResponse(res, false, error.message, null, error, null, 404);
      }
      if (error.message === 'Unauthorized access') {
        return sendResponse(res, false, error.message, null, error, null, 403);
      }
      return sendResponse(res, false, error.message, null, error, null, 400);
    }
  }

  /**
   * Update request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateRequest(req, res) {
    try {
      // Allow both buyers (for their own requests) and admins (for any request)
      // The service layer will handle the specific authorization checks

      const parsedData = RequestController.parseRequestFormData(req.body);
      const files = req.files;

      // Handle file uploads if present
      if (files) {
        // Handle image attachments
        if (files.image && files.image.length > 0) {
          parsedData.new_attachments = files.image.map(image => ({
            file_path: saveBufferToFile(image.buffer, image.originalname, 'uploads/request_attachments'),
            file_type: image.mimetype,
            file_size: image.size,
            description: image.originalname,
            is_public: true
          }));
        }

        // Handle additional file
        if (files.file && files.file[0]) {
          parsedData.new_file = saveBufferToFile(
            files.file[0].buffer,
            files.file[0].originalname,
            'uploads/additional_files'
          );
        }
      }

      // Ensure removed_images is an array
      if (parsedData.removed_images && !Array.isArray(parsedData.removed_images)) {
        parsedData.removed_images = [];
      }

      // Validate file types and sizes
      if (parsedData.new_attachments) {
        const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        const maxImageSize = 5 * 1024 * 1024; // 5MB

        for (const attachment of parsedData.new_attachments) {
          if (!allowedImageTypes.includes(attachment.file_type)) {
            return sendResponse(
              res,
              false,
              'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.',
              null,
              { image: ['Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'] },
              null,
              422
            );
          }

          if (attachment.file_size > maxImageSize) {
            return sendResponse(
              res,
              false,
              'File size exceeds the limit of 5MB.',
              null,
              { image: ['File size exceeds the limit of 5MB.'] },
              null,
              422
            );
          }
        }
      }

      const updatedRequest = await RequestService.updateRequest(
        req.params.id,
        parsedData,
        req.user,
      );

      return sendResponse(res, true, 'Request updated successfully', updatedRequest);
    } catch (error) {
      if (error.message === 'Request not found') {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 404);
      }
      if (error.message.includes('Unauthorized') || error.message.includes('Buyers cannot')) {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 403);
      }
      return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 400);
    }
  }

  /**
   * Delete request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async deleteRequest(req, res) {
    try {
      await RequestService.deleteRequest(req.params.id, req.user.id, req.user.role);
      return sendResponse(res, true, 'Request deleted successfully');
    } catch (error) {
      if (error.message === 'Request not found') {
        return sendResponse(res, false, error.message, null, error, null, 404);
      }
      if (error.message.includes('Unauthorized')) {
        return sendResponse(res, false, error.message, null, error, null, 403);
      }
      return sendResponse(res, false, error.message, null, error, null, 400);
    }
  }

  /**
   * Admin delete request - completely removes the request and all related data
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async adminDeleteRequest(req, res) {
    try {
      // Only admins can use this endpoint (already checked by middleware)
      const result = await RequestService.adminDeleteRequest(req.params.id, req.user.id);
      return sendResponse(res, true, 'Request and all related data deleted successfully', result);
    } catch (error) {
      if (error.message === 'Request not found') {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 404);
      }
      return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 400);
    }
  }

  /**
   * Get filtered requests
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getRequests(req, res) {
    try {
      const result = await RequestService.getRequests(
        req.query,
        req.user,
        parseInt(req.query.page) || 1,
        parseInt(req.query.limit) || 10
      );
      return sendResponse(res, true, 'Requests retrieved successfully', result.data, null, {
        count: result.data.length,
        total: result.total,
        page: result.page,
        limit: result.limit
      });
    } catch (error) {
      return sendResponse(res, false, error.message, null, error, null, 400);
    }
  }

  /**
   * Add attachment to request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async addAttachment(req, res) {
    try {
      const attachment = await RequestService.addAttachment(
        req.params.id,
        req.body,
        req.user.id,
        req.user.role
      );
      return sendResponse(res, true, 'Attachment added successfully', attachment, null, null, 201);
    } catch (error) {
      if (error.message === 'Request not found') {
        return sendResponse(res, false, error.message, null, error, null, 404);
      }
      if (error.message.includes('Unauthorized')) {
        return sendResponse(res, false, error.message, null, error, null, 403);
      }
      return sendResponse(res, false, error.message, null, error, null, 400);
    }
  }

  /**
   * Delete attachment from request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async deleteAttachment(req, res) {
    try {
      // Allow both buyers (for their own requests) and admins (for any request)
      // The service layer will handle the specific authorization checks

      await RequestService.deleteAttachment(
        req.params.attachmentId,
        req.user.id,
        req.user.roles
      );

      return sendResponse(res, true, 'Attachment deleted successfully');
    } catch (error) {
      if (error.message === 'Attachment not found') {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 404);
      }
      if (error.message.includes('Unauthorized')) {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 403);
      }
      return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 400);
    }
  }

  /**
   * Get request status history
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getRequestStatusHistory(req, res) {
    try {
      // For buyers, we need to check if it's their own request
      // For admins, they can access any request's status history
      let statusHistory;

      if (req.user.roles.includes('Admin')) {
        // Admins can view any request's status history
        statusHistory = await RequestService.getRequestStatusHistoryForAdmin(
          req.params.id
        );
      } else if (req.user.roles.includes('Buyer')) {
        // Buyers can only view their own requests' status history
        statusHistory = await RequestService.getRequestStatusHistory(
          req.params.id,
          req.user.id
        );
      } else {
        return sendResponse(res, false, 'Unauthorized to access request status history', null, null, null, 403);
      }

      return sendResponse(res, true, 'Request status history retrieved successfully', statusHistory);
    } catch (error) {
      if (error.message.includes('Request not found') || error.message.includes('not authorized')) {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 404);
      }

      return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 400);
    }
  }

  /**
   * Assign sellers to a request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async assignSellers(req, res) {
    try {
      const { request_id, seller_ids, notes } = req.body;

      const updatedRequest = await RequestService.assignSellers(
        request_id,
        seller_ids,
        req.user.id,
        req.user.roles,
        notes
      );

      return sendResponse(
        res,
        true,
        `Successfully assigned ${seller_ids.length} seller(s) to the request`,
        updatedRequest,
        null,
        null,
        200
      );
    } catch (error) {
      if (error.message === 'Request not found') {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 404);
      }
      if (error.message.includes('Unauthorized')) {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 403);
      }
      if (error.message.includes('already assigned')) {
        return sendResponse(res, false, error.message, null, { seller_ids: [error.message] }, null, 422);
      }
      return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 400);
    }
  }

  /**
   * Cancel a request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async cancelRequest(req, res) {
    try {
      if (!req.user.roles.includes('Buyer')) {
        return sendResponse(res, false, 'Only buyers can cancel their requests', null, null, null, 403);
      }

      const { reason } = req.body;

      const cancelledRequest = await RequestService.cancelRequest(
        req.params.id,
        req.user.id,
        reason
      );

      return sendResponse(res, true, 'Request cancelled successfully', cancelledRequest);
    } catch (error) {
      if (error.message === 'Request not found') {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 404);
      }
      if (error.message.includes('Unauthorized')) {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 403);
      }
      if (error.message.includes('already cancelled')) {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 422);
      }
      if (error.message.includes('Cannot cancel')) {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 422);
      }
      return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 400);
    }
  }

  /**
   * Bulk request actions (approve, reject, delete) - Admin only
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async bulkRequestAction(req, res) {
    
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(res, false, "Validation failed: ok", null, formattedErrors.errors, null, 422);
      }

      const { request_ids, action, note } = req.body;
      const adminId = req.user.id;
      // Call the service method to handle bulk actions
      const result = await RequestService.bulkRequestAction(request_ids, action, note, adminId);

      // Prepare response message based on action
      let message;
      switch (action) {
        case 'approve':
          message = `Successfully approved ${result.successful.length} request(s)`;
          break;
        case 'reject':
          message = `Successfully rejected ${result.successful.length} request(s)`;
          break;
        case 'delete':
          message = `Successfully deleted ${result.successful.length} request(s)`;
          break;
        default:
          message = `Successfully processed ${result.successful.length} request(s)`;
      }

      // If there were any failures, include them in the message
      if (result.failed.length > 0) {
        message += `. ${result.failed.length} request(s) failed to process.`;
      }

      return sendResponse(res, true, message, result, null, null, 200);
    } catch (error) {
      console.error('Bulk request action error:', error);
      return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 400);
    }
  }

  /**
   * Create a new request by merging multiple existing requests (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async createMergedRequest(req, res) {
    try {
      const { request_ids_to_merge, ...newRequestData } = req.body;
      const adminId = req.user.id;

      const mergedRequest = await RequestService.createMergedRequest(
        newRequestData,
        request_ids_to_merge,
        adminId
      );

      return sendResponse(
        res,
        true,
        `Successfully created new request by merging ${request_ids_to_merge.length} request(s)`,
        mergedRequest,
        null,
        null,
        201
      );
    } catch (error) {
      if (error.message.includes('not found')) {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 404);
      }
      return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 500);
    }
  }

  /**
   * Create a merged request with offers for each child request (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async createMergedRequestWithOffers(req, res) {
    try {
      const { message, description, merged_children_offers } = req.body;
      const adminId = req.user.id;

      const result = await RequestService.createMergedRequestWithOffers(
        message,
        description,
        merged_children_offers,
        adminId
      );

      return sendResponse(
        res,
        true,
        `Successfully created merged request with ${result.created_offers.length} offers for ${result.summary.total_child_requests} child requests`,
        result,
        null,
        null,
        201
      );
    } catch (error) {
      if (error.message.includes('not found')) {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 404);
      }
      if (error.message.includes('already merged')) {
        return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 400);
      }
      return sendResponse(res, false, error.message, null, { general: [error.message] }, null, 500);
    }
  }
}

module.exports = RequestController;