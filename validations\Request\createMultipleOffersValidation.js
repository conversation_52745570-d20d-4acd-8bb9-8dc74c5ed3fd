const { body } = require('express-validator');
const { validationResult } = require('express-validator');
const sendResponse = require('../../utils/sendResponse');

/**
 * Validation middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = {};
    errors.array().forEach(error => {
      if (!errorMessages[error.path]) {
        errorMessages[error.path] = [];
      }
      errorMessages[error.path].push(error.msg);
    });

    return sendResponse(
      res,
      false,
      'Validation failed',
      null,
      errorMessages,
      null,
      400
    );
  }
  next();
};

/**
 * Validation for creating multiple offers for different requests
 */
const createMultipleOffersValidation = [
  // Message and description validation
  body('message')
    .notEmpty()
    .withMessage('Message is required')
    .isLength({ min: 3, max: 500 })
    .withMessage('Message must be between 3 and 500 characters'),

  body('description')
    .notEmpty()
    .withMessage('Description is required')
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),

  // Multiple offers validation - support both 'offers' and 'merged_children_offers' for flexibility
  body()
    .custom((value) => {
      if (!value.offers && !value.merged_children_offers) {
        throw new Error('Either "offers" or "merged_children_offers" array is required');
      }
      if (value.offers && value.merged_children_offers) {
        throw new Error('Cannot provide both "offers" and "merged_children_offers" arrays');
      }
      return true;
    }),

  body('offers')
    .optional()
    .isArray({ min: 1 })
    .withMessage('Offers must be a non-empty array'),

  body('merged_children_offers')
    .optional()
    .isArray({ min: 1 })
    .withMessage('Merged children offers must be a non-empty array'),

  // Validation for offers array
  body('offers.*.request_id')
    .if(body('offers').exists())
    .notEmpty()
    .withMessage('Request ID is required for each offer')
    .isUUID()
    .withMessage('Request ID must be a valid UUID'),

  body('offers.*.price')
    .if(body('offers').exists())
    .notEmpty()
    .withMessage('Price is required for each offer')
    .isFloat({ min: 0.01 })
    .withMessage('Price must be a positive number greater than 0'),

  body('offers.*.delivery_time')
    .if(body('offers').exists())
    .notEmpty()
    .withMessage('Delivery time is required for each offer')
    .isInt({ min: 1 })
    .withMessage('Delivery time must be a positive integer (days)'),

  // Validation for merged_children_offers array
  body('merged_children_offers.*.request_id')
    .if(body('merged_children_offers').exists())
    .notEmpty()
    .withMessage('Request ID is required for each offer')
    .isUUID()
    .withMessage('Request ID must be a valid UUID'),

  body('merged_children_offers.*.price')
    .if(body('merged_children_offers').exists())
    .notEmpty()
    .withMessage('Price is required for each offer')
    .isFloat({ min: 0.01 })
    .withMessage('Price must be a positive number greater than 0'),

  body('merged_children_offers.*.delivery_time')
    .if(body('merged_children_offers').exists())
    .notEmpty()
    .withMessage('Delivery time is required for each offer')
    .isInt({ min: 1 })
    .withMessage('Delivery time must be a positive integer (days)'),

  // Optional offer title (for offers array)
  body('offers.*.offer_title')
    .optional()
    .isLength({ min: 3, max: 200 })
    .withMessage('Offer title must be between 3 and 200 characters'),

  // Optional offer description (for offers array)
  body('offers.*.offer_description')
    .optional()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Offer description must be between 10 and 1000 characters'),

  // Custom validation to ensure no duplicate request IDs in offers
  body('offers')
    .if(body('offers').exists())
    .custom((value) => {
      const requestIds = value.map(offer => offer.request_id);
      const uniqueIds = [...new Set(requestIds)];
      if (uniqueIds.length !== requestIds.length) {
        throw new Error('Duplicate request IDs are not allowed in offers');
      }
      return true;
    }),

  // Custom validation to ensure no duplicate request IDs in merged_children_offers
  body('merged_children_offers')
    .if(body('merged_children_offers').exists())
    .custom((value) => {
      const requestIds = value.map(offer => offer.request_id);
      const uniqueIds = [...new Set(requestIds)];
      if (uniqueIds.length !== requestIds.length) {
        throw new Error('Duplicate request IDs are not allowed in merged_children_offers');
      }
      return true;
    }),

  // Custom validation to ensure all prices are reasonable (offers)
  body('offers.*.price')
    .if(body('offers').exists())
    .custom((value) => {
      if (value > 1000000) {
        throw new Error('Price cannot exceed 1,000,000');
      }
      return true;
    }),

  // Custom validation to ensure all prices are reasonable (merged_children_offers)
  body('merged_children_offers.*.price')
    .if(body('merged_children_offers').exists())
    .custom((value) => {
      if (value > 1000000) {
        throw new Error('Price cannot exceed 1,000,000');
      }
      return true;
    }),

  // Custom validation to ensure delivery time is reasonable (offers)
  body('offers.*.delivery_time')
    .if(body('offers').exists())
    .custom((value) => {
      if (value > 365) {
        throw new Error('Delivery time cannot exceed 365 days');
      }
      return true;
    }),

  // Custom validation to ensure delivery time is reasonable (merged_children_offers)
  body('merged_children_offers.*.delivery_time')
    .if(body('merged_children_offers').exists())
    .custom((value) => {
      if (value > 365) {
        throw new Error('Delivery time cannot exceed 365 days');
      }
      return true;
    }),

  handleValidationErrors
];

module.exports = {
  createMultipleOffersValidation
};
