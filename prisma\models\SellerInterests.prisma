// Seller interested categories table
model seller_interested_categories {
  id          String   @id @default(uuid())
  seller_id   String
  category_id String
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  seller   users      @relation(fields: [seller_id], references: [id], onDelete: Cascade, name: "SellerCategoryInterests")
  category categories @relation(fields: [category_id], references: [id], onDelete: Cascade, name: "CategorySellerInterests")

  // Unique constraint to prevent duplicate interests
  @@unique([seller_id, category_id], name: "seller_id_category_id")
  @@index([seller_id])
  @@index([category_id])
}

// Seller interested subcategories table
model seller_interested_subcategories {
  id             String   @id @default(uuid())
  seller_id      String
  subcategory_id String
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt

  // Relations
  seller      users          @relation(fields: [seller_id], references: [id], onDelete: Cascade, name: "SellerSubcategoryInterests")
  subcategory sub_categories @relation(fields: [subcategory_id], references: [id], onDelete: Cascade, name: "SubcategorySellerInterests")

  // Unique constraint to prevent duplicate interests
  @@unique([seller_id, subcategory_id], name: "seller_id_subcategory_id")
  @@index([seller_id])
  @@index([subcategory_id])
}
