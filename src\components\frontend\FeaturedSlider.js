"use client";
import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import FeaturedCard from "./FeaturedCard";
import BlogCard from "./BlogCard";
import "aos/dist/aos.css"; // You can also use <link> for styles
import Aos from "aos";
import { useEffect } from "react";
const FeaturedSlider = () => {
  const featuredData = [
    {
      id: 1,
      image: "/assets/frontend_assets/featured-1.png",
      title: "Free energy audit + 20% off on energy-saving improvements",
      desc: "Secured sensitive data during a financial company is cloud migration by designing",
    },
    {
      id: 2,
      image: "/assets/frontend_assets/featured-2.png",
      title: "Free energy audit + 20% off on energy-saving improvements",
      desc: "Secured sensitive data during a financial company is cloud migration by designing",
    },
    {
      id: 3,
      image: "/assets/frontend_assets/featured-3.png",
      title: "Free energy audit + 20% off on energy-saving improvements",
      desc: "Secured sensitive data during a financial company is cloud migration by designing",
    },
    {
      id: 4,
      image: "/assets/frontend_assets/featured-1.png",
      title: "Free energy audit + 20% off on energy-saving improvements",
      desc: "Secured sensitive data during a financial company is cloud migration by designing",
    },
  ];
  useEffect(() => {
    Aos.init();
  }, []);
  return (
    <>
      <div className="max-w-7xl mx-auto px-4 relative">
        <div className="text-center">
          <h3 className="archivo text-[32px] font-bold text-[#0F0C1D]">
            Featured
            <span className="ml-2 text-transparent bg-clip-text bg-linear-to-r from-[#FD2692]   to-[#0A67F2]">
              Offers{" "}
            </span>
          </h3>
          <p className="text-[#656B76] archivo font-normal text-base max-w-xl mx-auto">
            Each listing is designed to be clear and concise, providing
            customers{" "}
          </p>
        </div>
        <div className="mt-10">
          <Swiper
            slidesPerView={1}
            spaceBetween={10}
            modules={[Pagination]}
            pagination={{
              clickable: true,
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 3,
                spaceBetween: 40,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 20,
              },
            }}
            className="mySwiper featured__slider"
          >
            {featuredData.map((item, index) => (
              <SwiperSlide key={item.id}>
                <FeaturedCard item={item} index={index} />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </>
  );
};

export default FeaturedSlider;
