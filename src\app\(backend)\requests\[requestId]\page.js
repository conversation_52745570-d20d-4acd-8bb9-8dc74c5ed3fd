"use client";

import React, { useState } from "react";
import { useFetchApiQuery, useMutateApiMutation } from "@/redux/services/api";
import { useRouter } from "next/navigation";
import GoBack from "@/components/backend/GoBack";
import Breadcrumb from "@/components/backend/Breadcrumb";
import { toast } from "react-hot-toast";
import { asseturl } from "@/config";
import Image from 'next/image';
import CustomFields from './CustomFields';

const RequestDetailsPage = ({ params }) => {
  // Unwrap the params promise
  const unwrappedParams = React.use(params);
  const requestId = unwrappedParams?.requestId;

  const router = useRouter();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelReason, setCancelReason] = useState("");

  return (
    <RequestDetailsContent
      requestId={requestId}
      router={router}
      selectedImageIndex={selectedImageIndex}
      setSelectedImageIndex={setSelectedImageIndex}
      showCancelModal={showCancelModal}
      setShowCancelModal={setShowCancelModal}
      cancelReason={cancelReason}
      setCancelReason={setCancelReason}
    />
  );
};

const RequestDetailsContent = ({
  requestId,
  router,
  selectedImageIndex,
  setSelectedImageIndex,
  showCancelModal,
  setShowCancelModal,
  cancelReason,
  setCancelReason
}) => {


  // Mutation hook for canceling requests
  const [mutateApi, { isLoading: isCanceling }] = useMutateApiMutation();

  // Fetch request details
  const { data: responseData, isLoading, error, refetch } = useFetchApiQuery({
    endpoint: requestId ? `/buyer/requests/${requestId}` : null,
    skip: !requestId,
  });

  const { data: testData, iststLoading, errortest } = useFetchApiQuery({
    endpoint: requestId ? `/buyer/requests/${requestId}/status-history` : null,
    skip: !requestId,
  });

  console.log(testData);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <GoBack />
        <Breadcrumb />
        <div className="mt-5 bg-white p-6 rounded-lg shadow-sm">
          <div className="text-center py-10">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-16 w-16 mx-auto text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Request not found</h3>
            <p className="mt-1 text-sm text-gray-500">The request you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.</p>
            <div className="mt-6">
              <button
                onClick={() => router.push("/requests/pending")}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Go back to requests
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const request = responseData?.data || {};
  const attachments = request.request_attachments || [];
  const statusHistory = request.request_statuses || [];

  const isImageFile = (filePath) => {
    if (!filePath) return false;
    const extension = filePath.split('.').pop().toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension);
  };

  const getFileTypeColor = (filePath) => {
    if (!filePath) return "#95a5a6";

    const extension = filePath.split('.').pop().toLowerCase();

    switch (extension) {
      case 'pdf': return "#e74c3c";
      case 'doc': case 'docx': return "#2980b9";
      case 'xls': case 'xlsx': return "#27ae60";
      case 'ppt': case 'pptx': return "#e67e22";
      case 'jpg': case 'jpeg': case 'png': case 'gif': case 'bmp': case 'webp': case 'svg': return "#3498db";
      case 'mp4': case 'avi': case 'mov': case 'wmv': return "#9b59b6";
      case 'mp3': case 'wav': case 'ogg': return "#f39c12";
      case 'csv': return "#16a085";
      case 'zip': case 'rar': case '7z': return "#7f8c8d";
      case 'html': case 'css': case 'js': case 'json': case 'xml': return "#2c3e50";
      default: return "#95a5a6";
    }
  };

  const imageAttachments = attachments.filter(att => isImageFile(att.file_path));

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getStatusColor = (status) => {
    if (!status) return 'gray';

    switch (status.toLowerCase()) {
      case 'active': case 'approved': case 'completed': return 'green';
      case 'inactive': case 'in progress': case 'processing': return 'yellow';
      case 'suspended': case 'rejected': case 'cancelled': return 'red';
      case 'pending': return 'blue';
      default: return 'gray';
    }
  };

  const previewImage = imageAttachments.length > 0 && imageAttachments[selectedImageIndex]?.file_path
    ? imageAttachments[selectedImageIndex].file_path
    : "/placeholder.jpg";

  const handleThumbnailClick = (index) => {
    setSelectedImageIndex(index);
  };

  const handleDocumentClick = (filePath) => {
    if (!filePath) return;
    window.open(asseturl + filePath, '_blank');
  };

  const handleCloseCancelModal = () => {
    setShowCancelModal(false);
    setCancelReason("");
  };


  const handleCancelRequest = () => {
    if (!cancelReason.trim()) {
      toast.error('Please provide a reason for cancellation');
      return;
    }

    const loadingToast = toast.loading('Cancelling request...');

    console.log('Attempting to cancel request:', requestId, 'with reason:', cancelReason);

    mutateApi({
      endpoint: `/buyer/requests/${requestId}/cancel`,
      data: { reason: cancelReason }
    }).then(() => {
      toast.dismiss(loadingToast);
      toast.success('Request cancelled successfully');
      handleCloseCancelModal();

      // Refresh the data
      refetch();
    }).catch((error) => {
      console.error('Error cancelling request:', error);
      toast.dismiss(loadingToast);

      // Handle authentication errors
      if (error?.status === 401 || error?.status === 403) {
        import('@/utils/auth').then(({ handleUnauthorized }) => {
          handleUnauthorized(error, router);
        });
      } else {
        toast.error(
          error?.data?.message ||
          "Failed to cancel request. Please try again."
        );
      }
    });
  };

  return (
    <div className="p-6">
      <GoBack />
      <Breadcrumb />

      <div className="bg-white rounded-xl shadow-md p-6 mt-5 grid md:grid-cols-2 gap-6">
        <div>
          {imageAttachments.length > 0 ? (
            <div className="relative w-full h-100">
              <Image
                src={asseturl + previewImage}
                alt="Preview"
                width={800}
                height={600}
                className="rounded-lg border object-cover h-full w-full"
                unoptimized={process.env.NODE_ENV === 'development'}
              />
            </div>
          ) : (
            <div className="w-full h-80 flex items-center justify-center bg-gray-100 rounded-lg border">
              <p className="text-gray-500">No image attachments available</p>
            </div>
          )}

          {imageAttachments.length > 0 && (
            <div className="mt-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Images</h3>
              <div className="flex gap-2 overflow-x-auto pb-2">
                {imageAttachments.map((att, index) => (
                  <div
                    key={index}
                    className="relative w-20 h-20"
                    onClick={() => handleThumbnailClick(index)}
                  >
                    <Image
                      src={asseturl + att.file_path}
                      alt={`Thumbnail ${index + 1}`}
                      width={80}
                      height={80}
                      className={`rounded-md border cursor-pointer object-cover h-full w-full ${
                        selectedImageIndex === index
                          ? 'border-2 border-blue-500'
                          : 'hover:border-gray-400'
                      }`}
                      unoptimized={process.env.NODE_ENV === 'development'}
                    />
                  </div>
                ))}

                {request.file && (() => {
                  const filePath = request.file;
                  const fileName = filePath.split('/').pop();

                  return (
                    <div
                      className="flex-shrink-0 flex flex-col items-center justify-center p-2 border border-indigo-200 rounded-md cursor-pointer hover:bg-gray-50 transition w-24"
                      onClick={() => handleDocumentClick(filePath)}
                      title={`Main File: ${fileName}`}
                    >
                      <div
                        className="w-10 h-10 flex items-center justify-center rounded-full"
                        style={{ backgroundColor: `${getFileTypeColor(filePath)}20` }}
                      >
                        <span className="text-sm font-bold" style={{ color: getFileTypeColor(filePath) }}>
                          {filePath.split('.').pop().toUpperCase()}
                        </span>
                      </div>
                    </div>
                  );
                })()}
              </div>
            </div>
          )}
        </div>

        <div className="space-y-3">
          <div className="flex justify-between items-start">
            <h2 className="text-2xl font-bold text-gray-800">{request.title || "Request Title"}</h2>
            <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-${getStatusColor(request.status)}-100 text-${getStatusColor(request.status)}-800`}>
              {request.status}
            </span>
          </div>

          <div className="text-sm text-gray-500 space-x-4">
            <span>{request?.category?.title || "Category"}</span> -
            <span>{request?.sub_category?.title || "Subcategory"}</span>
          </div>

          <div className="text-xl font-semibold text-gray-700">
          ${request.budget_min || 0} - ${request.budget_max || 0}
          </div>

          <div className="text-sm text-gray-600">{request.quantity || 0} Unit</div>

          <div className="text-sm text-gray-600">
            {request.deadline ? new Date(request.deadline).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }) : "No deadline specified"}
          </div>

          <div>
            <p className="text-gray-800 font-semibold">Short Description:</p>
            <p className="text-gray-600">{request.short_description || "N/A"}</p>
          </div>

          <div>
            <p className="text-gray-800 font-semibold">Details Descriptions:</p>
            <p className="text-gray-600 whitespace-pre-line">{request.description || "N/A"}</p>
          </div>

          <div>
            <p className="text-gray-800 font-semibold">Additional Criteria:</p>
            <p className="text-gray-600">{request.additional_info || "N/A"}</p>
          </div>

          <div className="flex flex-wrap gap-3 mt-6">
            {request.status === 'Pending' && (
              <>
                <button
                  onClick={() => router.push(`/requests/edit/${requestId}`)}
                  className="btn btn-primary px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md cursor-pointer"
                >
                  Edit Request
                </button>
                <button
                  onClick={() => setShowCancelModal(true)}
                  className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md cursor-pointer"
                >
                  Cancel Request
                </button>
              </>
            )}
            <button
              onClick={() => toast.info("Contact functionality will be implemented soon")}
              className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md cursor-pointer"
            >
              Contact Support
            </button>
          </div>
        </div>
      </div>

      {/* Custom Fields Section */}
      {request.custom_fields && Object.keys(request.custom_fields).length > 0 && (
        <CustomFields custom_fields={request.custom_fields} />
      )}

      {statusHistory && statusHistory.length > 0 && (
        <div className="mt-6 bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Status History</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Previous Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Updated By
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reason
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {statusHistory.map((history, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDateTime(history.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${getStatusColor(history.status)}-100 text-${getStatusColor(history.status)}-800`}>
                        {history.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {history.previous_status ? (
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${getStatusColor(history.previous_status)}-100 text-${getStatusColor(history.previous_status)}-800`}>
                          {history.previous_status}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {history.updated_by_user ? (
                        <div className="flex items-center">
                          <div className="ml-1">
                            <div className="text-sm font-medium text-gray-900">
                              {history.updated_by_user.first_name} {history.updated_by_user.last_name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {history.updated_by_user.email}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {history.reason || <span className="text-gray-400">-</span>}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {showCancelModal && (
        <div className="fixed inset-0 backdrop-blur-sm bg-black/70 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Cancel Request</h3>
            <p className="text-gray-600 mb-4">Please provide a reason for cancelling this request:</p>

            <textarea
              value={cancelReason}
              onChange={(e) => setCancelReason(e.target.value)}
              className="w-full border border-gray-300 rounded-lg p-3 mb-4 min-h-[100px]"
              placeholder="Enter reason for cancellation..."
            ></textarea>

            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCloseCancelModal}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 cursor-pointer"
              >
                Close
              </button>
              <button
                onClick={handleCancelRequest}
                disabled={!cancelReason.trim()}
                className={`px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 ${
                  !cancelReason.trim() ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Cancel Request
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RequestDetailsPage;
