import Link from "next/link";
import React from "react";

const Footer = () => {
  return (
    <>
      <footer
        className="bg-black"
        style={{
          backgroundImage: "url(/assets/frontend_assets/footer-bg.png)",
        }}
      >
        <div className="max-w-7xl mx-auto px-4 py-10">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="col-span-2 lg:col-span-1">
              <div className="mb-4">
                <Link href="" className="mb-3 inline-block">
                  <svg
                    className="w-[225px] h-[29px]"
                    // width={225}
                    // height={29}
                    viewBox="0 0 225 29"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M164.584 9.08549C166.283 10.3919 167.488 12.0531 168.308 14.0285C168.365 14.1623 168.421 14.2962 168.48 14.4341C169.222 16.5909 169.204 19.6949 168.308 21.7972C168.229 21.9918 168.149 22.1864 168.066 22.3869C166.868 25.0336 164.902 27.0345 162.182 28.1313C159.403 29.1763 156.147 28.9071 153.473 27.712C150.961 26.3788 149.214 24.1446 148.352 21.4441C147.615 19.0368 147.561 15.7686 148.705 13.4988C148.814 13.2575 148.814 13.2575 148.926 13.0112C150.194 10.4276 152.401 8.58892 155.074 7.58403C158.297 6.51173 161.819 7.18216 164.584 9.08549Z"
                      fill="#0C68F2"
                    />
                    <path
                      d="M181.555 7.14097C181.733 7.15144 181.911 7.16191 182.094 7.1727C184.777 7.47025 187.117 9.07386 188.805 11.1088C190.333 13.1722 190.982 15.281 190.96 17.8229C190.962 18.0575 190.962 18.0575 190.964 18.2968C190.955 21.2106 189.788 23.7703 187.803 25.8785C185.569 28.0191 182.901 28.8009 179.867 28.7521C177.308 28.6522 174.966 27.7032 173.1 25.9165C170.619 23.1847 169.918 20.1318 170.076 16.4987C170.264 14.3404 171.102 12.4527 172.548 10.8488C172.68 10.7013 172.811 10.5538 172.946 10.4018C175.303 7.89372 178.145 6.91045 181.555 7.14097Z"
                      fill="#0C68F2"
                    />
                    <path
                      d="M54.5723 9.4391C58.2604 9.42271 58.2604 9.42271 62.023 9.40599C63.181 9.39876 63.181 9.39876 64.3625 9.39138C65.3162 9.38899 65.3162 9.38899 65.7648 9.38854C66.0735 9.38774 66.3822 9.38589 66.6908 9.38318C69.5198 9.35957 71.8684 9.63591 74.3194 11.1606C75.4431 12.316 75.8426 13.6514 75.8254 15.2291C75.7011 16.4018 75.1672 17.3563 74.3083 18.1568C73.4341 18.8319 72.423 19.2585 71.3501 19.5031C71.5036 19.5525 71.657 19.6019 71.8151 19.6528C74.1004 20.4475 74.1004 20.4475 74.7057 21.6218C75.1213 22.7737 75.1246 23.8866 75.1434 25.101C75.1719 26.0174 75.2756 26.6457 75.7654 27.4483C75.8236 27.5649 75.8819 27.6814 75.942 27.8015C75.0998 27.8277 74.2578 27.8469 73.4153 27.8597C73.1288 27.865 72.8424 27.8722 72.556 27.8814C72.1439 27.8943 71.7321 27.9003 71.3198 27.9049C71.1921 27.9104 71.0644 27.9159 70.9328 27.9215C70.5687 27.9217 70.5687 27.9217 69.9373 27.8015C69.2799 27.0299 69.0351 26.3663 69.0294 25.3682C69.026 25.205 69.0225 25.0419 69.019 24.8737C69.0131 24.5303 69.0098 24.1868 69.0094 23.8433C69.005 23.6801 69.0007 23.5169 68.9963 23.3488C68.9946 23.1996 68.993 23.0504 68.9913 22.8967C68.9062 22.4778 68.9062 22.4778 68.5427 22.1927C67.7517 21.8785 67.0141 21.8902 66.1698 21.8846C65.9947 21.8814 65.8195 21.8783 65.6391 21.875C65.0813 21.8655 64.5235 21.8595 63.9657 21.8536C63.5868 21.8477 63.2079 21.8416 62.8291 21.8353C61.9018 21.8199 60.9746 21.8093 60.0471 21.7984C60.0471 23.7794 60.0471 25.7604 60.0471 27.8015C58.2404 27.8015 56.4337 27.8015 54.5723 27.8015C54.5723 21.7419 54.5723 15.6823 54.5723 9.4391Z"
                      fill="#0C68F2"
                    />
                    <path
                      d="M125.215 9.43652C131.743 9.43652 138.27 9.43652 144.996 9.43652C144.996 10.8932 144.996 12.3498 144.996 13.8506C143.642 15.1664 143.642 15.1664 143.001 15.4955C142.109 15.9869 141.34 16.5904 140.547 17.2273C140.279 17.4369 140.011 17.6461 139.743 17.8549C139.615 17.9545 139.488 18.0542 139.356 18.1568C138.654 18.7035 137.948 19.2451 137.242 19.7867C136.857 20.0827 136.472 20.3792 136.087 20.6764C135.935 20.794 135.935 20.794 135.78 20.9139C135.5 21.1298 135.221 21.3459 134.941 21.562C134.357 22.0043 133.759 22.4292 133.163 22.8552C139.107 22.9426 139.107 22.9426 145.172 23.0317C145.172 24.6049 145.172 26.1781 145.172 27.7989C138.412 27.7989 131.651 27.7989 124.686 27.7989C124.686 23.3849 124.686 23.3849 125.646 22.3696C125.979 22.1467 125.979 22.1467 126.28 22.0027C127.086 21.5804 127.77 21.0339 128.483 20.4716C128.739 20.2751 128.996 20.079 129.253 19.8833C129.894 19.3926 130.536 18.9007 131.176 18.408C131.359 18.2676 131.541 18.1272 131.729 17.9825C132.108 17.691 132.486 17.399 132.863 17.1066C132.962 17.0307 133.06 16.9549 133.161 16.8767C133.429 16.6696 133.697 16.4622 133.964 16.2548C134.807 15.618 135.664 15.0013 136.518 14.3802C130.923 14.2928 130.923 14.2928 125.215 14.2037C125.215 12.6305 125.215 11.0574 125.215 9.43652Z"
                      fill="#0C68F2"
                    />
                    <path
                      d="M15.4745 11.1269C16.4013 12.096 16.9313 13.0423 17.3863 14.4452C17.5175 14.2201 17.5175 14.2201 17.6514 13.9905C18.8498 12.0058 20.1677 10.1809 22.1953 9.76101C24.3268 9.5052 26.2808 9.70496 28.0519 11.405C28.9805 12.4986 29.5749 13.6226 29.9636 15.1478C30.0584 15.491 30.0584 15.491 30.155 15.8412C30.4141 17.0015 30.3795 18.1583 30.3696 19.3553C30.3691 19.5973 30.3686 19.8393 30.3681 20.0886C30.366 20.857 30.3614 21.6253 30.3566 22.3936C30.3548 22.9164 30.3531 23.4393 30.3516 23.9621C30.3478 25.2398 30.3406 26.5173 30.3335 27.795C28.5024 27.795 26.6713 27.795 24.7847 27.795C24.7743 26.8385 24.7743 26.8385 24.7638 25.8628C24.7552 25.2403 24.7457 24.6179 24.7362 23.9954C24.73 23.5652 24.7249 23.135 24.7206 22.7048C24.7144 22.0837 24.7047 21.4629 24.6944 20.842C24.6926 20.5568 24.6926 20.5568 24.6908 20.2658C24.6648 18.9651 24.4708 17.9758 24.0449 16.7872C23.1698 15.8036 22.1206 15.9747 21.014 16.0178C19.9512 16.124 19.3627 16.3716 18.6117 17.3728C18.0572 18.4529 18.0873 19.4807 18.0676 20.7276C18.0618 21.0302 18.0618 21.0302 18.056 21.339C18.0439 21.9832 18.033 22.6275 18.0221 23.2719C18.0141 23.7087 18.006 24.1456 17.9979 24.5824C17.9779 25.6533 17.9596 26.7241 17.9412 27.795C16.1101 27.795 14.279 27.795 12.3924 27.795C12.3854 27.135 12.3785 26.475 12.3714 25.7951C12.3628 25.1532 12.3534 24.5114 12.3438 23.8696C12.3377 23.4249 12.3325 22.9801 12.3283 22.5353C12.322 21.8945 12.3124 21.2539 12.302 20.6132C12.3009 20.4156 12.2997 20.2179 12.2985 20.0142C12.2754 18.8349 12.1284 18.0648 11.6525 17.0214C11.6525 16.8669 11.6525 16.7123 11.6525 16.553C10.4198 15.8025 8.8386 15.8505 7.51041 16.2017C6.77218 16.5602 6.34289 17.132 5.91874 17.9583C5.67659 18.8782 5.69038 19.7677 5.67525 20.7276C5.67141 20.9293 5.66757 21.1311 5.66361 21.339C5.65152 21.9832 5.64061 22.6275 5.62974 23.2719C5.62175 23.7087 5.61368 24.1456 5.60553 24.5824C5.58559 25.6533 5.56724 26.7241 5.54882 27.795C3.71771 27.795 1.8866 27.795 0 27.795C0 21.9211 0 16.0471 0 9.99522C0.915555 9.99522 1.83111 9.99522 2.77441 9.99522C3.32374 11.6956 3.87307 13.3959 4.43905 15.1478C4.74424 14.3749 5.04942 13.602 5.36386 12.8057C6.2937 11.2139 7.61022 10.117 9.12448 9.61921C11.4199 9.1894 13.5228 9.42734 15.4745 11.1269Z"
                      fill="#0B68F2"
                    />
                    <path
                      d="M210.146 11.2557C210.93 12.0485 211.398 12.8352 211.826 13.9835C211.826 14.2152 211.826 14.447 211.826 14.6858C211.948 14.6858 212.07 14.6858 212.196 14.6858C212.248 14.454 212.248 14.454 212.3 14.2176C212.968 12.4534 214.158 11.1199 215.526 10.238C217.351 9.43858 219.552 9.39188 221.347 10.3669C223.167 11.6861 224.189 13.4638 224.783 15.9705C225.015 17.1687 225.006 18.3374 224.996 19.5651C224.995 19.8015 224.995 20.0379 224.994 20.2814C224.992 21.0303 224.987 21.779 224.983 22.5279C224.981 23.0381 224.979 23.5484 224.978 24.0586C224.974 25.3041 224.967 26.5495 224.96 27.795C223.128 27.795 221.297 27.795 219.41 27.795C219.403 27.1353 219.396 26.4757 219.389 25.7961C219.381 25.1545 219.371 24.513 219.362 23.8715C219.355 23.427 219.35 22.9824 219.346 22.5378C219.34 21.8974 219.33 21.2571 219.32 20.6167C219.318 20.3204 219.318 20.3204 219.316 20.018C219.293 18.8393 219.146 18.0695 218.67 17.0267C218.67 16.8722 218.67 16.7177 218.67 16.5585C217.437 15.8083 215.856 15.8563 214.528 16.2074C213.66 16.6286 213.158 17.402 212.751 18.4312C212.722 19.1306 212.703 19.8254 212.692 20.5253C212.687 20.8361 212.687 20.8361 212.681 21.1531C212.669 21.8162 212.658 22.4793 212.647 23.1424C212.639 23.5914 212.631 24.0404 212.623 24.4893C212.603 25.5912 212.584 26.6931 212.566 27.795C210.735 27.795 208.903 27.795 207.017 27.795C207.01 27.155 207.003 26.5149 206.996 25.8555C206.987 25.2318 206.978 24.6081 206.968 23.9844C206.962 23.5528 206.957 23.1212 206.953 22.6896C206.946 22.0671 206.937 21.4449 206.926 20.8225C206.925 20.6312 206.924 20.44 206.923 20.243C206.893 18.7505 206.691 17.5938 205.865 16.4625C204.81 15.6973 203.303 15.8954 202.134 16.2092C201.265 16.6261 200.764 17.4033 200.357 18.4312C200.328 19.1306 200.31 19.8254 200.299 20.5253C200.295 20.7325 200.291 20.9396 200.287 21.1531C200.275 21.8162 200.264 22.4793 200.253 23.1424C200.245 23.5914 200.237 24.0404 200.229 24.4893C200.209 25.5912 200.191 26.6931 200.172 27.795C198.341 27.795 196.51 27.795 194.623 27.795C194.623 21.9239 194.623 16.0529 194.623 10.0039C195.478 10.0039 196.332 10.0039 197.213 10.0039C198.878 14.1005 198.878 14.1005 198.878 15.1539C199 15.1539 199.122 15.1539 199.248 15.1539C199.267 14.9846 199.286 14.8154 199.305 14.6409C199.607 13.081 200.505 11.7552 201.525 10.8378C204.205 8.82201 207.595 8.98692 210.146 11.2557Z"
                      fill="#0B68F2"
                    />
                    <path
                      d="M78.5898 9.43706C80.3966 9.43706 82.2033 9.43706 84.0647 9.43706C84.0647 12.1173 84.0647 14.7975 84.0647 17.5589C84.1813 17.5589 84.2979 17.5589 84.4179 17.5589C84.4762 17.3841 84.5345 17.2093 84.5946 17.0292C84.8402 16.7816 84.8402 16.7816 85.1685 16.5106C85.8315 15.9483 86.4742 15.3692 87.1112 14.778C87.3101 14.5939 87.5091 14.4097 87.708 14.2256C87.807 14.1338 87.9061 14.0421 88.0082 13.9476C90.9359 11.2395 90.9359 11.2395 92.1887 10.1433C92.3058 10.021 92.4229 9.89873 92.5435 9.77273C93.3096 9.28598 93.9688 9.3485 94.8579 9.3681C95.0291 9.36909 95.2004 9.37009 95.3768 9.37112C95.9221 9.37507 96.4672 9.38393 97.0124 9.39292C97.3827 9.39647 97.7531 9.39969 98.1234 9.40258C99.0299 9.41035 99.9362 9.42254 100.843 9.43706C99.886 10.5961 99.886 10.5961 99.3193 11.0703C98.7277 11.5747 98.1493 12.0907 97.5753 12.6152C96.9121 13.2199 96.2432 13.8163 95.565 14.4042C94.9204 14.9726 94.2901 15.557 93.6582 16.1395C93.2484 16.4995 93.2484 16.4995 92.8952 16.6761C93.1232 17.1645 93.3732 17.5676 93.7168 17.9824C93.8093 18.0948 93.9017 18.2073 93.997 18.3232C94.1455 18.502 94.1455 18.502 94.297 18.6845C94.5062 18.9388 94.7153 19.1933 94.9241 19.448C95.0288 19.5755 95.1334 19.703 95.2412 19.8344C95.6812 20.3757 96.1097 20.9256 96.5377 21.4763C97.1606 22.2755 97.7996 23.057 98.4584 23.8268C99.161 24.6479 99.8432 25.4825 100.511 26.3318C100.595 26.4378 100.679 26.5439 100.765 26.6532C101.35 27.401 101.35 27.401 101.549 27.7994C100.582 27.8655 99.6161 27.9132 98.6472 27.9449C98.3188 27.9581 97.9906 27.9761 97.6628 27.9992C95.0264 28.1801 95.0264 28.1801 94.1355 27.5541C93.4989 26.9361 93.0196 26.2258 92.561 25.4701C92.3195 25.0761 92.0438 24.717 91.7569 24.3551C91.5987 24.1425 91.4409 23.9297 91.2836 23.7165C91.1094 23.4823 90.9351 23.2483 90.7607 23.0144C90.6334 22.8436 90.6334 22.8436 90.5035 22.6693C89.8385 21.7826 89.1585 20.9072 88.48 20.0307C87.8721 20.4214 87.3198 20.795 86.8242 21.3218C85.9783 22.1919 85.1562 23.0164 84.0647 23.562C84.0647 24.9603 84.0647 26.3587 84.0647 27.7994C82.258 27.7994 80.4513 27.7994 78.5898 27.7994C78.5898 21.7399 78.5898 15.6803 78.5898 9.43706Z"
                      fill="#0C68F2"
                    />
                    <path
                      d="M49.1323 11.8473C50.38 13.0744 51.2231 14.7383 51.5487 16.6803C51.5644 17.0528 51.5706 17.4259 51.5698 17.7989C51.5698 18.1305 51.5698 18.1305 51.5698 18.4689C51.5688 18.7058 51.5679 18.9426 51.5669 19.1867C51.5666 19.4306 51.5663 19.6745 51.5661 19.9258C51.565 20.7043 51.5627 21.4828 51.5603 22.2613C51.5594 22.7893 51.5585 23.3173 51.5578 23.8453C51.5557 25.1393 51.5525 26.4332 51.5487 27.7272C50.6286 27.7272 49.7085 27.7272 48.7605 27.7272C48.2698 26.3601 47.7791 24.9931 47.2735 23.5846C47.1508 23.5846 47.0281 23.5846 46.9017 23.5846C46.7419 23.8501 46.5927 24.1255 46.4486 24.4045C45.2281 26.4769 43.1863 27.5485 41.2092 28.0913C38.6168 28.4888 36.0016 28.55 33.7159 26.8641C32.9419 25.8186 32.5475 24.7817 32.4961 23.3544C32.5502 21.9898 32.859 20.9719 33.6281 19.9823C35.9048 17.6402 38.9866 17.3297 41.8133 17.1118C42.0495 17.092 42.2858 17.0721 42.5292 17.0516C43.6792 16.9582 44.8199 16.8853 45.9723 16.9105C45.6683 16.0401 45.4605 15.786 44.754 15.3813C43.9064 15.0147 43.1227 14.9663 42.2315 14.983C42.088 14.9803 41.9445 14.9777 41.7966 14.9749C40.8837 14.9807 40.2572 15.141 39.4666 15.7597C39.3916 15.8802 39.3166 16.0006 39.2393 16.1247C38.7194 16.6368 38.2468 16.5275 37.594 16.5176C37.4669 16.5166 37.3398 16.5156 37.2088 16.5146C36.8034 16.5108 36.3983 16.5021 35.9929 16.4933C35.7179 16.4899 35.4428 16.4867 35.1677 16.4839C34.494 16.4763 33.8204 16.4644 33.1467 16.4502C33.2835 14.5998 33.6941 13.4567 34.7804 12.1323C38.2636 8.60935 45.5451 8.55531 49.1323 11.8473Z"
                      fill="#0D69F2"
                    />
                    <path
                      d="M101.375 9.43652C108.602 9.43652 115.829 9.43652 123.275 9.43652C123.275 11.0097 123.275 12.5828 123.275 14.2037C120.594 14.2037 117.913 14.2037 115.151 14.2037C115.151 18.6901 115.151 23.1765 115.151 27.7989C113.286 27.7989 111.421 27.7989 109.499 27.7989C109.499 23.3125 109.499 18.8261 109.499 14.2037C106.818 14.2037 104.137 14.2037 101.375 14.2037C101.375 12.6305 101.375 11.0574 101.375 9.43652Z"
                      fill="#0C68F2"
                    />
                    <path
                      d="M161.774 12.7909C163.079 14.0058 163.719 15.5832 163.8 17.3493C163.829 19.3997 163.396 21.3301 161.95 22.8548C160.89 23.8868 159.788 24.127 158.352 24.1791C156.857 24.1478 155.885 23.7386 154.743 22.7997C153.507 21.2819 153.05 19.8709 153.065 17.9222C153.066 17.7822 153.067 17.6422 153.068 17.498C153.104 15.7767 153.597 14.4766 154.709 13.144C156.724 11.2283 159.542 11.2071 161.774 12.7909Z"
                      fill="#020E16"
                    />
                    <path
                      d="M182.792 12.0831C184.264 12.8812 185.13 14.2154 185.688 15.7604C185.868 16.4103 185.873 17.0405 185.86 17.711C185.859 17.8503 185.857 17.9897 185.855 18.1333C185.809 19.9263 185.338 21.4689 184.099 22.8043C182.79 24.018 181.48 24.2015 179.736 24.1616C178.355 24.0085 177.277 23.338 176.34 22.3284C175.043 20.5384 174.948 18.0924 175.279 15.9757C175.701 14.3954 176.591 13.0645 178.01 12.2382C179.484 11.4821 181.247 11.4953 182.792 12.0831Z"
                      fill="#020E16"
                    />
                    <path
                      d="M188.796 0.785258C188.971 1.57184 188.971 1.57184 189.149 2.37431C188.032 2.80773 186.903 3.17366 185.757 3.52403C185.568 3.58225 185.379 3.64047 185.184 3.70045C184.574 3.88787 183.964 4.07462 183.354 4.26131C182.937 4.38924 182.521 4.51717 182.104 4.64512C179.335 5.49385 176.565 6.33364 173.784 7.14146C173.325 6.6825 173.242 6.27148 173.056 5.65173C172.962 5.34618 172.962 5.34618 172.867 5.03445C172.725 4.49305 172.725 4.49305 172.725 3.96336C175.394 2.9708 178.033 2.3683 180.839 1.90519C181.374 1.81584 181.908 1.72288 182.443 1.63013C183.02 1.53009 183.597 1.43123 184.174 1.33271C184.457 1.28447 184.739 1.23604 185.022 1.18739C185.42 1.11917 185.818 1.05237 186.216 0.985959C186.445 0.947196 186.675 0.908433 186.911 0.868496C187.548 0.786804 188.155 0.770213 188.796 0.785258Z"
                      fill="#0D69F2"
                    />
                    <path
                      d="M150.648 0.78418C152.6 0.8664 154.502 1.31959 156.41 1.71113C156.768 1.78389 157.126 1.85657 157.484 1.92916C158.015 2.03685 158.546 2.14465 159.077 2.25291C160.043 2.45005 161.011 2.64394 161.979 2.83257C162.179 2.87166 162.379 2.91075 162.585 2.95102C162.934 3.01888 163.283 3.08618 163.632 3.15276C164.683 3.35919 165.698 3.63706 166.719 3.96228C166.638 4.70816 166.503 5.42137 166.311 6.14723C166.252 6.37323 166.252 6.37323 166.192 6.60381C166.013 6.96383 166.013 6.96383 165.723 7.09494C165.127 7.15999 164.683 6.99453 164.112 6.81507C163.993 6.77834 163.874 6.74161 163.752 6.70378C163.359 6.5818 162.967 6.45729 162.575 6.33276C162.301 6.24695 162.026 6.1613 161.751 6.07581C161.027 5.84981 160.303 5.62165 159.579 5.39304C158.85 5.16327 158.121 4.93544 157.392 4.7075C156.568 4.44987 155.744 4.19203 154.921 3.93281C154.795 3.89322 154.669 3.85364 154.54 3.81285C154.295 3.73578 154.051 3.65845 153.807 3.58083C153.233 3.39925 152.659 3.22351 152.08 3.06018C151.871 3.00013 151.662 2.94008 151.446 2.87822C151.262 2.82716 151.077 2.7761 150.887 2.7235C150.75 2.66617 150.613 2.60885 150.471 2.54979C150.24 1.85472 150.428 1.46838 150.648 0.78418Z"
                      fill="#0D69F2"
                    />
                    <path
                      d="M60.0469 14.0278C61.2567 14.0147 62.4664 14.0051 63.6763 13.9987C64.0876 13.996 64.4989 13.9924 64.9101 13.9878C65.5024 13.9814 66.0946 13.9784 66.687 13.9761C66.8699 13.9733 67.0528 13.9706 67.2412 13.9678C68.2764 13.9675 69.0281 14.0452 69.937 14.5575C70.1379 14.9591 70.1663 15.2359 70.1909 15.683C70.2016 15.8192 70.2123 15.9553 70.2233 16.0955C70.0861 16.6011 69.8501 16.7832 69.414 17.0582C68.643 17.3745 67.7999 17.2905 66.9802 17.2749C66.7817 17.2739 66.5833 17.2729 66.3789 17.2718C65.7472 17.2679 65.1158 17.259 64.4842 17.25C64.0551 17.2465 63.626 17.2433 63.1969 17.2404C62.1468 17.2326 61.0969 17.2204 60.0469 17.2059C60.0469 16.1571 60.0469 15.1083 60.0469 14.0278Z"
                      fill="#020816"
                    />
                    <path
                      d="M159.48 15.6175C160.341 16.0175 160.834 16.5276 161.246 17.3831C161.387 18.2699 161.351 18.9371 160.948 19.7446C159.963 20.8621 159.963 20.8621 159.105 21.0081C157.082 20.9891 157.082 20.9891 156.301 20.2081C155.636 19.2995 155.625 18.2909 155.771 17.2065C156.554 15.5825 157.821 15.4172 159.48 15.6175Z"
                      fill="#0D68F2"
                    />
                    <path
                      d="M180.638 15.5714C180.821 15.5669 181.004 15.5623 181.193 15.5576C182.025 15.6473 182.467 15.9787 183.011 16.5977C183.579 17.3888 183.584 18.0155 183.497 18.9702C183.248 19.6895 182.882 20.2758 182.273 20.751C181.45 21.1138 180.485 21.0436 179.611 20.9124C178.815 20.5142 178.408 20.1377 178.022 19.3233C177.859 18.4082 177.799 17.5775 178.287 16.7598C179.04 15.8874 179.476 15.5886 180.638 15.5714Z"
                      fill="#0C68F2"
                    />
                    <path
                      d="M46.2717 22.1481C45.942 23.1571 45.4093 23.4367 44.5056 23.9137C43.3892 24.4452 42.2428 24.4755 41.0286 24.4765C40.8946 24.4787 40.7606 24.4809 40.6226 24.4831C39.9392 24.4849 39.6123 24.4779 39.0307 24.0903C38.9976 23.6268 38.9976 23.6268 39.0307 23.2075C41.1373 21.8912 43.8702 22.0946 46.2717 22.1481Z"
                      fill="#020E16"
                    />
                  </svg>
                </Link>
                <svg
                  className="w-[274px] h-[3px]"
                  viewBox="0 0 274 3"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect
                    y="0.780273"
                    width={274}
                    height={2}
                    fill="white"
                    fillOpacity="0.1"
                  />
                  <rect
                    x="0.189453"
                    y="0.920898"
                    width={75}
                    height={2}
                    fill="#336AEA"
                  />
                </svg>
              </div>

              <p className="dm_sans text-base font-normal text-[#8C8F94]">
                Setting the standard in service. Our global network has the
                power to help businesses grow.{" "}
              </p>
              <div className="flex items-center space-x-4 pt-10">
                <Link href="">
                  <svg
                    width={10}
                    height={18}
                    viewBox="0 0 10 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M8.71875 9.31152H6.375V16.3115H3.25V9.31152H0.6875V6.43652H3.25V4.21777C3.25 1.71777 4.75 0.311523 7.03125 0.311523C8.125 0.311523 9.28125 0.530273 9.28125 0.530273V2.99902H8C6.75 2.99902 6.375 3.74902 6.375 4.56152V6.43652H9.15625L8.71875 9.31152Z"
                      fill="white"
                    />
                    <rect
                      fill="black"
                      fillOpacity={0}
                      y="-1.2"
                      width={10}
                      height="18.4"
                    />
                  </svg>
                </Link>

                <Link href="">
                  <svg
                    width={15}
                    height={18}
                    viewBox="0 0 15 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M8 4.71777C9.96875 4.71777 11.5938 6.34277 11.5938 8.31152C11.5938 10.3115 9.96875 11.9053 8 11.9053C6 11.9053 4.40625 10.3115 4.40625 8.31152C4.40625 6.34277 6 4.71777 8 4.71777ZM8 10.6553C9.28125 10.6553 10.3125 9.62402 10.3125 8.31152C10.3125 7.03027 9.28125 5.99902 8 5.99902C6.6875 5.99902 5.65625 7.03027 5.65625 8.31152C5.65625 9.62402 6.71875 10.6553 8 10.6553ZM12.5625 4.59277C12.5625 5.06152 12.1875 5.43652 11.7188 5.43652C11.25 5.43652 10.875 5.06152 10.875 4.59277C10.875 4.12402 11.25 3.74902 11.7188 3.74902C12.1875 3.74902 12.5625 4.12402 12.5625 4.59277ZM14.9375 5.43652C15 6.59277 15 10.0615 14.9375 11.2178C14.875 12.3428 14.625 13.3115 13.8125 14.1553C13 14.9678 12 15.2178 10.875 15.2803C9.71875 15.3428 6.25 15.3428 5.09375 15.2803C3.96875 15.2178 3 14.9678 2.15625 14.1553C1.34375 13.3115 1.09375 12.3428 1.03125 11.2178C0.96875 10.0615 0.96875 6.59277 1.03125 5.43652C1.09375 4.31152 1.34375 3.31152 2.15625 2.49902C3 1.68652 3.96875 1.43652 5.09375 1.37402C6.25 1.31152 9.71875 1.31152 10.875 1.37402C12 1.43652 13 1.68652 13.8125 2.49902C14.625 3.31152 14.875 4.31152 14.9375 5.43652ZM13.4375 12.4365C13.8125 11.5303 13.7188 9.34277 13.7188 8.31152C13.7188 7.31152 13.8125 5.12402 13.4375 4.18652C13.1875 3.59277 12.7188 3.09277 12.125 2.87402C11.1875 2.49902 9 2.59277 8 2.59277C6.96875 2.59277 4.78125 2.49902 3.875 2.87402C3.25 3.12402 2.78125 3.59277 2.53125 4.18652C2.15625 5.12402 2.25 7.31152 2.25 8.31152C2.25 9.34277 2.15625 11.5303 2.53125 12.4365C2.78125 13.0615 3.25 13.5303 3.875 13.7803C4.78125 14.1553 6.96875 14.0615 8 14.0615C9 14.0615 11.1875 14.1553 12.125 13.7803C12.7188 13.5303 13.2188 13.0615 13.4375 12.4365Z"
                      fill="white"
                    />
                  </svg>
                </Link>
                <Link href="">
                  <svg
                    width={14}
                    height={18}
                    viewBox="0 0 14 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M3.125 14.3115H0.21875V4.96777H3.125V14.3115ZM1.65625 3.71777C0.75 3.71777 0 2.93652 0 1.99902C0 1.09277 0.75 0.342773 1.65625 0.342773C2.59375 0.342773 3.34375 1.09277 3.34375 1.99902C3.34375 2.93652 2.59375 3.71777 1.65625 3.71777ZM13.9688 14.3115H11.0938V9.78027C11.0938 8.68652 11.0625 7.31152 9.5625 7.31152C8.0625 7.31152 7.84375 8.46777 7.84375 9.68652V14.3115H4.9375V4.96777H7.71875V6.24902H7.75C8.15625 5.53027 9.09375 4.74902 10.5 4.74902C13.4375 4.74902 14 6.68652 14 9.18652V14.3115H13.9688Z"
                      fill="white"
                    />
                  </svg>
                </Link>
              </div>
            </div>
            <div>
              <div className="">
                <h5 className="mb-4 text-xl font-bold outfit text-white tracking-[-0.2px] uppercase ">
                  Explore
                </h5>
                <svg
                  className="lg:w-[274px] w-[180px] h-[3px]"
                  viewBox="0 0 274 3"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect
                    y="0.780273"
                    width={274}
                    height={2}
                    fill="white"
                    fillOpacity="0.1"
                  />
                  <rect
                    x="0.189453"
                    y="0.920898"
                    width={75}
                    height={2}
                    fill="#336AEA"
                  />
                </svg>
              </div>

              <ul className="text-[#8C8F94] dm_sans text-base font-normal py-5">
                <li className="mb-4">
                  <a href="https://flowbite.com/" className="">
                    About
                  </a>
                </li>
                <li className="mb-4">
                  <a
                    href="https://tailwindcss.com/"
                    className="hover:underline"
                  >
                    Services{" "}
                  </a>
                </li>{" "}
                <li className="mb-4">
                  <a
                    href="https://tailwindcss.com/"
                    className="hover:underline"
                  >
                    News & Blogs{" "}
                  </a>
                </li>
                <li className="mb-4">
                  <a
                    href="https://tailwindcss.com/"
                    className="hover:underline"
                  >
                    Our Projects{" "}
                  </a>
                </li>
                <li className="mb-4">
                  <a
                    href="https://tailwindcss.com/"
                    className="hover:underline"
                  >
                    Contact{" "}
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <div className="mb-5">
                <h5 className="mb-4 text-xl font-bold outfit text-white tracking-[-0.2px] uppercase ">
                  Contact
                </h5>
                <svg
                  className="lg:w-[274px] w-[180px] h-[3px]"
                  viewBox="0 0 274 3"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect
                    y="0.780273"
                    width={274}
                    height={2}
                    fill="white"
                    fillOpacity="0.1"
                  />
                  <rect
                    x="0.189453"
                    y="0.920898"
                    width={75}
                    height={2}
                    fill="#336AEA"
                  />
                </svg>
              </div>
              <p className="dm_sans text-base font-medium text-white">
                66 Road Broklyn Street, 600 New York, USA
              </p>
              <ul className="text-white dm_sans text-base font-normal py-5">
                <li className="mb-4">
                  <a href="https://flowbite.com/" className="flex items-center">
                    <svg
                      width={16}
                      height={16}
                      viewBox="0 0 16 16"
                      fill="none"
                      className="mr-2"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clipPath="url(#clip0_829_14330)">
                        <path
                          d="M14.1738 2.7959C14.5645 2.81543 14.8965 2.95215 15.1699 3.20605C15.4238 3.47949 15.5605 3.81152 15.5801 4.20215C15.5605 4.6709 15.375 5.04199 15.0234 5.31543L8.63672 10.1201C8.26562 10.3545 7.89453 10.3545 7.52344 10.1201L1.13672 5.31543C0.785156 5.04199 0.599609 4.6709 0.580078 4.20215C0.599609 3.81152 0.736328 3.47949 0.990234 3.20605C1.26367 2.95215 1.5957 2.81543 1.98633 2.7959H14.1738ZM6.9668 10.8525C7.29883 11.1064 7.66992 11.2334 8.08008 11.2334C8.49023 11.2334 8.86133 11.1064 9.19336 10.8525L15.5801 6.07715V12.1709C15.5605 12.6982 15.375 13.1377 15.0234 13.4893C14.6719 13.8408 14.2324 14.0264 13.7051 14.0459H2.45508C1.92773 14.0264 1.48828 13.8408 1.13672 13.4893C0.785156 13.1377 0.599609 12.6982 0.580078 12.1709V6.07715L6.9668 10.8525Z"
                          fill="#336AEA"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_829_14330">
                          <rect
                            width={15}
                            height={15}
                            fill="white"
                            transform="matrix(1 0 0 -1 0.580078 15.9209)"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                    <EMAIL>
                  </a>
                </li>{" "}
                <li className="mb-4">
                  <a href="https://flowbite.com/" className="flex items-center">
                    <svg
                      className="mr-2"
                      width={16}
                      height={16}
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clipPath="url(#clip0_829_14334)">
                        <path
                          d="M15.0234 11.2041L11.8594 9.85645C11.4297 9.7002 11.0684 9.78809 10.7754 10.1201L9.48633 11.6729C8.4707 11.1846 7.5625 10.54 6.76172 9.73926C5.96094 8.93848 5.31641 8.03027 4.82812 7.01465L6.38086 5.72559C6.71289 5.43262 6.80078 5.07129 6.64453 4.6416L5.29688 1.47754C5.08203 1.04785 4.7207 0.87207 4.21289 0.950195L1.2832 1.62402C0.833984 1.76074 0.599609 2.06348 0.580078 2.53223C0.599609 5.0127 1.21484 7.25879 2.42578 9.27051C3.61719 11.2822 5.21875 12.8838 7.23047 14.0752C9.24219 15.2861 11.498 15.9014 13.998 15.9209C14.4473 15.9014 14.7402 15.667 14.877 15.2178L15.5508 12.2588C15.6289 11.79 15.4531 11.4385 15.0234 11.2041ZM10.6289 4.40723H12.0645V5.81348C12.1035 6.2627 12.3379 6.50684 12.7676 6.5459C13.1777 6.50684 13.4023 6.28223 13.4414 5.87207V4.40723H14.8477C15.2969 4.3877 15.541 4.16309 15.5801 3.7334C15.541 3.30371 15.3164 3.0791 14.9062 3.05957H13.4414V1.59473C13.4219 1.18457 13.1973 0.959961 12.7676 0.920898C12.3379 0.959961 12.1133 1.18457 12.0938 1.59473V3.00098H10.6289C10.2188 3.05957 9.99414 3.30371 9.95508 3.7334C9.99414 4.16309 10.2188 4.3877 10.6289 4.40723Z"
                          fill="#336AEA"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_829_14334">
                          <rect
                            width={15}
                            height={15}
                            fill="white"
                            transform="matrix(1 0 0 -1 0.580078 15.9209)"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                    +92 666 888 0000{" "}
                  </a>
                </li>
              </ul>
            </div>
            <div className="col-span-2 lg:col-span-1">
              <div className="mb-5">
                <h5 className="mb-4 text-xl font-bold outfit text-white tracking-[-0.2px] uppercase ">
                  Subscribe
                </h5>
                <svg
                  className="lg:w-[274px] w-[180px] h-[3px]"
                  viewBox="0 0 274 3"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect
                    y="0.780273"
                    width={274}
                    height={2}
                    fill="white"
                    fillOpacity="0.1"
                  />
                  <rect
                    x="0.189453"
                    y="0.920898"
                    width={75}
                    height={2}
                    fill="#336AEA"
                  />
                </svg>
              </div>
              <p className="dm_sans text-base font-medium text-white">
                Are you interested in follow to a particular website{" "}
              </p>

              <form className=" mx-auto mt-5">
                <div className="relative inline-block">
                  <input
                    type="search"
                    id="default-search"
                    className="inline-block relative p-4 ps-8 pe-10 text-sm text-white archivo font-normal placeholder:text-white border border-[#4D79CC] archivo outline-0  rounded-full"
                    placeholder="Your email address"
                    required
                  />
                  <button
                    type="submit"
                    className="text-white bg-[#4D79CC] absolute top-1/2 right-2 translate-y-[-50%]  font-medium  text-sm px-4 py-2 w-[45px] h-[45px] rounded-full"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="size-5 -rotate-20"
                    >
                      <path d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z" />
                    </svg>
                  </button>
                </div>
              </form>
            </div>
          </div>
          <div className="bg-[#07151D] py-4 text-center lg:border-t lg:border-white/10 mt-5">
            <p className="dm_sans text-base font-medium text-white">
              © 2024 
              <span className="font-semibold text-[#336AEA]">marktzoom</span> –
              All Rights Reserved –{" "}
              <span className="font-semibold text-[#336AEA]">marktzoom</span>
            </p>
          </div>
        </div>
      </footer>
    </>
  );
};

export default Footer;
