import Link from "next/link";
import React from "react";

const MainMenuItems = ({ link, children }) => {
  return link ? (
    <div>
      <Link
        href=""
        className="flex inter items-center py-3 rounded-sm space-x-2 text-[#535862] font-semibold text-sm px-4 group"
      >
        <span>icon</span>
        <div className="">hello</div>
      </Link>
    </div>
  ) : children.length > 0 ? (
    <div
      className={
        isActiveGroup
          ? "transition-all duration-500 ease-out mb-2 pb-1"
          : "transition-all duration-500 ease-out"
      }
    >
      <div className="">
        <a
          href="#"
          onClick={(e) => {
            e && e.preventDefault();
            setIsActiveGroup(!isActiveGroup);
          }}
          className="flex items-center py-4 mt-2 group"
        >
          <span>icon</span>
          <div className={textClasses}>{text}</div>
          <span>icon</span>
        </a>
        <div
          className={
            isActiveGroup
              ? "transition-all duration-500 ease-out opacity-100 overflow-hidden"
              : "transition-all duration-500 ease-out opacity-0 h-0 overflow-hidden"
          }
          style={{ height: isActiveGroup ? children.length * 49 : 0 }}
        >
          {children.map((i, k) => {
            return (
              <div className={wrapperClasses} key={k}>
                {link ? (
                  <Link
                    href={newRoute}
                    className="flex items-center py-3 group"
                  >
                    <span>icon</span>
                    <div className="">title</div>
                  </Link>
                ) : (
                  <div className="flex items-center py-3 mt-3 group">
                    <span>icon</span>
                    <div className="">title</div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  ) : (
    <div className="">
      <div className="flex items-center py-3 mt-3 group">
        <span>icon</span>
        <div className="">hello</div>
      </div>
    </div>
  );
};

export default MainMenuItems;
