"use client";
import React from "react";

const CustomFields = ({ custom_fields }) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden mt-6">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800">Additional Information</h2>
      </div>
      <div className="p-6">
        <dl className="space-y-4">
          {Object.entries(custom_fields).map(([key, value]) => {
            const formattedKey = key
              .split('_')
              .map(word => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' ')
              .replace(/\?/g, '');

            return (
              <div key={key} className="flex flex-col sm:flex-row sm:items-center">
                <dt className="text-sm font-medium text-gray-500 sm:w-1/3 sm:pr-4">
                  {formattedKey}
                </dt>
                <dd className="mt-1 text-sm text-gray-900 font-medium sm:mt-0 sm:w-2/3">
                  {value || <span className="text-gray-400">N/A</span>}
                </dd>
              </div>
            );
          })}
        </dl>
      </div>
    </div>
  );
};

export default CustomFields;