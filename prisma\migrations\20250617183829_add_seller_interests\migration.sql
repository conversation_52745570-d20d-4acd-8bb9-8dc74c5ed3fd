-- CreateTable
CREATE TABLE "seller_interested_categories" (
    "id" TEXT NOT NULL,
    "seller_id" TEXT NOT NULL,
    "category_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "seller_interested_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "seller_interested_subcategories" (
    "id" TEXT NOT NULL,
    "seller_id" TEXT NOT NULL,
    "subcategory_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "seller_interested_subcategories_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "seller_interested_categories_seller_id_idx" ON "seller_interested_categories"("seller_id");

-- CreateIndex
CREATE INDEX "seller_interested_categories_category_id_idx" ON "seller_interested_categories"("category_id");

-- CreateIndex
CREATE UNIQUE INDEX "seller_interested_categories_seller_id_category_id_key" ON "seller_interested_categories"("seller_id", "category_id");

-- CreateIndex
CREATE INDEX "seller_interested_subcategories_seller_id_idx" ON "seller_interested_subcategories"("seller_id");

-- CreateIndex
CREATE INDEX "seller_interested_subcategories_subcategory_id_idx" ON "seller_interested_subcategories"("subcategory_id");

-- CreateIndex
CREATE UNIQUE INDEX "seller_interested_subcategories_seller_id_subcategory_id_key" ON "seller_interested_subcategories"("seller_id", "subcategory_id");

-- AddForeignKey
ALTER TABLE "seller_interested_categories" ADD CONSTRAINT "seller_interested_categories_seller_id_fkey" FOREIGN KEY ("seller_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "seller_interested_categories" ADD CONSTRAINT "seller_interested_categories_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "seller_interested_subcategories" ADD CONSTRAINT "seller_interested_subcategories_seller_id_fkey" FOREIGN KEY ("seller_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "seller_interested_subcategories" ADD CONSTRAINT "seller_interested_subcategories_subcategory_id_fkey" FOREIGN KEY ("subcategory_id") REFERENCES "sub_categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;
