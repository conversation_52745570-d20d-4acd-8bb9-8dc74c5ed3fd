"use client";

import React, { useState, useEffect } from "react";
import { useMutateApiMutation } from "../../../redux/services/api";
import GoBack from "@/components/backend/GoBack";
import Image from "next/image";
import Button from "@/components/backend/Button";
import { useRouter } from 'next/navigation';
import Link from 'next/link';

const ForgotPassword = () => {
  const [formData, setFormData] = useState({ email: "" });
  const [errors, setErrors] = useState({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();

  // Example: Mutating data (POST request)
  const [mutateApi, { data: mutationData, error: mutationError }] = useMutateApiMutation();

  useEffect(() => {
    if (mutationData) {
      setIsSubmitted(true);
      setIsLoading(false);
    }
    if (mutationError) {
      console.error("Mutation API Error:", mutationError);
      setIsLoading(false);
      setErrors({ server: mutationError.data?.message || "An error occurred. Please try again." });
    }
  }, [mutationData, mutationError]);

  const validateForm = () => {
    let newErrors = {};
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      setIsLoading(true);
      mutateApi({ endpoint: "/auth/forgot-password", data: formData });
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    // Clear error when user starts typing
    if (errors[e.target.name]) {
      setErrors({ ...errors, [e.target.name]: null });
    }
  };

  return (
    <>
      <div className="flex min-h-screen">
        {/* Left Side - Form */}
        <div className="w-full lg:w-1/2 p-8 flex flex-col">
          <div className="max-w-md mx-auto w-full">
            {/* Back Button */}
            <div className="mb-16">
              <GoBack />
            </div>

            {/* Logo */}
            <div className="mb-8">
              <Image
                src="/assets/backend_assets/images/site-logo.svg"
                alt="logo"
                width={100}
                height={100}
                className="w-[200px]"
              />
            </div>

            {isSubmitted ? (
              <div className="text-center">
                <div className="mb-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-16 w-16 text-green-500 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h2 className="text-3xl inter font-bold text-gray-800 mb-4">
                  Password Reset Sent
                </h2>
                <p className="text-gray-600 mb-8 poppins">
                  We&apos;ve sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password.
                </p>
                <p className="text-gray-600 mb-8 poppins">
                  Didn&apos;t receive the email? <button
                    onClick={() => setIsSubmitted(false)}
                    className="text-blue-500 hover:underline cursor-pointer"
                  >
                    Try again
                  </button> or check your spam folder.
                </p>
                <Button
                  onClick={() => router.replace('/login')}
                  className="w-full bg-blue-500 text-white py-3 rounded-md font-semibold transition text-xl"
                >
                  Back to Login
                </Button>
              </div>
            ) : (
              <>
                <h2 className="text-3xl inter font-bold text-gray-800 mb-2">
                  Forgot Password
                </h2>
                <p className="text-gray-600 mt-4 mb-8 poppins">
                  Enter your email address and we&apos;ll send you a link to reset your password.
                </p>

                {errors.server && (
                  <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
                    {errors.server}
                  </div>
                )}

                <form onSubmit={handleSubmit}>
                  <div className="mb-6">
                    <div className={`flex items-center border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-md`}>
                      <div className="pl-4 pr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <input
                        type="email"
                        name="email"
                        className={`w-full py-3 px-2 text-gray-700 focus:outline-none ${errors.email ? 'placeholder-red-300' : ''}`}
                        placeholder="Email"
                        value={formData.email}
                        onChange={handleChange}
                      />
                    </div>
                    {errors.email && (
                      <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-blue-500 text-white py-3 rounded-md font-semibold transition text-xl"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </span>
                    ) : (
                      "Send Reset Link"
                    )}
                  </Button>
                </form>

                <div className="text-center mt-6 poppins font-medium text-sm">
                  <span className="text-gray-600">Remember your password?</span>
                  <Link href="/login" className="text-blue-500 hover:underline ml-1">
                    Log in
                  </Link>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Right Side - Image */}
        <div className="hidden lg:block lg:w-1/2 relative overflow-hidden">
          <Image
            src="/assets/login-img.png"
            alt="Login Image"
            height={1000}
            width={1000}
            className="object-contain h-[99vh] w-full"
            priority
          />
        </div>
      </div>
    </>
  );
};

export default ForgotPassword;