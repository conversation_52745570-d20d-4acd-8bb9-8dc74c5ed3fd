"use client";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import ImageUpload from "@/components/backend/ImageUpload";
import SelectInput from "@/components/backend/SelectInput";
import TextInput from "@/components/backend/TextInput";

import React, { useState, useEffect } from "react";
import { useFetchApiQuery, usePutApiMutation } from "@/redux/services/api";
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import DatePicker from "react-datepicker";
import { format, isValid, parseISO } from "date-fns";
import "react-datepicker/dist/react-datepicker.css";
import "@/styles/datepicker.css";
import toast from 'react-hot-toast';
import DynamicFormField from "@/components/backend/DynamicFormField";
import { useRouter } from 'next/navigation';
import { handleUnauthorized } from '@/utils/auth';
import { asseturl } from "@/config";
import Image from 'next/image';

// Validation schema
const validationSchema = Yup.object({
  category_id: Yup.string().required('Category is required'),
  sub_category_id: Yup.string().when(['category_id'], {
    is: (category_id) => category_id && category_id.length > 0,
    then: () => Yup.string().required('Sub-category is required'),
    otherwise: () => Yup.string().notRequired(),
  }),
  title: Yup.string().required('Title is required'),
  description: Yup.string(),
  quantity: Yup.number()
    .typeError('Quantity must be a number')
    .min(1, 'Quantity must be at least 1')
    .required('Quantity is required'),
  budget_min: Yup.number()
    .typeError('Minimum budget must be a number')
    .min(0, 'Minimum budget must be at least 0')
    .required('Minimum budget is required'),
  budget_max: Yup.number()
    .typeError('Maximum budget must be a number')
    .min(0, 'Maximum budget must be at least 0')
    .test('is-greater', 'Maximum budget must be greater than or equal to minimum budget',
      function(value) {
        const { budget_min } = this.parent;
        return !value || !budget_min || parseFloat(value) >= parseFloat(budget_min);
      })
    .required('Maximum budget is required'),
  deadline: Yup.date()
    .required('Please select a deadline')
    .test('is-future', 'Deadline must be in the future', function(value) {
      if (!value) return true; // Allow null/undefined
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return new Date(value) >= today;
    }),
  urgency: Yup.string().required('Urgency level is required'),
  location: Yup.string(),
  additional_info: Yup.string(),
  remarks: Yup.string(),
  // Custom validation for images
  image: Yup.mixed().test(
    'has-images',
    'At least one image is required',
    function(value) {
      // Check if there are existing images or a new image is being uploaded
      const { existingImages } = this.options.context;

      // If there are existing images, no need to upload new ones
      if (existingImages && existingImages.length > 0) {
        return true;
      }

      // If no existing images, then a new image must be uploaded
      return value && value.length > 0;
    }
  ),
});

const Edit = ({ params }) => {
  // Unwrap the params promise
  const unwrappedParams = React.use(params);
  const requestId = unwrappedParams?.requestId;
  const router = useRouter();
  const [subCategories, setSubCategories] = useState([]);
  const [imageFileName, setImageFileName] = useState("");
  const [fileFileName, setFileFileName] = useState("");
  const [serverError, setServerError] = useState("");
  const [existingImages, setExistingImages] = useState([]);
  const [removedImages, setRemovedImages] = useState([]);
  const [existingFile, setExistingFile] = useState(null);
  const [formFields, setFormFields] = useState([]);
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState(null);
  const [dynamicValidationSchema, setDynamicValidationSchema] = useState(validationSchema);
  const [customFieldsInitialValues, setCustomFieldsInitialValues] = useState({});
  

  // Reference to track previous category ID
  const prevCategoryIdRef = React.useRef(null);

  // Fetch request details
  const { data: responseData, isLoading, error } = useFetchApiQuery({
    endpoint: requestId ? `/buyer/requests/${requestId}` : null,
    skip: !requestId,
  });

  // Fetch categories
  const categories = useFetchApiQuery({
    endpoint: "/buyer/categories",
    skip: false,
  });

  // Handle 403 errors in API queries
  useEffect(() => {
    if (error?.status === 403 || categories.error?.status === 403) {
      handleUnauthorized(error || categories.error, router);
    }
  }, [error, categories.error, router]);

  const [putApi] = usePutApiMutation();

  // Set initial values when data is loaded
  useEffect(() => {
    if (responseData?.data) {
      const requestData = responseData.data;
      setExistingImages(requestData.request_attachments || []);
      setExistingFile(requestData.file || null);
      setFileFileName(requestData.file ? requestData.file.split('/').pop() : "");
      setSelectedSubCategoryId(requestData.sub_category_id);

      // Initialize custom fields from response data
      if (requestData.custom_fields) {
        setCustomFieldsInitialValues(requestData.custom_fields);
      }

      // If category is selected, update subcategories
      if (requestData.category_id && categories?.data?.data) {
        const category = categories.data.data.find(cat => cat.id === requestData.category_id);
        if (category && category.sub_categories) {
          setSubCategories(category.sub_categories);
          prevCategoryIdRef.current = requestData.category_id;
        }
      }
    }
  }, [responseData, categories?.data?.data]);

  const formatDate = (date) => {
    if (!date) return '';
    try {
      // Handle both Date objects and ISO strings
      const dateObj = date instanceof Date ? date : parseISO(date);
      if (!isValid(dateObj)) return '';
      return format(dateObj, 'yyyy-MM-dd');
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleDateChange = (date, setFieldValue) => {
    setFieldValue('deadline', date ? date.toISOString().split('T')[0] : null);
  };

  const handleImageChange = (e, setFieldValue) => {
    const files = e.target.files;
    if (files.length > 0) {
      const fileNames = Array.from(files).map(file => file.name);
      setImageFileName(fileNames.join(", "));
      setFieldValue('image', Array.from(files));
    } else {
      setImageFileName("");
      setFieldValue('image', null);
    }
  };

  const handleFileChange = (e, setFieldValue) => {
    const file = e.target.files[0];
    if (file) {
      setFileFileName(file.name);
      setFieldValue('file', file);
      setExistingFile(null);
    } else {
      setFileFileName("");
      setFieldValue('file', null);
    }
  };

  const handleRemoveImage = (imageId, setFieldValue) => {
    if (imageId) {
      if (existingImages.length === 1 && !imageFileName) {
        toast.error('At least one image is required');
        return;
      }

      setRemovedImages(prev => [...prev, imageId]);
      setExistingImages(prev => prev.filter(img => img.id !== imageId));
    } else {
      handleCloseImageClick(setFieldValue);
    }
  };

  const handleCloseImageClick = (setFieldValue) => {
    setImageFileName("");
    setFieldValue('image', null);
  };

  const handleCloseFileClick = (setFieldValue) => {
    setFileFileName("");
    setFieldValue('file', null);
    setExistingFile(null);
  };

  const handleFormSubmit = (values, { setSubmitting }) => {
    setServerError("");

    const formPayload = new FormData();

    // Append all form fields to FormData
    for (const key in values) {
      if (values[key] !== null && values[key] !== undefined) {
        if (key === 'image' && values[key]?.length > 0) {
          values[key].forEach(image => formPayload.append(key, image));
        } else if (key === 'file') {
          if (values[key]) {
            formPayload.append(key, values[key]);
          }
        } else if (key === 'custom_fields') {
          // Stringify custom fields object
          formPayload.append(key, JSON.stringify(values[key]));
        } else {
          formPayload.append(key, values[key]);
        }
      }
    }

    // Append removed images if any
    if (removedImages.length > 0) {
      formPayload.append('removed_images', JSON.stringify(removedImages));
    }

    const loadingToast = toast.loading('Updating your request...');

    putApi({
      endpoint: `/buyer/requests/${requestId}`,
      data: formPayload,
      headers: {}
    }).then(() => {
      toast.dismiss(loadingToast);
      toast.success('Request updated successfully!');
      setTimeout(() => {
        router.push('/requests/pending');
      }, 1000);
    }).catch((error) => {
      toast.dismiss(loadingToast);
      if (!handleUnauthorized(error, router)) {
        const errorMessage = error?.data?.message || 'Failed to update request. Please try again.';
        setServerError(errorMessage);
        toast.error(errorMessage);
        setSubmitting(false);
      }
    });
  };

  // Initial form values
  const initialValues = {
    category_id: responseData?.data?.category_id || "",
    sub_category_id: responseData?.data?.sub_category_id || "",
    title: responseData?.data?.title || "",
    short_description: responseData?.data?.short_description || "",
    description: responseData?.data?.description || "",
    quantity: responseData?.data?.quantity || "",
    budget_min: responseData?.data?.budget_min || "",
    budget_max: responseData?.data?.budget_max || "",
    deadline: responseData?.data?.deadline || "",
    urgency: responseData?.data?.urgency || "Normal",
    status: responseData?.data?.status || "Pending",
    request_type: responseData?.data?.request_type || "General",
    location: responseData?.data?.location || "",
    additional_info: responseData?.data?.additional_info || "",
    remarks: responseData?.data?.remarks || "",
    image: null,
    file: null,
    custom_fields: customFieldsInitialValues // Initialize with custom fields from API
  };

  const categoryOptions = categories?.data?.data?.map(category => ({
    value: category.id,
    label: category.title
  })) || [];

  const currencyOptions = [
    { value: "USD", label: "USD" },
    { value: "EUR", label: "EUR" },
    { value: "GBP", label: "GBP" }
  ];

  const urgencyOptions = [
    { value: "Low", label: "Low" },
    { value: "Normal", label: "Normal" },
    { value: "High", label: "High" },
    { value: "Urgent", label: "Urgent" }
  ];

  const subCategoryDetails = useFetchApiQuery({
    endpoint: selectedSubCategoryId ? `/buyer/subcategories/${selectedSubCategoryId}` : null,
    skip: !selectedSubCategoryId,
  });

  // Update form fields when subcategory details are loaded
  useEffect(() => {
    if (subCategoryDetails.data?.data?.form_fields) {
      const fields = subCategoryDetails.data.data.form_fields;
      setFormFields(fields);

      const customFieldsSchema = {};

      fields.forEach(field => {
        const fieldName = field.label_name.toLowerCase().replace(/\s+/g, '_');

        if (field.is_required) {
          switch (field.input_type) {
            case 'NUMBER':
              customFieldsSchema[fieldName] = Yup.number()
                .typeError(`${field.label_name} must be a number`)
                .required(`${field.label_name} is required`);
              break;
            case 'EMAIL':
              customFieldsSchema[fieldName] = Yup.string()
                .email(`Please enter a valid email address`)
                .required(`${field.label_name} is required`);
              break;
            case 'FILE':
              customFieldsSchema[fieldName] = Yup.mixed()
                .required(`${field.label_name} is required`);
              break;
            case 'DATE':
              customFieldsSchema[fieldName] = Yup.date()
                .typeError(`${field.label_name} must be a valid date`)
                .required(`${field.label_name} is required`);
              break;
            case 'TIME':
              customFieldsSchema[fieldName] = Yup.string()
                .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, `${field.label_name} must be in HH:MM format`)
                .required(`${field.label_name} is required`);
              break;
            case 'DATETIME':
              customFieldsSchema[fieldName] = Yup.date()
                .typeError(`${field.label_name} must be a valid date and time`)
                .required(`${field.label_name} is required`);
              break;
            case 'URL':
              customFieldsSchema[fieldName] = Yup.string()
                .url(`Please enter a valid URL`)
                .required(`${field.label_name} is required`);
              break;
            case 'PHONE':
              customFieldsSchema[fieldName] = Yup.string()
                .matches(/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,
                  `Please enter a valid phone number`)
                .required(`${field.label_name} is required`);
              break;
            case 'CHECKBOX':
              customFieldsSchema[fieldName] = Yup.boolean()
                .oneOf([true], `${field.label_name} must be checked`)
                .required(`${field.label_name} is required`);
              break;
            default:
              customFieldsSchema[fieldName] = Yup.string()
                .required(`${field.label_name} is required`);
          }
        } else {
          switch (field.input_type) {
            case 'NUMBER':
              customFieldsSchema[fieldName] = Yup.number()
                .typeError(`${field.label_name} must be a number`)
                .nullable();
              break;
            case 'EMAIL':
              customFieldsSchema[fieldName] = Yup.string()
                .email(`Please enter a valid email address`)
                .nullable();
              break;
            case 'DATE':
              customFieldsSchema[fieldName] = Yup.date()
                .typeError(`${field.label_name} must be a valid date`)
                .nullable();
              break;
            case 'TIME':
              customFieldsSchema[fieldName] = Yup.string()
                .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, `${field.label_name} must be in HH:MM format`)
                .nullable();
              break;
            case 'DATETIME':
              customFieldsSchema[fieldName] = Yup.date()
                .typeError(`${field.label_name} must be a valid date and time`)
                .nullable();
              break;
            case 'URL':
              customFieldsSchema[fieldName] = Yup.string()
                .url(`Please enter a valid URL`)
                .nullable();
              break;
            case 'PHONE':
              customFieldsSchema[fieldName] = Yup.string()
                .matches(/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,
                  `Please enter a valid phone number`)
                .nullable();
              break;
            default:
              customFieldsSchema[fieldName] = Yup.string().nullable();
          }
        }
      });

      setDynamicValidationSchema(
        validationSchema.shape({
          custom_fields: Yup.object().shape(customFieldsSchema)
        })
      );
    } else {
      setFormFields([]);
      setDynamicValidationSchema(validationSchema);
    }
  }, [subCategoryDetails.data]);

  if (isLoading) {
    return <div className="text-center py-10">Loading request details...</div>;
  }

  if (error && error.status !== 403) {
    return <div className="text-center py-10 text-red-500">Error loading request: {error.data?.message || 'Unknown error'}</div>;
  }

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold text-[#343A40] text-xl poppins">
          Edit Request{" "}
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4 pb-5 border border-[#CACACA] rounded-2xl">
          <h3 className="font-semibold text-[#343A40] text-[32px] leading-normal archivo">
            Edit Buyer Request
          </h3>
          <Formik
            initialValues={initialValues}
            validationSchema={dynamicValidationSchema}
            validationContext={{ existingImages }}
            onSubmit={handleFormSubmit}
            enableReinitialize
          >
            {({ values, setFieldValue, isSubmitting, errors, touched }) => {
              const errorFields = Object.keys(errors).filter(key => touched[key]);

              if (values.category_id && prevCategoryIdRef.current !== values.category_id) {
                prevCategoryIdRef.current = values.category_id;
                const category = categories?.data?.data?.find(cat => cat.id === values.category_id);
                if (category && category.sub_categories) {
                  setSubCategories(category.sub_categories);
                } else {
                  setSubCategories([]);
                }
              }

              const subCategoryOptions = subCategories.map(subCat => ({
                value: subCat.id,
                label: subCat.title
              }));

              return (
                <Form>
                  {serverError && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <h4 className="text-red-700 font-medium">{serverError}</h4>
                      </div>
                    </div>
                  )}

                  {errorFields.length > 0 && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <h4 className="text-red-700 font-medium">Please fix the following errors:</h4>
                      </div>
                      <ul className="list-disc pl-5 text-red-600">
                        {errorFields.map(field => (
                          <li key={field} className="text-sm">
                            {errors[field]}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    <div className="">
                      <SelectInput
                        label="Select Category"
                        options={categoryOptions}
                        value={values.category_id}
                        onChange={(e) => setFieldValue('category_id', e.target.value)}
                        placeholder="Select Category"
                        className="w-full h-11 !font-light"
                        name="category_id"
                        error={touched.category_id && errors.category_id}
                        required
                      />
                    </div>
                    <div className="">
                      <SelectInput
                        label="Select Sub Category"
                        options={subCategoryOptions}
                        value={values.sub_category_id}
                        onChange={(e) => {
                          setFieldValue('sub_category_id', e.target.value);
                          setSelectedSubCategoryId(e.target.value);
                        }}
                        placeholder="Select Sub Category"
                        className="w-full h-11 !font-light"
                        name="sub_category_id"
                        disabled={!values.category_id}
                        error={touched.sub_category_id && errors.sub_category_id}
                        required
                      />
                    </div>
                    <div className="">
                      <TextInput
                        className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                        label="Request Title"
                        placeholder="Write request title here"
                        name="title"
                        value={values.title}
                        onChange={(e) => setFieldValue('title', e.target.value)}
                        error={touched.title && errors.title}
                        required
                      />
                    </div>
                    <div className="">
                      <TextInput
                        className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                        label="Short descriptions"
                        placeholder="Write short description here"
                        name="short_description"
                        value={values.short_description}
                        onChange={(e) => setFieldValue('short_description', e.target.value)}
                        error={touched.short_description && errors.short_description}
                      />
                    </div>
                    <div className="">
                      <div className="mb-2">
                        <label className="text-sm archivo font-medium text-[#374151] inline-block">
                          Attach Image {existingImages.length === 0 && <span className="text-red-500">*</span>}
                        </label>
                      </div>

                      {existingImages.length > 0 && (
                        <div className="mb-3">
                          <p className="text-sm text-gray-500 mb-2">Existing Images:</p>
                          <div className="flex flex-wrap gap-2">
                            {existingImages.map((image) => (
                              <div key={image.id} className="relative">
                                <Image
                                  src={asseturl + image.file_path}
                                  alt="Request attachment"
                                  width={80}
                                  height={80}
                                  className="h-20 w-20 object-cover rounded border"
                                  unoptimized={process.env.NODE_ENV === 'development'}
                                />
                                <button
                                  type="button"
                                  onClick={() => handleRemoveImage(image.id, setFieldValue)}
                                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                  </svg>
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <ImageUpload
                        label={existingImages.length > 0 ? "Add More Images (Optional)" : ""}
                        required={existingImages.length === 0}
                        handleChange={(e) => handleImageChange(e, setFieldValue)}
                        fileName={imageFileName}
                        onClose={() => handleCloseImageClick(setFieldValue)}
                      />
                      {touched.image && errors.image && existingImages.length === 0 && (
                        <div className="text-red-500 text-sm mt-1">{errors.image}</div>
                      )}
                    </div>
                    <div className="">
                      <TextInput
                        className="w-full !placeholder:text-[#6B7280] !font-light"
                        label="Detailed Description"
                        placeholder="Write detailed description here"
                        name="description"
                        value={values.description}
                        onChange={(e) => setFieldValue('description', e.target.value)}
                        multiline={true}
                        row="5"
                        error={touched.description && errors.description}
                      />
                    </div>
                    <div className="">
                      <TextInput
                        className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
                        label="Quantity (Unit)"
                        placeholder="Enter your Quantity. Ex: Unit"
                        name="quantity"
                        value={values.quantity}
                        onChange={(e) => setFieldValue('quantity', e.target.value)}
                        error={touched.quantity && errors.quantity}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="">
                        <p className="text-sm archivo font-medium mb-2 text-[#374151] inline-block">
                          Minimum Budget <span className="text-red-500"> *</span>
                        </p>
                        <div className={`flex w-full border border-[#D1D5DB] rounded-lg ${touched.budget_min && errors.budget_min ? 'border-red-500' : ''}`}>
                          <TextInput
                            wrapperClassName="w-full"
                            className={`w-full !border-0 h-11 !placeholder:text-[#6B7280] !font-light }`}
                            placeholder="$ 0.00"
                            name="budget_min"
                            value={values.budget_min}
                            onChange={(e) => setFieldValue('budget_min', e.target.value)}
                            error={null}
                          />
                          <SelectInput
                            options={currencyOptions}
                            value="USD"
                            onChange={() => {}}
                            placeholder="USD"
                            className="w-24 h-11 !border-0 !placeholder:text-[#6B7280] new-custom-select !font-light"
                          />
                        </div>
                        {touched.budget_min && errors.budget_min && (
                          <div className="text-red-500 text-sm mt-1">{errors.budget_min}</div>
                        )}
                      </div>
                      <div className="">
                        <p className="text-sm archivo font-medium mb-2 text-[#374151] inline-block">
                          Maximum Budget <span className="text-red-500"> *</span>
                        </p>
                        <div className={`flex w-full border border-[#D1D5DB] rounded-lg ${touched.budget_max && errors.budget_max ? 'border-red-500' : ''}`}>
                          <TextInput
                            wrapperClassName="w-full"
                            className={`w-full !border-0 h-11 !placeholder:text-[#6B7280] !font-light ${touched.budget_max && errors.budget_max ? 'border-red-500' : ''}`}
                            placeholder="$ 0.00"
                            name="budget_max"
                            value={values.budget_max}
                            onChange={(e) => setFieldValue('budget_max', e.target.value)}
                            error={null}
                          />
                          <SelectInput
                            options={currencyOptions}
                            value="USD"
                            onChange={() => {}}
                            placeholder="USD"
                            className="w-24 h-11 !border-0 !placeholder:text-[#6B7280] new-custom-select !font-light"
                          />
                        </div>
                        {touched.budget_max && errors.budget_max && (
                          <div className="text-red-500 text-sm mt-1">{errors.budget_max}</div>
                        )}
                      </div>
                    </div>
                    <div className="">
                      <TextInput
                        className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
                        label="Additional Criteria"
                        placeholder="Ex: brands or others."
                        name="additional_info"
                        value={values.additional_info}
                        onChange={(e) => setFieldValue('additional_info', e.target.value)}
                        error={touched.additional_info && errors.additional_info}
                      />
                    </div>
                    <div className="datepicker">
                      <label className="text-sm archivo font-medium  text-[#374151] inline-block">
                        Deadline <span className="text-red-500"> *</span>
                      </label>

                      <div className={`${touched.deadline && errors.deadline ? 'border-red-500' : 'border-[#D1D5DB]'} rounded-lg`}>
                        <DatePicker
                          selected={values.deadline ? new Date(values.deadline) : null}
                          onChange={(date) => handleDateChange(date, setFieldValue)}
                          dateFormat="yyyy-MM-dd"
                          minDate={new Date()}
                          placeholderText="Select your deadline"
                          className="w-full h-11 py-2 border-0 text-[#374151] text-sm rounded-lg focus:outline-none"
                          wrapperClassName="w-full"
                          showMonthDropdown
                          showYearDropdown
                          dropdownMode="select"
                          isClearable={true}
                          disabledKeyboardNavigation
                          strictParsing
                          preventOpenOnFocus
                          customInput={
                            <div className="relative w-full">
                              <div className={`flex items-center border inter font-medium ${touched.deadline && errors.deadline ? 'border-red-500' : 'border-inherit'} text-[#374151] text-sm rounded-lg outline-0 block p-2.5 h-11`}>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  strokeWidth={1.5}
                                  stroke="currentColor"
                                  className="size-5 text-[#6B7280] mr-2"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"
                                  />
                                </svg>
                                <div className="w-full">
                                  {values.deadline ? (
                                    <span className="text-[#374151] leading-[1.7] text-sm inter font-normal">
                                      {formatDate(values.deadline)}
                                    </span>
                                  ) : (
                                    <span className="text-[#6B7280] leading-[1.7] text-sm inter font-light">
                                      Select your deadline
                                    </span>
                                  )}
                                </div>
                              </div>
                              <input
                                type="text"
                                className="absolute inset-0 opacity-0 cursor-pointer z-10 w-full"
                              />
                            </div>
                          }
                        />
                      </div>

                      {touched.deadline && errors.deadline && (
                        <div className="text-red-500 text-sm mt-2">{errors.deadline}</div>
                      )}
                    </div>
                    <div className="">
                      <SelectInput
                        label="Urgency Level"
                        options={urgencyOptions}
                        value={values.urgency}
                        onChange={(e) => setFieldValue('urgency', e.target.value)}
                        placeholder="Select Urgency"
                        className="w-full h-11 !font-light"
                        name="urgency"
                        error={touched.urgency && errors.urgency}
                      />
                    </div>
                    <div className="">
                      <p className="text-sm archivo font-medium mb-2 text-[#374151] inline-block">
                        File Attachment
                      </p>
                      <div className="flex items-center space-x-4 h-11 w-full border inter font-medium border-[#D1D5DB] text-[#374151] text-sm rounded-lg relative outline-0 overflow-hidden">
                        <input
                          type="file"
                          id="file-input"
                          className="hidden"
                          onChange={(e) => handleFileChange(e, setFieldValue)}
                        />

                        <label
                          htmlFor="file-input"
                          className="flex items-center justify-center p-2 bg-[#F3F3F3] w-20 font-semibold cursor-pointer transition-colors h-full"
                        >
                          <svg
                            width={10}
                            height={20}
                            viewBox="0 0 10 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M5 0.625C6.15994 0.626242 7.27202 1.08757 8.09222 1.90778C8.91243 2.72798 9.37376 3.84006 9.375 5V16.25C9.375 17.0788 9.04576 17.8737 8.45971 18.4597C7.87366 19.0458 7.0788 19.375 6.25 19.375C5.4212 19.375 4.62634 19.0458 4.04029 18.4597C3.45424 17.8737 3.125 17.0788 3.125 16.25V5C3.125 4.50272 3.32254 4.02581 3.67417 3.67417C4.02581 3.32254 4.50272 3.125 5 3.125C5.49728 3.125 5.97419 3.32254 6.32583 3.67417C6.67746 4.02581 6.875 4.50272 6.875 5V13.75C6.875 13.9158 6.80915 14.0747 6.69194 14.1919C6.57473 14.3092 6.41576 14.375 6.25 14.375C6.08424 14.375 5.92527 14.3092 5.80806 14.1919C5.69085 14.0747 5.625 13.9158 5.625 13.75V5C5.625 4.83424 5.55915 4.67527 5.44194 4.55806C5.32473 4.44085 5.16576 4.375 5 4.375C4.83424 4.375 4.67527 4.44085 4.55806 4.55806C4.44085 4.67527 4.375 4.83424 4.375 5V16.25C4.375 16.7473 4.57254 17.2242 4.92417 17.5758C5.27581 17.9275 5.75272 18.125 6.25 18.125C6.74728 18.125 7.22419 17.9275 7.57583 17.5758C7.92746 17.2242 8.125 16.7473 8.125 16.25V5C8.125 4.1712 7.79576 3.37634 7.20971 2.79029C6.62366 2.20424 5.8288 1.875 5 1.875C4.1712 1.875 3.37634 2.20424 2.79029 2.79029C2.20424 3.37634 1.875 4.1712 1.875 5V13.75C1.875 13.9158 1.80915 14.0747 1.69194 14.1919C1.57473 14.3092 1.41576 14.375 1.25 14.375C1.08424 14.375 0.925269 14.3092 0.808058 14.1919C0.690848 14.0747 0.625 13.9158 0.625 13.75V5C0.626241 3.84006 1.08758 2.72798 1.90778 1.90778C2.72798 1.08757 3.84006 0.626242 5 0.625Z"
                              fill="#6B7280"
                            />
                          </svg>
                        </label>

                        <div className="w-full">
                          {fileFileName ? (
                            <span className="text-[#6B7280] leading-[1.7] text-sm inter font-normal">
                              {fileFileName}
                            </span>
                          ) : existingFile ? (
                            <span className="text-[#6B7280] leading-[1.7] text-sm inter font-normal">
                              {existingFile.split('/').pop()}
                            </span>
                          ) : (
                            <span className="text-[#6B7280] leading-[1.7] text-sm inter font-light">
                              Ex: Pdf, Doc,Text.
                            </span>
                          )}
                        </div>
                        {(fileFileName || existingFile) && (
                          <button
                            type="button"
                            onClick={() => handleCloseFileClick(setFieldValue)}
                            className="text-[#6B7280] cursor-pointer absolute top-1/2 -translate-y-1/2 right-2 transition-colors"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                              strokeWidth={1.5}
                              stroke="currentColor"
                              className="w-5 h-5"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  {formFields.length > 0 && (
                    <div className="col-span-2 mt-4">
                      <h4 className="font-semibold text-[#343A40] text-lg mb-3">
                        Additional Information for {subCategoryDetails.data?.data?.title || 'Selected Subcategory'}
                      </h4>
                      <div className="grid grid-cols-2 gap-4">
                        {formFields.map((field, index) => {
                          const fieldName = field.label_name.toLowerCase().replace(/\s+/g, '_');
                          const fieldError =
                            touched.custom_fields?.[fieldName] &&
                            errors.custom_fields?.[fieldName];

                          const handleChange = (value) => {
                            if (!values.custom_fields) {
                              setFieldValue('custom_fields', {});
                            }
                            setFieldValue(`custom_fields.${fieldName}`, value);
                          };

                          const colSpan = field.input_type === 'TEXTAREA' ? 'col-span-2' : '';

                          return (
                            <div key={index} className={colSpan}>
                              <DynamicFormField
                                field={field}
                                fieldName={fieldName}
                                value={values.custom_fields?.[fieldName] || ''}
                                onChange={handleChange}
                                error={fieldError}
                                setFieldValue={setFieldValue}
                              />
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  <div className="mt-10 flex items-center space-x-4 justify-end">
                    <button
                      type="button"
                      className="py-2.5 px-5 text-sm font-medium text-[#374151] focus:outline-none bg-white rounded-lg border border-[#D1D5DB] inter"
                      onClick={() => router.push('/requests')}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="text-white bg-[#4F46E5] inter font-medium rounded-lg text-sm px-5 py-2.5 disabled:opacity-50"
                    >
                      {isSubmitting ? 'Updating...' : 'Update Request'}
                    </button>
                  </div>
                </Form>
              );
            }}
          </Formik>
        </div>
      </div>
    </>
  );
};

export default Edit;