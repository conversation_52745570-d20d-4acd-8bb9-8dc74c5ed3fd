"use client";
export const dynamic = "force-dynamic";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import Button from "@/components/backend/Button";
import Link from "next/link";
import React, { useState } from "react";
import { useFetchApiQuery, useMutateApiMutation } from "@/redux/services/api";
import toast from 'react-hot-toast';
import { handleUnauthorized } from '@/utils/auth';
import { useRouter } from 'next/navigation';
import { updateCartItem, removeFromCart, clearCart } from '@/utils/cart';

const Cart = () => {
  const router = useRouter();

  // Fetch cart data
  const { data: cartData, isLoading, error, refetch } = useFetchApiQuery({
    endpoint: "/buyer/cart",
    skip: false,
  });

  // Mutation hook for cart operations
  const [mutateApi, { isLoading: isUpdatingCart }] = useMutateApiMutation();

  // Handle quantity change
  const handleQuantityChange = async (itemId, newQuantity) => {
    if (newQuantity <= 0) {
      await handleRemoveItem(itemId);
      return;
    }

    try {
      await updateCartItem(mutateApi, itemId, newQuantity);
      refetch();
    } catch (error) {
      if (!handleUnauthorized(error, router)) {
        console.error('Error updating cart:', error);
      }
    }
  };

  // Handle remove item
  const handleRemoveItem = async (itemId) => {
    try {
      await removeFromCart(mutateApi, itemId);
      refetch();
    } catch (error) {
      if (!handleUnauthorized(error, router)) {
        console.error('Error removing item:', error);
      }
    }
  };

  // Handle clear cart
  const handleClearCart = async () => {
    try {
      await clearCart(mutateApi);
      refetch();
    } catch (error) {
      if (!handleUnauthorized(error, router)) {
        console.error('Error clearing cart:', error);
      }
    }
  };

  // Handle checkout
  const handleCheckout = () => {
    router.push('/buyer/checkout');
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    // Handle unauthorized access
    handleUnauthorized(error, router);

    return (
      <div className="text-center py-10">
        <p className="text-red-500">Error loading cart</p>
      </div>
    );
  }

  const cart = cartData?.data || {};
  const cartItems = cart?.cart_items || [];
  const totalItems = cart?.total_items || 0;
  const totalPrice = cart?.total_price || 0;

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold text-[#343A40] text-xl poppins">
          Shopping Cart
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4">
          {cartItems.length === 0 ? (
            <div className="text-center py-10">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              <p className="mt-4 text-lg text-gray-600">Your cart is empty</p>
              <p className="text-gray-500">Browse processed offers to add items to your cart.</p>
              <Link
                href="/buyer/processed-offers"
                className="mt-4 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Browse Offers
              </Link>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left rtl:text-right text-gray-500 inter">
                  <thead className="text-xs text-[#4C596E] capitalize bg-[#E2E8F0]">
                    <tr>
                      <th scope="col" className="px-3 py-3 font-medium">
                        #
                      </th>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Request Title
                      </th>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Price
                      </th>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Quantity
                      </th>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Subtotal
                      </th>
                      <th scope="col" className="px-3 py-3 font-medium">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {cartItems.map((item, index) => (
                      <tr key={item.id} className="bg-white border-b border-gray-200">
                        <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                          {index + 1}
                        </td>
                        <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568]">
                          <Link
                            href={`/buyer/offers/${item.offer_id}`}
                            className="text-indigo-600 hover:text-indigo-800 hover:underline"
                          >
                            {item.offer?.request?.title || 'N/A'}
                          </Link>
                          <p className="text-xs text-gray-500 mt-1">
                            Delivery Time: {item.offer?.delivery_time || 0} days
                          </p>
                        </td>
                        <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                          ${item.price}
                        </td>
                        <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                          <div className="flex items-center space-x-2">

                            <span className="w-8 text-center">{item.quantity}</span>

                          </div>
                        </td>
                        <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                          ${(item.price * item.quantity).toFixed(2)}
                        </td>
                        <td className="px-3 py-4 font-medium inter text-[#4A5568] whitespace-nowrap">
                          <Button
                            onClick={() => handleRemoveItem(item.id)}
                            disabled={isUpdatingCart}
                            className="text-sm font-medium px-3 py-1 bg-red-500 text-white rounded-sm hover:bg-red-600 transition-colors"
                          >
                            Remove
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="mt-6 flex flex-col md:flex-row justify-between items-start md:items-center">
                <Button
                  onClick={handleClearCart}
                  disabled={isUpdatingCart}
                  className="text-sm font-medium px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
                >
                  Clear Cart
                </Button>

                <div className="mt-4 md:mt-0 bg-gray-50 p-4 rounded-md">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600">Total Items:</span>
                    <span className="font-semibold">{totalItems}</span>
                  </div>
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-gray-600">Total Price:</span>
                    <span className="font-semibold text-lg">${totalPrice.toFixed(2)}</span>
                  </div>
                  <Button
                    onClick={handleCheckout}
                    className="w-full text-sm font-medium px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                  >
                    Proceed to Checkout
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Cart;
