/**
 * Test script for seller interests API functionality
 */

const { prisma } = require('../config/dbConfig');
const SellerInterestService = require('../services/sellerInterestService');

async function testSellerInterestsAPI() {
  console.log('🧪 Testing Seller Interests API functionality...\n');

  try {
    // Find a seller to test with
    const seller = await prisma.users.findFirst({
      where: {
        roles: {
          some: {
            role: {
              name: 'Seller'
            }
          }
        }
      }
    });

    if (!seller) {
      console.log('❌ No seller found in database to test with');
      return;
    }

    console.log(`✅ Found test seller: ${seller.first_name} ${seller.last_name} (${seller.id})`);

    // Find some categories to test with
    const categories = await prisma.categories.findMany({
      where: {
        is_deleted: false,
        is_active: true
      },
      take: 3
    });

    if (categories.length === 0) {
      console.log('❌ No categories found in database to test with');
      return;
    }

    console.log(`✅ Found ${categories.length} categories to test with`);

    // Test 1: Update interested categories (this is what was failing)
    console.log('\n1. Testing updateInterestedCategories...');
    const categoryIds = categories.map(cat => cat.id);
    
    try {
      const result = await SellerInterestService.updateInterestedCategories(seller.id, categoryIds);
      console.log(`✅ Successfully updated interested categories:`, result);
    } catch (error) {
      console.error('❌ Error updating interested categories:', error.message);
      throw error;
    }

    // Test 2: Get interested categories
    console.log('\n2. Testing getInterestedCategories...');
    try {
      const interestedCategories = await SellerInterestService.getInterestedCategories(seller.id);
      console.log(`✅ Retrieved ${interestedCategories.length} interested categories`);
      interestedCategories.forEach((cat, index) => {
        console.log(`   ${index + 1}. ${cat.title} (${cat.id})`);
      });
    } catch (error) {
      console.error('❌ Error getting interested categories:', error.message);
      throw error;
    }

    // Test 3: Update with fewer categories (test removal)
    console.log('\n3. Testing category removal...');
    const fewerCategoryIds = categoryIds.slice(0, 1); // Keep only first category
    
    try {
      const result = await SellerInterestService.updateInterestedCategories(seller.id, fewerCategoryIds);
      console.log(`✅ Successfully updated with fewer categories:`, result);
    } catch (error) {
      console.error('❌ Error updating with fewer categories:', error.message);
      throw error;
    }

    // Test 4: Verify the removal worked
    console.log('\n4. Verifying category removal...');
    try {
      const interestedCategories = await SellerInterestService.getInterestedCategories(seller.id);
      console.log(`✅ Now has ${interestedCategories.length} interested categories`);
      
      if (interestedCategories.length === 1) {
        console.log('✅ Category removal worked correctly');
      } else {
        console.log('❌ Category removal did not work as expected');
      }
    } catch (error) {
      console.error('❌ Error verifying category removal:', error.message);
      throw error;
    }

    // Test 5: Test with empty array (remove all)
    console.log('\n5. Testing removal of all categories...');
    try {
      const result = await SellerInterestService.updateInterestedCategories(seller.id, []);
      console.log(`✅ Successfully removed all categories:`, result);
    } catch (error) {
      console.error('❌ Error removing all categories:', error.message);
      throw error;
    }

    // Test 6: Verify all categories removed
    console.log('\n6. Verifying all categories removed...');
    try {
      const interestedCategories = await SellerInterestService.getInterestedCategories(seller.id);
      console.log(`✅ Now has ${interestedCategories.length} interested categories`);
      
      if (interestedCategories.length === 0) {
        console.log('✅ All categories removed successfully');
      } else {
        console.log('❌ Not all categories were removed');
      }
    } catch (error) {
      console.error('❌ Error verifying all categories removed:', error.message);
      throw error;
    }

    console.log('\n🎉 All seller interests API tests passed!');
    console.log('\n📋 Summary:');
    console.log('- ✅ updateInterestedCategories method working');
    console.log('- ✅ getInterestedCategories method working');
    console.log('- ✅ Category addition working');
    console.log('- ✅ Category removal working');
    console.log('- ✅ Complete category clearing working');
    console.log('\n🚀 The API endpoint should now work correctly!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
    throw error;
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Prisma connection closed');
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testSellerInterestsAPI()
    .then(() => {
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testSellerInterestsAPI };
