"use client";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState, useMemo } from "react";
import MenuDropdown from "./MenuDropdown";
import Icon from "./Icon";
import { usePathname, useSearchParams } from "next/navigation";
import { getUserRole } from "@/utils/user";

// Helper function to check if a link is active
const isLinkActive = (link, currentPath, searchParams) => {
  // If the link doesn't have query params, just check the path
  if (!link.includes('?')) {
    return currentPath === link;
  }

  // Split the link into path and query parts
  const [linkPath, linkQuery] = link.split('?');

  // If the current path doesn't match the link path, it's not active
  if (currentPath !== linkPath) {
    return false;
  }

  // Parse the link query params
  const linkParams = new URLSearchParams(linkQuery);

  // Check if all link query params are present in the current URL
  for (const [key, value] of linkParams.entries()) {
    if (searchParams.get(key) !== value) {
      return false;
    }
  }

  return true;
};

const Sidebar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenDropdown, setIsOpenDropdown] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const currentPath = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get user role from cookies
    const role = getUserRole();
    console.log("User role:", role);
    setUserRole(role);
  }, []);

  // Buyer menu items
  const buyerMenuItems = useMemo(() => [
    {
      icon: "dashboard",
      link: "/dashboard",
      title: "Dashboard",
      items: [],
    },
    {
      icon: "requests",
      link: "#",
      title: "Requests",
      items: [
        { label: "All Requests", link: "/requests/list" },
        { label: "Pending", link: "/requests/list?status=Pending" },
        { label: "Approved", link: "/requests/list?status=Approved" },
        { label: "Rejected", link: "/requests/list?status=Rejected" },
        { label: "Cancelled", link: "/requests/list?status=Cancelled" },
        { label: "Add New Request", link: "/requests/add" },
      ],
    },
    {
      icon: "offers",
      link: "#",
      title: "Offers",
      items: [
        { label: "Processed Offers", link: "/buyer/processed-offers" },
        { label: "Online Offers", link: "/offers/online" },
      ],
    },
    {
      icon: "shopping-cart",
      link: "/buyer/cart",
      title: "Shopping Cart",
      items: [],
    },
    {
      icon: "orders",
      link: "#",
      title: "Orders",
      items: [
        { label: "All orders", link: "/orders/all_orders" },
        { label: "Pre orders", link: "/orders/pre_orders" },
        { label: "Pending orders", link: "/orders/pending_order" },
        { label: "Processed orders", link: "/orders/processed_order" },
        { label: "Delivered orders", link: "/orders/delivered_order" },
        { label: "Canceled orders", link: "/orders/canceled_order" },
        { label: "Payment failed", link: "/orders/payment_failed" },
        { label: "Refunded orders", link: "/orders/refunded_order" },
        { label: "Invoices", link: "/orders/invoices" },
      ],
    },
    {
      icon: "my-favourites",
      link: "#",
      title: "My Favourites",
      items: [
        {
          label: "Favourite products",
          link: "/favourities/favourities_products",
        },
        {
          label: "Favourite services",
          link: "/favourities/favourities_services",
        },
        {
          label: "Favourite sellers",
          link: "/favourities/favourities_sellers",
        },
        {
          label: "Favourite suppliers",
          link: "/favourities/favourities_suppliers",
        },
        {
          label: "Favourite service providers",
          link: "/favourities/favourities_service_providers",
        },
      ],
    },
    {
      icon: "my-subscriptions",
      link: "#",
      title: "Subscriptions",
      items: [
        { label: "My Subscriptions", link: "/subscriptions" },
        { label: "Available Plans", link: "/subscription-plans" },
        { label: "Usage & Limits", link: "/subscription-usage" },
      ],
    },
    {
      icon: "statistic-reports",
      link: "#",
      title: "Statistic & Reports",
      items: [],
    },
    {
      icon: "notifications",
      link: "/notifications",
      title: "Notifications",
      items: [],
    },
    {
      icon: "my-chats",
      link: "/chat",
      title: "My chats",
      items: [],
    },
    {
      icon: "support",
      link: "#",
      title: "Support",
      items: [],
    },
  ], []);

  // Seller menu items
  const sellerMenuItems = useMemo(() => [
    {
      icon: "dashboard",
      link: "/dashboard",
      title: "Dashboard",
      items: [],
    },
    {
      icon: "requests",
      link: "#",
      title: "Requests",
      items: [
        { label: "Assigned Requests", link: "/seller/assigned-requests" },
        { label: "All Requests", link: "/seller/requests" },
      ],
    },
    {
      icon: "products",
      link: "#",
      title: "Products",
      items: [
        { label: "All Products", link: "/products/all" },
        { label: "Add New Product", link: "/products/add" },
        { label: "Categories", link: "/products/categories" },
      ],
    },
    {
      icon: "orders",
      link: "#",
      title: "Orders",
      items: [
        { label: "All Orders", link: "/orders/all" },
        { label: "Pending Orders", link: "/orders/pending" },
        { label: "Processing Orders", link: "/orders/processing" },
        { label: "Shipped Orders", link: "/orders/shipped" },
        { label: "Delivered Orders", link: "/orders/delivered" },
        { label: "Canceled Orders", link: "/orders/canceled" },
      ],
    },
    {
      icon: "customers",
      link: "/customers",
      title: "Customers",
      items: [],
    },
    {
      icon: "inventory",
      link: "/inventory",
      title: "Inventory",
      items: [],
    },
    {
      icon: "statistic-reports",
      link: "#",
      title: "Analytics",
      items: [
        { label: "Sales Reports", link: "/analytics/sales" },
        { label: "Customer Reports", link: "/analytics/customers" },
        { label: "Product Performance", link: "/analytics/products" },
      ],
    },
    {
      icon: "my-subscriptions",
      link: "#",
      title: "Subscriptions",
      items: [
        { label: "My Subscriptions", link: "/subscriptions" },
        { label: "Available Plans", link: "/subscription-plans" },
        { label: "Usage & Limits", link: "/subscription-usage" },
      ],
    },
    {
      icon: "settings",
      link: "/store-settings",
      title: "Store Settings",
      items: [],
    },
    {
      icon: "notifications",
      link: "/notifications",
      title: "Notifications",
      items: [],
    },
    {
      icon: "my-chats",
      link: "/chat",
      title: "Messages",
      items: [],
    },
    {
      icon: "support",
      link: "/support",
      title: "Support",
      items: [],
    },
  ], []);

  // Get the appropriate menu items based on user role
  const menuItems = useMemo(() => {
    if (userRole === 'Seller') {
      return sellerMenuItems;
    } else {
      return buyerMenuItems;
    }
  }, [userRole, buyerMenuItems, sellerMenuItems]);

  useEffect(() => {
    if (menuItems) {
      menuItems.forEach((item, index) => {
        if (item.items.some((subItem) => isLinkActive(subItem.link, currentPath, searchParams))) {
          setIsOpenDropdown(index);
        }
      });
    }
  }, [currentPath, searchParams, menuItems]);

  const toggleDropdown = (index) => {
    setIsOpenDropdown(prev => prev === index ? null : index);
  };

  return (
    <div
      id="sidebar"
      className={`overflow-y-auto h-full bg-white shadow-lg transition-transform md:relative md:translate-x-0 ${
        isOpen ? "translate-x-0" : "-translate-x-64"
      }`}
    >
      <div className="flex items-center justify-between h-[65px] px-8">
        <Image
          src="/assets/backend_assets/images/site-logo.svg"
          alt="logo"
          width={100}
          height={100}
          className="w-full"
          priority
        />
        <button
          className="md:hidden"
          onClick={() => setIsOpen(false)}
        ></button>
      </div>
      <div className="px-4 pb-10 h-[calc(100vh-90px)]">
        {/* Role indicator */}
        <div className={`mb-4 py-2 px-4 rounded-md text-sm font-medium ${
          userRole === 'Seller' ? 'bg-indigo-100 text-indigo-800' : 'bg-blue-100 text-blue-800'
        }`}>
          {userRole === 'Seller' ? 'Seller Account' : 'Buyer Account'}
        </div>

        <nav className="space-y-1">
          {menuItems && menuItems.map((item, index) =>
            item.items.length > 0 ? (
              <div key={index} className="mb-1">
                <MenuDropdown
                  title={item.title}
                  items={item.items}
                  icon={item.icon}
                  currentPath={currentPath}
                  isOpenDropdown={isOpenDropdown === index}
                  toggleDropdown={() => toggleDropdown(index)}
                />
              </div>
            ) : (
              <Link
                key={index}
                href={item.link}
                className={`flex inter items-center py-3 rounded-sm space-x-2 font-semibold text-sm px-4 ${
                  currentPath === item.link
                    ? userRole === 'Seller' ? "bg-indigo-600 text-white" : "bg-blue-600 text-white"
                    : "text-[#535862]"
                }`}
              >
                <Icon name={item.icon} />
                <span>{item.title}</span>
              </Link>
            )
          )}
        </nav>
      </div>
    </div>
  );
};

export default Sidebar;