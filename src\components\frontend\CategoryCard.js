import Image from "next/image";
import Link from "next/link";
import React from "react";

const CategoryCard = ({ imgSrc, title, rating, index }) => {
  return (
    <>
      <div
        className="bg-[#FFFFFF01] rounded-[10px] overflow-hidden shadow-md mb-3"
        data-aos="fade-left"
        data-aos-offset="200"
        data-aos-delay={50 * (index + 1)}
        data-aos-duration="1000"
        data-aos-easing="ease-in-out"
      >
        <Image
          src={imgSrc}
          width={1000}
          height={1000}
          quality={100}
          className="object-contain mx-auto"
          alt=""
        />
        <div className="flex flex-col p-4 border border-[#EBECED] border-t-0">
          <h6 className="text-[#242B3A] archivo font-semibold text-lg mb-1">
            {title}{" "}
          </h6>
          <p className="text-[#74788D] archivo font-normal text-sm mb-1">
            {rating} (254 Reviews)
          </p>

          <Link
            href=""
            className="text-base inline-block dm_sans px-5 py-2 mt-2 text-white font-normal bg-[#336AEA] text-center rounded-[8px] w-full"
          >
            Post a request{" "}
          </Link>
        </div>
      </div>
    </>
  );
};

export default CategoryCard;
