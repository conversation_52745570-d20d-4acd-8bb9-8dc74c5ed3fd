"use client";
import React, { useState } from "react";
import Icon from "./Icon";
import Link from "next/link";
import { useSearchParams } from "next/navigation";

// Helper function to check if a link is active
const isActiveLink = (link, currentPath, searchParams) => {
  // If the link doesn't have query params, just check the path
  if (!link.includes('?')) {
    return currentPath === link;
  }

  // Split the link into path and query parts
  const [linkPath, linkQuery] = link.split('?');

  // If the current path doesn't match the link path, it's not active
  if (currentPath !== linkPath) {
    return false;
  }

  // Parse the link query params
  const linkParams = new URLSearchParams(linkQuery);

  // Check if all link query params are present in the current URL
  for (const [key, value] of linkParams.entries()) {
    if (searchParams.get(key) !== value) {
      return false;
    }
  }

  return true;
};

const MenuDropdown = ({
  title,
  items,
  isOpenDropdown,
  toggleDropdown,
  icon,
  currentPath,
}) => {
  const searchParams = useSearchParams();
  return (
    <>
      <div
        className={` rounded-tl-sm rounded-tr-sm rounded-bl-md rounded-br-md ${
          isOpenDropdown ? "border border-[#79AFFF]" : ""
        }`}
      >
        <div
          className={`flex w-full justify-between inter items-center py-3 space-x-2  font-semibold text-sm px-4 rounded-tl-sm rounded-tr-sm cursor-pointer ${
            isOpenDropdown ? "bg-[#175CD3] text-white" : "text-[#374151]"
          }`}
          onClick={toggleDropdown}
        >
          <div className="flex items-center space-x-2">
            <Icon
              name={icon}
              className={`${isOpenDropdown ? "text-white" : "text-[#374151]"}`}
            />
            <span>{title}</span>
          </div>
          <Icon
            name={isOpenDropdown ? "cheveron-up" : "cheveron-down"}
            className="w-5 h-5"
          />
        </div>
        {isOpenDropdown && (
          <ul className="my-1">
            {items.map((item, index) => (
              <li key={index} className="">
                <Link
                  href={item.link}
                  className={`block inter text-sm px-4 py-2 text-[#374151] hover:font-bold transition duration-300 ease-in-out ${
                    isActiveLink(item.link, currentPath, searchParams)
                      ? "font-bold"
                      : "font-light"
                  }`}
                >
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
        )}
      </div>
    </>
  );
};

export default MenuDropdown;
