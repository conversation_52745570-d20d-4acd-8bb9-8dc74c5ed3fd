"use client";

import React from 'react';
import { calculateUsagePercentage, getUsageColorClass } from '@/services/subscriptionService';

const SubscriptionUsageCard = ({ usageData }) => {
  if (!usageData || !usageData.hasActiveSubscription) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Usage Status</h3>
          <p className="text-gray-600">No active subscription found. Subscribe to a plan to track your usage.</p>
        </div>
      </div>
    );
  }

  const { subscription, subscriptionDetails, usage } = usageData;

  const UsageBar = ({ label, used, limit, canUse }) => {
    const percentage = calculateUsagePercentage(used, limit);
    const colorClass = getUsageColorClass(percentage);

    return (
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          <span className="text-sm text-gray-600">
            {used} / {limit}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${colorClass}`}
            style={{ width: `${percentage}%` }}
          />
        </div>
        <div className="flex justify-between items-center text-xs">
          <span className="text-gray-500">
            {canUse ? 'Available' : 'Limit reached'}
          </span>
          <span className={`font-medium ${percentage >= 90 ? 'text-red-600' : 'text-gray-600'}`}>
            {percentage}% used
          </span>
        </div>
      </div>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysRemaining = () => {
    if (!subscriptionDetails.end_date) return null;
    const endDate = new Date(subscriptionDetails.end_date);
    const today = new Date();
    const diffTime = endDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  const daysRemaining = getDaysRemaining();

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex justify-between items-start mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Usage & Limits</h3>
          <p className="text-gray-600 text-sm">Track your current usage against plan limits</p>
        </div>
        <div className="text-right">
          <p className="text-sm font-medium text-gray-700">{subscription.name}</p>
          <p className="text-xs text-gray-500">
            {subscriptionDetails.status} • {daysRemaining} days remaining
          </p>
        </div>
      </div>

      {/* Usage Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {usage.requests && (
          <div className="bg-gray-50 rounded-lg p-4">
            <UsageBar
              label="Requests"
              used={usage.requests.used}
              limit={usage.requests.limit}
              canUse={usage.requests.canUse}
            />
          </div>
        )}

        {usage.offers && (
          <div className="bg-gray-50 rounded-lg p-4">
            <UsageBar
              label="Offers"
              used={usage.offers.used}
              limit={usage.offers.limit}
              canUse={usage.offers.canUse}
            />
          </div>
        )}

        {usage.orders && (
          <div className="bg-gray-50 rounded-lg p-4">
            <UsageBar
              label="Orders"
              used={usage.orders.used}
              limit={usage.orders.limit}
              canUse={usage.orders.canUse}
            />
          </div>
        )}
      </div>

      {/* Subscription Details */}
      <div className="border-t pt-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <p className="font-medium text-gray-700">Subscription Period</p>
            <p className="text-gray-600 mt-1">
              {formatDate(subscriptionDetails.start_date)} - {formatDate(subscriptionDetails.end_date)}
            </p>
          </div>
          <div>
            <p className="font-medium text-gray-700">Auto Renewal</p>
            <p className="text-gray-600 mt-1">
              {subscriptionDetails.auto_renew ? 'Enabled' : 'Disabled'}
            </p>
          </div>
          <div>
            <p className="font-medium text-gray-700">Status</p>
            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${
              subscriptionDetails.status === 'ACTIVE' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {subscriptionDetails.status}
            </span>
          </div>
        </div>
      </div>

      {/* Warnings */}
      {(usage.requests?.remaining <= 5 || usage.offers?.remaining <= 5 || usage.orders?.remaining <= 5) && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-yellow-800">Usage Warning</h4>
              <p className="text-sm text-yellow-700 mt-1">
                You're approaching your plan limits. Consider upgrading to avoid service interruption.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Expiration Warning */}
      {daysRemaining <= 7 && daysRemaining > 0 && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-red-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-red-800">Subscription Expiring Soon</h4>
              <p className="text-sm text-red-700 mt-1">
                Your subscription expires in {daysRemaining} day{daysRemaining !== 1 ? 's' : ''}. 
                Renew now to continue enjoying premium features.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionUsageCard;
