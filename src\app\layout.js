import { Gei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import ReduxProvider from "../components/ReduxProvider"; // Import ReduxProvider
import ProfileGuard from "../components/ProfileGuard"; // Import ProfileGuard
import { Toaster } from 'react-hot-toast';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Marktzoom ",
  description:
    "Marktzoom is a web application that helps you find the best deals on products in your local market. It uses AI to analyze product prices and provide you with the best deals.",
};

export default function RootLayout({ children }) {
  return (
    <html>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <ReduxProvider>
          <ProfileGuard>
            {children}
          </ProfileGuard>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 5000,
              style: {
                background: '#fff',
                color: '#333',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                borderRadius: '8px',
                padding: '16px',
              },
              success: {
                style: {
                  border: '1px solid #10B981',
                },
                iconTheme: {
                  primary: '#10B981',
                  secondary: '#FFFFFF',
                },
              },
              error: {
                style: {
                  border: '1px solid #EF4444',
                },
                iconTheme: {
                  primary: '#EF4444',
                  secondary: '#FFFFFF',
                },
              },
            }}
          />
        </ReduxProvider>
      </body>
    </html>
  );
}
