"use client";
import Image from "next/image";
import React, { useState, useRef } from "react";

const ProfileFileUploadComponent = () => {
  const [image, setImage] = useState(null);
  const fileInputRef = useRef(null);

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  return (
    <div className="flex items-center gap-4">
      <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
        {image ? (
          <Image
            src={image}
            alt="Profile"
            width={48}
            height={48}
            className="w-12 h-12 object-cover"
          />
        ) : (
          <svg
            width={48}
            height={48}
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clipPath="url(#clip0_437_12577)">
              <rect width={48} height={48} rx={24} fill="#F3F4F6" />
              <path
                d="M48 41.988V48.002H0V42.01C2.7919 38.2789 6.41581 35.2507 10.5836 33.1661C14.7513 31.0815 19.348 29.9981 24.008 30.002C33.816 30.002 42.528 34.71 48 41.988ZM32.004 18C32.004 20.1217 31.1611 22.1566 29.6609 23.6569C28.1606 25.1571 26.1257 26 24.004 26C21.8823 26 19.8474 25.1571 18.3471 23.6569C16.8469 22.1566 16.004 20.1217 16.004 18C16.004 15.8783 16.8469 13.8434 18.3471 12.3431C19.8474 10.8429 21.8823 10 24.004 10C26.1257 10 28.1606 10.8429 29.6609 12.3431C31.1611 13.8434 32.004 15.8783 32.004 18Z"
                fill="#D1D5DB"
              />
            </g>
            <defs>
              <clipPath id="clip0_437_12577">
                <rect width={48} height={48} rx={24} fill="white" />
              </clipPath>
            </defs>
          </svg>
        )}
      </div>

      <button
        onClick={handleButtonClick}
        className="px-4 py-1 border border-gray-300 rounded-md text-[#374151] bg-white inter font-medium text-sm"
      >
        Change
      </button>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />
    </div>
  );
};

export default ProfileFileUploadComponent;
