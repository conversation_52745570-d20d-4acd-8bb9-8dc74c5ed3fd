import { NextResponse } from "next/server";

export function middleware(req) {
    const token = req.cookies.get("accessToken")?.value;
    const pathname = req.nextUrl.pathname;

    const publicPaths = ["/", "/about", "/contact", "/login", "/register", "/forgot-password"];
    const protectedPaths = ["/dashboard", "/profile"];
    const adminPaths = ["/admin"];
    const allowedIncompleteProfilePaths = ["/complete-profile", "/login", "/register", "/forgot-password"];

    // Check if user is not authenticated and trying to access protected routes
    if (!token && protectedPaths.includes(pathname)) {
        return NextResponse.redirect(new URL("/login", req.url));
    }

    // If user is authenticated and trying to access auth pages, redirect to dashboard
    if (token && ["/login", "/register", "/forgot-password"].includes(pathname)) {
        return NextResponse.redirect(new URL("/dashboard", req.url));
    }

    // Check for incomplete profile if user is authenticated
    if (token && !allowedIncompleteProfilePaths.includes(pathname) && !publicPaths.includes(pathname)) {
        const userCookie = req.cookies.get("user")?.value;

        if (userCookie && userCookie !== 'undefined' && userCookie.trim() !== '') {
            try {
                const userData = JSON.parse(decodeURIComponent(userCookie));

                // Only check if we have valid user data
                if (userData && typeof userData === 'object') {
                    // Check if profile is incomplete using is_complete field or fallback to name fields
                    const isIncompleteProfile = userData.is_complete === false ||
                        (!userData.first_name || !userData.last_name);

                    if (isIncompleteProfile) {
                        return NextResponse.redirect(new URL("/complete-profile", req.url));
                    }
                }
            } catch (error) {
                console.error('Error parsing user cookie in middleware:', error);
                // If cookie is corrupted, clear it and redirect to login
                const response = NextResponse.redirect(new URL("/login", req.url));
                response.cookies.delete("user");
                response.cookies.delete("accessToken");
                response.cookies.delete("refreshToken");
                return response;
            }
        }
        // If we have a token but no valid user cookie, let the request proceed
        // The ProfileGuard will handle the redirect on the client side
    }

    const userRole = req.cookies.get("role")?.value;

    if (adminPaths.includes(pathname) && userRole !== "admin") {
        return NextResponse.redirect(new URL("/dashboard", req.url));
    }

    return NextResponse.next();
}

export const config = {
    matcher: [
        "/dashboard",
        "/profile",
        "/admin",
        "/login",
        "/register",
        "/forgot-password",
        "/complete-profile",
        "/(backend)/(.*)"
    ],
};
