// Blog posts table
model blog_posts {
  id                String   @id @default(uuid())
  title             String
  slug              String   @unique
  excerpt           String?
  content           String   @db.Text
  featured_image    String?
  meta_title        String?
  meta_description  String?
  meta_keywords     String?
  status            blog_post_status_enum @default(draft)
  is_featured       Boolean  @default(false)
  published_at      DateTime?
  author_id         String
  category_id       String?
  tags              String?  // JSON array of tags
  view_count        Int      @default(0)
  reading_time      Int?     // in minutes
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt
  is_deleted        Boolean  @default(false)

  // Relations
  author            users @relation(fields: [author_id], references: [id], name: "BlogPostAuthor")
  category          blog_categories? @relation(fields: [category_id], references: [id], name: "BlogPostCategory")
  
  // Child Relations
  comments          blog_comments[]
  attachments       blog_post_attachments[]

  @@index([status])
  @@index([published_at])
  @@index([is_featured])
  @@index([category_id])
  @@index([author_id])
  @@index([slug])
}

// Blog categories table
model blog_categories {
  id          String   @id @default(uuid())
  name        String   @unique
  slug        String   @unique
  description String?
  color       String?  // hex color code
  icon        String?  // icon class or URL
  sort_order  Int      @default(0)
  is_active   Boolean  @default(true)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  blog_posts  blog_posts[] @relation("BlogPostCategory")

  @@index([sort_order])
  @@index([is_active])
}

// Blog comments table
model blog_comments {
  id              String   @id @default(uuid())
  blog_post_id    String
  parent_id       String?  // for nested comments
  author_name     String
  author_email    String
  author_website  String?
  content         String   @db.Text
  status          comment_status_enum @default(pending)
  ip_address      String?
  user_agent      String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  is_deleted      Boolean  @default(false)

  // Relations
  blog_post       blog_posts @relation(fields: [blog_post_id], references: [id], onDelete: Cascade)
  parent          blog_comments? @relation("CommentReplies", fields: [parent_id], references: [id])
  replies         blog_comments[] @relation("CommentReplies")

  @@index([blog_post_id])
  @@index([status])
  @@index([parent_id])
}

// Blog post attachments table
model blog_post_attachments {
  id           String   @id @default(uuid())
  blog_post_id String
  file_path    String
  file_name    String
  file_type    String
  file_size    Int
  alt_text     String?
  caption      String?
  sort_order   Int      @default(0)
  created_at   DateTime @default(now())
  is_deleted   Boolean  @default(false)

  // Relations
  blog_post    blog_posts @relation(fields: [blog_post_id], references: [id], onDelete: Cascade)

  @@index([blog_post_id])
  @@index([sort_order])
}

// Enums
enum blog_post_status_enum {
  draft
  published
  scheduled
  archived
}

enum comment_status_enum {
  pending
  approved
  rejected
  spam
}
