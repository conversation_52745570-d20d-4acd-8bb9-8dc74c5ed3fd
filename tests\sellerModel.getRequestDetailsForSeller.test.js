const { PrismaClient } = require('@prisma/client');
const SellerModel = require('../models/sellerModel');

// Mock Prisma Client
jest.mock('@prisma/client', () => {
  const mockPrisma = {
    request_assigned_sellers: {
      findFirst: jest.fn(),
    },
    requests: {
      findUnique: jest.fn(),
    },
    request_merged_items: {
      findMany: jest.fn(),
    },
    offers: {
      findFirst: jest.fn(),
    },
  };
  
  return {
    PrismaClient: jest.fn(() => mockPrisma),
  };
});

// Mock CodeGeneratorService
jest.mock('../services/codeGeneratorService', () => ({
  addCodeToRequest: jest.fn((request) => ({ ...request, request_code: 'REQ-001' })),
}));

describe('SellerModel.getRequestDetailsForSeller', () => {
  let mockPrisma;
  
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    
    // Get the mocked prisma instance
    mockPrisma = new (require('@prisma/client').PrismaClient)();
  });

  const mockSellerId = 'seller-123';
  const mockRequestId = 'request-456';
  
  const mockAssignment = {
    id: 'assignment-1',
    request_id: mockRequestId,
    seller_id: mockSellerId,
    assigned_at: new Date(),
    status: 'Active',
    notes: 'Test assignment',
    assigner: {
      id: 'admin-1',
      first_name: 'Admin',
      last_name: 'User',
    },
  };

  const mockRequest = {
    id: mockRequestId,
    title: 'Test Request',
    description: 'Test description',
    category: { id: 'cat-1', title: 'Technology' },
    sub_category: { id: 'subcat-1', title: 'Web Development' },
    request_attachments: [
      {
        id: 'att-1',
        file_path: '/uploads/parent-file1.jpg',
        file_type: 'image/jpeg',
        file_size: 1024,
        description: 'Parent attachment 1',
      },
      {
        id: 'att-2',
        file_path: '/uploads/parent-file2.pdf',
        file_type: 'application/pdf',
        file_size: 2048,
        description: 'Parent attachment 2',
      },
    ],
    request_statuses: [
      {
        id: 'status-1',
        status: 'Active',
        created_at: new Date(),
      },
    ],
    assigned_sellers: [
      {
        id: 'assignment-1',
        request_id: mockRequestId,
        assigned_at: new Date(),
        status: 'Active',
        notes: 'Test assignment',
        assigner: {
          id: 'admin-1',
          first_name: 'Admin',
          last_name: 'User',
        },
      },
    ],
  };

  describe('Non-merged request scenario', () => {
    beforeEach(() => {
      // Setup mocks for non-merged request
      mockPrisma.request_assigned_sellers.findFirst.mockResolvedValue(mockAssignment);
      mockPrisma.requests.findUnique.mockResolvedValue(mockRequest);
      mockPrisma.request_merged_items.findMany.mockResolvedValue([]); // No merged items
      mockPrisma.offers.findFirst.mockResolvedValue(null); // No offer
    });

    test('should return request details without merged data', async () => {
      const result = await SellerModel.getRequestDetailsForSeller(mockRequestId, mockSellerId);

      expect(result).toBeDefined();
      expect(result.id).toBe(mockRequestId);
      expect(result.title).toBe('Test Request');
      expect(result.is_merged).toBe(false);
      expect(result.child_requests).toEqual([]);
      expect(result.request_attachments).toHaveLength(2);
      expect(result.request_attachments[0].file_path).toBe('/uploads/parent-file1.jpg');
      expect(result.request_attachments[1].file_path).toBe('/uploads/parent-file2.pdf');
    });

    test('should verify correct database queries for non-merged request', async () => {
      await SellerModel.getRequestDetailsForSeller(mockRequestId, mockSellerId);

      expect(mockPrisma.request_assigned_sellers.findFirst).toHaveBeenCalledWith({
        where: {
          request_id: mockRequestId,
          seller_id: mockSellerId,
        },
        include: {
          assigner: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
            },
          },
        },
      });

      expect(mockPrisma.request_merged_items.findMany).toHaveBeenCalledWith({
        where: { request_id: mockRequestId },
        include: {
          merged_item: {
            select: {
              id: true,
              title: true,
              custom_fields: true,
              request_attachments: {
                where: {
                  is_deleted: false,
                },
              },
            },
          },
        },
      });
    });
  });

  describe('Merged request scenario', () => {
    const mockMergedRequests = [
      {
        id: 'merged-1',
        request_id: mockRequestId,
        merged_item_id: 'child-1',
        merged_item: {
          id: 'child-1',
          title: 'Child Request 1',
          custom_fields: {
            preferred_language: 'JavaScript',
            experience_level: 'Senior',
          },
          request_attachments: [
            {
              id: 'att-3',
              file_path: '/uploads/child1-file1.jpg',
              file_type: 'image/jpeg',
              file_size: 512,
              description: 'Child 1 attachment 1',
            },
            {
              id: 'att-4',
              file_path: '/uploads/child1-file2.doc',
              file_type: 'application/msword',
              file_size: 1536,
              description: 'Child 1 attachment 2',
            },
          ],
        },
      },
      {
        id: 'merged-2',
        request_id: mockRequestId,
        merged_item_id: 'child-2',
        merged_item: {
          id: 'child-2',
          title: 'Child Request 2',
          custom_fields: {
            project_type: 'E-commerce',
            budget_range: '$5000-$10000',
          },
          request_attachments: [
            {
              id: 'att-5',
              file_path: '/uploads/child2-file1.png',
              file_type: 'image/png',
              file_size: 768,
              description: 'Child 2 attachment 1',
            },
          ],
        },
      },
    ];

    beforeEach(() => {
      // Setup mocks for merged request
      mockPrisma.request_assigned_sellers.findFirst.mockResolvedValue(mockAssignment);
      mockPrisma.requests.findUnique.mockResolvedValue(mockRequest);
      mockPrisma.request_merged_items.findMany.mockResolvedValue(mockMergedRequests);
      mockPrisma.offers.findFirst.mockResolvedValue(null); // No offer
    });

    test('should return merged request with combined attachments and child details', async () => {
      const result = await SellerModel.getRequestDetailsForSeller(mockRequestId, mockSellerId);

      expect(result).toBeDefined();
      expect(result.id).toBe(mockRequestId);
      expect(result.title).toBe('Test Request');
      expect(result.is_merged).toBe(true);
      
      // Check child requests
      expect(result.child_requests).toHaveLength(2);
      expect(result.child_requests[0]).toEqual({
        id: 'child-1',
        title: 'Child Request 1',
        form_fields: {
          preferred_language: 'JavaScript',
          experience_level: 'Senior',
        },
      });
      expect(result.child_requests[1]).toEqual({
        id: 'child-2',
        title: 'Child Request 2',
        form_fields: {
          project_type: 'E-commerce',
          budget_range: '$5000-$10000',
        },
      });

      // Check combined attachments (parent + children)
      expect(result.request_attachments).toHaveLength(5); // 2 parent + 2 child1 + 1 child2
      
      // Parent attachments should be first
      expect(result.request_attachments[0].file_path).toBe('/uploads/parent-file1.jpg');
      expect(result.request_attachments[1].file_path).toBe('/uploads/parent-file2.pdf');
      
      // Child attachments should follow
      expect(result.request_attachments[2].file_path).toBe('/uploads/child1-file1.jpg');
      expect(result.request_attachments[3].file_path).toBe('/uploads/child1-file2.doc');
      expect(result.request_attachments[4].file_path).toBe('/uploads/child2-file1.png');
    });

    test('should handle child requests with null custom_fields', async () => {
      // Modify mock to have null custom_fields
      const modifiedMergedRequests = [...mockMergedRequests];
      modifiedMergedRequests[0].merged_item.custom_fields = null;
      
      mockPrisma.request_merged_items.findMany.mockResolvedValue(modifiedMergedRequests);

      const result = await SellerModel.getRequestDetailsForSeller(mockRequestId, mockSellerId);

      expect(result.child_requests[0].form_fields).toEqual({});
      expect(result.child_requests[1].form_fields).toEqual({
        project_type: 'E-commerce',
        budget_range: '$5000-$10000',
      });
    });

    test('should handle child requests with no attachments', async () => {
      // Modify mock to have no attachments for one child
      const modifiedMergedRequests = [...mockMergedRequests];
      modifiedMergedRequests[1].merged_item.request_attachments = [];
      
      mockPrisma.request_merged_items.findMany.mockResolvedValue(modifiedMergedRequests);

      const result = await SellerModel.getRequestDetailsForSeller(mockRequestId, mockSellerId);

      // Should have parent (2) + child1 (2) + child2 (0) = 4 attachments
      expect(result.request_attachments).toHaveLength(4);
    });
  });

  describe('Error scenarios', () => {
    test('should throw error when seller is not assigned to request', async () => {
      mockPrisma.request_assigned_sellers.findFirst.mockResolvedValue(null);

      await expect(
        SellerModel.getRequestDetailsForSeller(mockRequestId, mockSellerId)
      ).rejects.toThrow('You are not assigned to this request');
    });

    test('should throw error when request is not found', async () => {
      mockPrisma.request_assigned_sellers.findFirst.mockResolvedValue(mockAssignment);
      mockPrisma.requests.findUnique.mockResolvedValue(null);

      await expect(
        SellerModel.getRequestDetailsForSeller(mockRequestId, mockSellerId)
      ).rejects.toThrow('Request not found');
    });
  });
});
