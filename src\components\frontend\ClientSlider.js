"use client";
import React, { useState } from "react";
import Image from "next/image";

const ClientSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(2); // Start with the 3rd item as active (index 2)
  const [animation, setAnimation] = useState("");

  const testimonials = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      designation: "Founder & CEO, USA",
      quote:
        "Incredible eCommerce software! I integrated many stores, increased efficiency, and drove more sales with 6amMart. The script was easy to understand!",
      image:
        "https://6ammart.app/wp-content/uploads/2023/11/6amMart-josey-marques-review.webp",
    },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      designation: "Businessman, Istanbul",
      quote:
        "It was my dream to build the biggest eCommerce platform in my country. I was confused if 6amMart was right for me. Thanks to the support team for their constant dedication, I am able to make my dream come true.",
      image:
        "https://6ammart.app/wp-content/uploads/2023/11/6am<PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-review.webp",
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON><PERSON>",
      designation: "Freelance Developer, India",
      quote:
        "6amMart is the perfect solution that I've been looking for my client. As a software developer myself, I must admit, the scripts are next-level!",
      image:
        "https://6ammart.app/wp-content/uploads/2023/11/6amMart-jagdish-nandi-review.webp",
    },
    {
      id: 4,
      name: "Fyodor Slovanski",
      designation: "Entrepreneur, Poland",
      quote:
        "6amMart is packed with amazing features. But I wanted some new features to match my business idea. The 6amMart team came forward to successfully deliver my requested customization on time.",
      image:
        "https://6ammart.app/wp-content/uploads/2023/11/6amMart-fyodor-slovanski-review.webp",
    },
  ];

  const handleNext = () => {
    setAnimation("next");
    setTimeout(() => {
      setCurrentSlide((prev) =>
        prev === testimonials.length - 1 ? 0 : prev + 1
      );
      setAnimation("");
    }, 300);
  };

  const handlePrev = () => {
    setAnimation("prev");
    setTimeout(() => {
      setCurrentSlide((prev) =>
        prev === 0 ? testimonials.length - 1 : prev - 1
      );
      setAnimation("");
    }, 300);
  };

  const getSlideClass = (index) => {
    if (index === currentSlide) return "active";
    if (index === (currentSlide + 1) % testimonials.length) return "secondary";
    return "";
  };

  return (
    <>
      <section className="my-10 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center pb-10">
            <h3 className="archivo text-[32px] font-bold text-[#0F0C1D]">
              Testimonials of our
              <span className="ml-2 text-transparent bg-clip-text bg-linear-to-r from-[#FD2692]   to-[#0A67F2]">
                Customers
              </span>
            </h3>
            <p className="text-[#656B76] archivo font-normal text-base max-w-xl mx-auto">
              Real Stories from Satisfied Customers Who Found Exactly What They
              Needed
            </p>
          </div>
          <div className="testimonials-slider-wrapper">
            <Image
              src="https://6ammart.app/wp-content/uploads/2023/11/6amMart-testimonial-shapes.svg"
              width={500}
              height={300}
              className="bg-img"
              alt="Testimonial shapes"
              unoptimized
            />
            <div className="title-area text-center text-md-start">
              <span className="title-badge archivo">Testimonial</span>
              <h2 className="title archivo text-[32px]  font-medium">
                Our Clients{" "}
                <span className="color-animation animate archivo font-bold">
                  Love Us
                </span>
              </h2>
              <p className="archivo text-sm text-[#656B76]">
                Each listing is designed to be clear and concise, providing
                customers
              </p>
              <div className="slider-nav-btns d-none d-md-flex">
                <button className="prev cursor-pointer" onClick={handlePrev}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="size-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
                    />
                  </svg>
                </button>
                <button
                  className="next active cursor-pointer"
                  onClick={handleNext}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="size-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div className="slider-area">
              <div className={`slider ${animation}`}>
                {testimonials.map((testimonial, index) => (
                  <div
                    key={testimonial.id}
                    className={`testimonial-slide-item ${getSlideClass(index)}`}
                  >
                    <div className="thumb">
                      <Image
                        width={144}
                        height={144}
                        src={testimonial.image}
                        className="attachment-full size-full"
                        alt={`${testimonial.name}-review`}
                        unoptimized
                      />
                    </div>
                    <div className="content archivo">
                      <blockquote className="text-black text-base mb-5 font-medium">
                        {testimonial.quote}
                      </blockquote>
                      <p className="text-lg font-semibold">
                        {testimonial.name}
                      </p>
                      <span className="designation text-sm font-normal text-[#656B76]">
                        {testimonial.designation}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ClientSlider;
