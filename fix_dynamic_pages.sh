#!/bin/bash

# First, remove any existing dynamic exports
find src/app -name "*.js" -type f | while read -r file; do
  if grep -q "export const dynamic = \"force-dynamic\"" "$file"; then
    echo "Removing existing dynamic export from $file"
    sed -i '/export const dynamic = "force-dynamic";/d' "$file"
  fi
done

# Find all JavaScript files in the src/app directory
find src/app -name "*.js" -type f | while read -r file; do
  # Check if the file contains useSearchParams or Pagination
  if grep -q "useSearchParams" "$file" || grep -q "import Pagination" "$file"; then
    echo "Adding dynamic export to $file"

    # Add the dynamic export after the "use client" directive
    sed -i '/"use client";/a export const dynamic = "force-dynamic";' "$file"
  fi
done

echo "Done fixing files"
