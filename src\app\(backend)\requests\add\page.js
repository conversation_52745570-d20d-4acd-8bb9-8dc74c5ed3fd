"use client";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import ImageUpload from "@/components/backend/ImageUpload";
import SelectInput from "@/components/backend/SelectInput";
import TextInput from "@/components/backend/TextInput";
import Dynamic<PERSON><PERSON><PERSON>ield from "@/components/backend/DynamicFormField";

import React, { useState, useEffect } from "react";
import { useFetchApiQuery, useMutateApiMutation } from "@/redux/services/api";
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import DatePicker from "react-datepicker";
import { format, isValid, parseISO } from "date-fns";
import "react-datepicker/dist/react-datepicker.css";
import "@/styles/datepicker.css";
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import { handleUnauthorized } from '@/utils/auth';

// Validation schema
const validationSchema = Yup.object({
  category_id: Yup.string().required('Category is required'),
  sub_category_id: Yup.string().when(['category_id'], {
    is: (category_id) => category_id && category_id.length > 0,
    then: () => Yup.string().required('Sub-category is required'),
    otherwise: () => Yup.string().notRequired(),
  }),
  title: Yup.string().required('Title is required'),
  short_description: Yup.string(), // Added validation for short_description
  description: Yup.string(),
  quantity: Yup.number()
    .typeError('Quantity must be a number')
    .min(1, 'Quantity must be at least 1')
    .required('Quantity is required'),
  budget_min: Yup.number()
    .typeError('Minimum budget must be a number')
    .min(0, 'Minimum budget must be at least 0')
    .required('Minimum budget is required'),
  budget_max: Yup.number()
    .typeError('Maximum budget must be a number')
    .min(0, 'Maximum budget must be at least 0')
    .test('is-greater', 'Maximum budget must be greater than or equal to minimum budget',
      function(value) {
        const { budget_min } = this.parent;
        return !value || !budget_min || parseFloat(value) >= parseFloat(budget_min);
      })
    .required('Maximum budget is required'),
  deadline: Yup.date()
    .required('Please select a deadline')
    .test('is-future', 'Deadline must be in the future', function(value) {
      if (!value) return true; // Allow null/undefined
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return new Date(value) >= today;
    }),
  urgency: Yup.string().required('Urgency level is required'),
  location: Yup.string(),
  additional_info: Yup.string(),
  remarks: Yup.string().nullable(),
  image: Yup.mixed().required('At least one image is required'),
  custom_fields: Yup.object(), // Will be dynamically validated based on form fields
});

const Add = () => {
  const router = useRouter();
  const [subCategories, setSubCategories] = useState([]);
  const [fileFileName, setFileFileName] = useState("");
  const [serverError, setServerError] = useState("");

  // Clear server error when form values change
  const [prevFormValues, setPrevFormValues] = useState(null);

  // Initial form values
  const initialValues = {
    category_id: "",
    sub_category_id: "",
    title: "",
    short_description: "", // Added missing field
    description: "",
    quantity: "",
    budget_min: "",
    budget_max: "",
    deadline: "",
    urgency: "Normal",
    status: "Pending",
    request_type: "General",
    location: "",
    additional_info: "",
    remarks: "",
    image: null,
    file: null,
    custom_fields: {} // For dynamic form fields from subcategory
  };

  const categories = useFetchApiQuery({
    endpoint: "/buyer/categories",
    skip: false, // Ensure the query always runs with a valid endpoint
  });

  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState(null);
  const [formFields, setFormFields] = useState([]);

  const subCategoryDetails = useFetchApiQuery({
    endpoint: selectedSubCategoryId ? `/buyer/subcategories/${selectedSubCategoryId}` : null,
    skip: !selectedSubCategoryId, // Skip the query if no subcategory is selected
  });

  // Create dynamic validation schema for form fields
  const [dynamicValidationSchema, setDynamicValidationSchema] = useState(validationSchema);

  // Handle category change and update subcategories
  const handleCategoryChange = (categoryId, setFieldValue) => {
    setFieldValue('category_id', categoryId);
    setFieldValue('sub_category_id', ''); // Reset subcategory when category changes
    setSelectedSubCategoryId(null); // Reset selected subcategory

    if (categoryId) {
      const category = categories?.data?.data.find(cat => cat.id === categoryId);
      if (category && category.sub_categories) {
        setSubCategories(category.sub_categories);
      } else {
        setSubCategories([]);
      }
    } else {
      setSubCategories([]);
    }
  };

  // Clear server error when form values change
  useEffect(() => {
    if (prevFormValues !== null && serverError) {
      setServerError("");
    }
  }, [prevFormValues, serverError]);

  // Update form fields when subcategory details are loaded
  useEffect(() => {
    if (subCategoryDetails.data?.data?.form_fields) {
      const fields = subCategoryDetails.data.data.form_fields;
      setFormFields(fields);

      // Create dynamic validation schema for custom fields
      const customFieldsSchema = {};

      fields.forEach(field => {
        const fieldName = field.label_name.toLowerCase().replace(/\s+/g, '_');

        if (field.is_required) {
          // Different validation based on input type
          switch (field.input_type) {
            case 'NUMBER':
              customFieldsSchema[fieldName] = Yup.number()
                .typeError(`${field.label_name} must be a number`)
                .required(`${field.label_name} is required`);
              break;
            case 'EMAIL':
              customFieldsSchema[fieldName] = Yup.string()
                .email(`Please enter a valid email address`)
                .required(`${field.label_name} is required`);
              break;
            case 'FILE':
              customFieldsSchema[fieldName] = Yup.mixed()
                .required(`${field.label_name} is required`);
              break;
            case 'DATE':
              customFieldsSchema[fieldName] = Yup.date()
                .typeError(`${field.label_name} must be a valid date`)
                .required(`${field.label_name} is required`);
              break;
            case 'TIME':
              customFieldsSchema[fieldName] = Yup.string()
                .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, `${field.label_name} must be in HH:MM format`)
                .required(`${field.label_name} is required`);
              break;
            case 'DATETIME':
              customFieldsSchema[fieldName] = Yup.date()
                .typeError(`${field.label_name} must be a valid date and time`)
                .required(`${field.label_name} is required`);
              break;
            case 'URL':
              customFieldsSchema[fieldName] = Yup.string()
                .url(`Please enter a valid URL`)
                .required(`${field.label_name} is required`);
              break;
            case 'PHONE':
              customFieldsSchema[fieldName] = Yup.string()
                .matches(/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,
                  `Please enter a valid phone number`)
                .required(`${field.label_name} is required`);
              break;
            case 'CHECKBOX':
              customFieldsSchema[fieldName] = Yup.boolean()
                .oneOf([true], `${field.label_name} must be checked`)
                .required(`${field.label_name} is required`);
              break;
            default:
              customFieldsSchema[fieldName] = Yup.string()
                .required(`${field.label_name} is required`);
          }
        } else {
          // Optional fields
          switch (field.input_type) {
            case 'NUMBER':
              customFieldsSchema[fieldName] = Yup.number()
                .typeError(`${field.label_name} must be a number`)
                .nullable();
              break;
            case 'EMAIL':
              customFieldsSchema[fieldName] = Yup.string()
                .email(`Please enter a valid email address`)
                .nullable();
              break;
            case 'DATE':
              customFieldsSchema[fieldName] = Yup.date()
                .typeError(`${field.label_name} must be a valid date`)
                .nullable();
              break;
            case 'TIME':
              customFieldsSchema[fieldName] = Yup.string()
                .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, `${field.label_name} must be in HH:MM format`)
                .nullable();
              break;
            case 'DATETIME':
              customFieldsSchema[fieldName] = Yup.date()
                .typeError(`${field.label_name} must be a valid date and time`)
                .nullable();
              break;
            case 'URL':
              customFieldsSchema[fieldName] = Yup.string()
                .url(`Please enter a valid URL`)
                .nullable();
              break;
            case 'PHONE':
              customFieldsSchema[fieldName] = Yup.string()
                .matches(/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,
                  `Please enter a valid phone number`)
                .nullable();
              break;
            default:
              customFieldsSchema[fieldName] = Yup.string().nullable();
          }
        }
      });

      // Update the validation schema with custom fields
      setDynamicValidationSchema(
        validationSchema.shape({
          custom_fields: Yup.object().shape(customFieldsSchema)
        })
      );
    } else {
      setFormFields([]);
      setDynamicValidationSchema(validationSchema);
    }
  }, [subCategoryDetails.data]);



  // Handle 403 errors in API queries
  useEffect(() => {
    if (categories.error?.status === 403 || subCategoryDetails.error?.status === 403) {
      const error = categories.error || subCategoryDetails.error;
      handleUnauthorized(error, router);
    }
  }, [categories.error, subCategoryDetails.error, router]);

  const [mutateApi] = useMutateApiMutation();

  const formatDate = (date) => {
    if (!date) return '';
    try {
      // Handle both Date objects and ISO strings
      const dateObj = date instanceof Date ? date : parseISO(date);
      if (!isValid(dateObj)) return '';
      return format(dateObj, 'yyyy-MM-dd');
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleDateChange = (date, setFieldValue, fieldName = 'deadline') => {
    // Format the date as YYYY-MM-DD for the backend
    setFieldValue(fieldName, date ? date.toISOString().split('T')[0] : null);
  };

  const handleImageChange = (e, setFieldValue) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setFieldValue('image', Array.from(files));
    } else {
      setFieldValue('image', null);
    }
  };

  const handleFileChange = (e, setFieldValue) => {
    const file = e.target.files[0];
    if (file) {
      setFileFileName(file.name);
      setFieldValue('file', file);
    } else {
      setFileFileName("");
      setFieldValue('file', null);
    }
  };



  const handleCloseFileClick = (setFieldValue) => {
    setFileFileName("");
    setFieldValue('file', null);
  };

  const handleFormSubmit = (values, { setSubmitting, resetForm }) => {
    // Clear any previous server errors
    setServerError("");

    const formPayload = new FormData();

    // Append all form fields to FormData
    for (const key in values) {
      if (values[key] !== null && values[key] !== undefined) {
        if (key === 'image' && values[key].length > 0) {
          values[key].forEach(image => formPayload.append(key, image));
        } else if (key === 'file') {
          if (values[key]) {
            formPayload.append(key, values[key]);
          }
        } else if (key === 'custom_fields') {
          // Handle custom fields
          for (const fieldName in values.custom_fields) {
            const fieldValue = values.custom_fields[fieldName];

            if (fieldValue !== null && fieldValue !== undefined) {
              // Check if it's a file
              if (fieldValue instanceof File) {
                formPayload.append(`custom_fields[${fieldName}]`, fieldValue);
              } else {
                formPayload.append(`custom_fields[${fieldName}]`, fieldValue);
              }
            }
          }
        } else {
          formPayload.append(key, values[key]);
        }
      }
    }

    // Show loading toast
    const loadingToast = toast.loading('Creating your request...');

    mutateApi({
      endpoint: "/buyer/requests",
      data: formPayload,
    }).then((response) => {

      console.log(response);
      if (response?.data?.success) {
        toast.dismiss(loadingToast);

        // Show success toast
        toast.success('Request created successfully!');

        // Only reset form and redirect on success
        resetForm();
        setFileFileName("");

        router.push('/requests/pending');
      } else {
        throw new 'Something went wrong';
      }
    }).catch((error) => {
      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Handle 403 Forbidden error with utility function
      if (!handleUnauthorized(error, router)) {
        // Set server error to display at the top of the form
        const errorMessage = error?.data?.message || 'Failed to create request. Please try again.';
        setServerError(errorMessage);

        // Show error toast
        toast.error(errorMessage);

        // Don't reset the form on error
        setSubmitting(false);
      }
    });
  };

  const categoryOptions = categories?.data?.data?.map(category => ({
    value: category.id,
    label: category.title
  })) || [];

  const currencyOptions = [
    { value: "USD", label: "USD" },
    { value: "EUR", label: "EUR" },
    { value: "GBP", label: "GBP" }
  ];

  const urgencyOptions = [
    { value: "Low", label: "Low" },
    { value: "Normal", label: "Normal" },
    { value: "High", label: "High" },
    { value: "Urgent", label: "Urgent" }
  ];

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold text-[#343A40] text-xl poppins">
          Add New Request{" "}
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4 pb-5 border border-[#CACACA] rounded-2xl">
          <h3 className="font-semibold text-[#343A40] text-[32px] leading-normal archivo">
            Buyer Request Submission
          </h3>
          <Formik
            initialValues={initialValues}
            validationSchema={dynamicValidationSchema}
            enableReinitialize={true}
            onSubmit={handleFormSubmit}
          >
            {({ values, setFieldValue, isSubmitting, errors, touched }) => {
              // Get all error fields for summary
              const errorFields = Object.keys(errors).filter(key => touched[key]);

              // Use useEffect to track form values changes outside of render
              useEffect(() => {
                setPrevFormValues(values);
              }, [values]);

              const subCategoryOptions = subCategories.map(subCat => ({
                value: subCat.id,
                label: subCat.title
              }));

              return (
                <Form>
                  {/* Server Error Message */}
                  {serverError && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <h4 className="text-red-700 font-medium">{serverError}</h4>
                      </div>
                    </div>
                  )}

                  {/* Validation Error Summary */}
                  {errorFields.length > 0 && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <h4 className="text-red-700 font-medium">Please fix the following errors:</h4>
                      </div>
                      <ul className="list-disc pl-5 text-red-600">
                        {errorFields.map(field => (
                          <li key={field} className="text-sm">
                            {errors[field]}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    <div className="">
                      <SelectInput
                        label="Select Category"
                        options={categoryOptions}
                        value={values.category_id}
                        onChange={(e) => handleCategoryChange(e.target.value, setFieldValue)}
                        placeholder="Select Category"
                        className="w-full h-11 !font-light"
                        name="category_id"
                        error={touched.category_id && errors.category_id}
                        required
                      />
                    </div>
                    <div className="">
                      <SelectInput
                        label="Select Sub Category"
                        options={subCategoryOptions}
                        value={values.sub_category_id}
                        onChange={(e) => {
                          const value = e.target.value;
                          setFieldValue('sub_category_id', value);
                          setSelectedSubCategoryId(value || null);
                        }}
                        placeholder="Select Sub Category"
                        className="w-full h-11 !font-light"
                        name="sub_category_id"
                        disabled={!values.category_id}
                        error={touched.sub_category_id && errors.sub_category_id}
                        required
                      />
                    </div>
                    <div className="">
                      <TextInput
                        className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                        label="Request Title"
                        placeholder="Write request title here"
                        name="title"
                        value={values.title}
                        onChange={(e) => setFieldValue('title', e.target.value)}
                        error={touched.title && errors.title}
                        required
                      />
                    </div>
                    <div className="">
                      <TextInput
                        className="w-full h-11 !placeholder:text-[#6B7280] !font-light"
                        label="Short descriptions"
                        placeholder="Write short description here"
                        name="short_description"
                        value={values.short_description}
                        onChange={(e) => setFieldValue('short_description', e.target.value)}
                        error={touched.short_description && errors.short_description}                      />
                    </div>
                    <div className="">
                      <div>
                        <ImageUpload
                          label="Attach Image"
                          required={true}
                          handleChange={(e) => handleImageChange(e, setFieldValue)}
                          maxImages={15}
                          className="mb-2"
                        />
                        {touched.image && errors.image && (
                          <div className="text-red-500 text-sm mt-1">{errors.image}</div>
                        )}
                      </div>
                    </div>
                    <div className="">
                      <TextInput
                        className="w-full !placeholder:text-[#6B7280] !font-light"
                        label="Detailed Description"
                        placeholder="Write detailed description here"
                        name="description"
                        value={values.description}
                        onChange={(e) => setFieldValue('description', e.target.value)}
                        multiline={true}
                        row="5"
                        error={touched.description && errors.description}
                      />
                    </div>
                    <div className="">
                      <TextInput
                        className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
                        label="Quantity (Unit)"
                        placeholder="Enter your Quantity. Ex: Unit"
                        name="quantity"
                        value={values.quantity}
                        onChange={(e) => setFieldValue('quantity', e.target.value)}
                        error={touched.quantity && errors.quantity}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="">
                        <p className="text-sm archivo font-medium mb-2 text-[#374151] inline-block">
                          Minimum Budget <span className="text-red-500"> *</span>
                        </p>
                        <div className={`flex w-full border border-[#D1D5DB] rounded-lg ${touched.budget_min && errors.budget_min ? 'border-red-500' : ''}`}>
                          <TextInput
                            wrapperClassName="w-full"
                            className={`w-full !border-0 h-11 !placeholder:text-[#6B7280] !font-light }`}
                            placeholder="$ 0.00"
                            name="budget_min"
                            value={values.budget_min}
                            onChange={(e) => setFieldValue('budget_min', e.target.value)}
                            error={null}

                          />
                          <SelectInput
                            options={currencyOptions}
                            value="USD"
                            onChange={() => {}}
                            placeholder="USD"
                            className="w-24 h-11 !border-0 !placeholder:text-[#6B7280] new-custom-select !font-light"
                          />
                        </div>
                        {touched.budget_min && errors.budget_min && (
                          <div className="text-red-500 text-sm mt-1">{errors.budget_min}</div>
                        )}
                      </div>
                      <div className="">
                        <p className="text-sm archivo font-medium mb-2 text-[#374151] inline-block">
                          Maximum Budget <span className="text-red-500"> *</span>
                        </p>
                        <div className={`flex w-full border border-[#D1D5DB] rounded-lg ${touched.budget_max && errors.budget_max ? 'border-red-500' : ''}`}>
                          <TextInput
                            wrapperClassName="w-full"
                            className={`w-full !border-0 h-11 !placeholder:text-[#6B7280] !font-light ${touched.budget_max && errors.budget_max ? 'border-red-500' : ''}`}
                            placeholder="$ 0.00"
                            name="budget_max"
                            value={values.budget_max}
                            onChange={(e) => setFieldValue('budget_max', e.target.value)}
                            error={null}

                          />
                          <SelectInput
                            options={currencyOptions}
                            value="USD"
                            onChange={() => {}}
                            placeholder="USD"
                            className="w-24 h-11 !border-0 !placeholder:text-[#6B7280] new-custom-select !font-light"
                          />
                        </div>
                        {touched.budget_max && errors.budget_max && (
                          <div className="text-red-500 text-sm mt-1">{errors.budget_max}</div>
                        )}
                      </div>
                    </div>
                    <div className="">
                      <TextInput
                        className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
                        label="Additional Criteria"
                        placeholder="Ex: brands or others."
                        name="additional_info"
                        value={values.additional_info}
                        onChange={(e) => setFieldValue('additional_info', e.target.value)}
                        error={touched.additional_info && errors.additional_info}
                      />
                    </div>
                    <div className="datepicker">
                      <label className="text-sm archivo font-medium  text-[#374151] inline-block">
                        Deadline <span className="text-red-500"> *</span>
                      </label>

                      <div className={`${touched.deadline && errors.deadline ? 'border-red-500' : 'border-[#D1D5DB]'} rounded-lg`}>
                        <DatePicker
                          selected={values.deadline ? new Date(values.deadline) : null}
                          onChange={(date) => handleDateChange(date, setFieldValue)}
                          dateFormat="yyyy-MM-dd"
                          minDate={new Date()}
                          placeholderText="Select your deadline"
                          className="w-full h-11 py-2 border-0 text-[#374151] text-sm rounded-lg focus:outline-none"
                          wrapperClassName="w-full"
                          showMonthDropdown
                          showYearDropdown
                          dropdownMode="select"
                          isClearable={true}
                          disabledKeyboardNavigation
                          strictParsing
                          preventOpenOnFocus
                          customInput={
                            <div className="relative w-full">
                              <div className={`flex items-center border inter font-medium ${touched.deadline && errors.deadline ? 'border-red-500' : 'border-inherit'} text-[#374151] text-sm rounded-lg outline-0 block p-2.5 h-11`}>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  strokeWidth={1.5}
                                  stroke="currentColor"
                                  className="size-5 text-[#6B7280] mr-2"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"
                                  />
                                </svg>
                                <div className="w-full">
                                  {values.deadline ? (
                                    <span className="text-[#374151] leading-[1.7] text-sm inter font-normal">
                                      {formatDate(values.deadline)}
                                    </span>
                                  ) : (
                                    <span className="text-[#6B7280] leading-[1.7] text-sm inter font-light">
                                      Select your deadline
                                    </span>
                                  )}
                                </div>
                              </div>
                              <input
                                type="text"
                                className="absolute inset-0 opacity-0 cursor-pointer z-10 w-full"
                              />
                            </div>
                          }
                        />
                      </div>

                      {touched.deadline && errors.deadline && (
                        <div className="text-red-500 text-sm mt-2">{errors.deadline}</div>
                      )}
                    </div>
                    <div className="">
                      <SelectInput
                        label="Urgency Level"
                        options={urgencyOptions}
                        value={values.urgency}
                        onChange={(e) => setFieldValue('urgency', e.target.value)}
                        placeholder="Select Urgency"
                        className="w-full h-11 !font-light"
                        name="urgency"
                        error={touched.urgency && errors.urgency}
                      />
                    </div>
                    <div className="">
                      <p className="text-sm archivo font-medium mb-2 text-[#374151] inline-block">
                        File Attachment
                      </p>
                      <div className="flex items-center space-x-4 h-11 w-full border inter font-medium border-[#D1D5DB] text-[#374151] text-sm rounded-lg relative outline-0 overflow-hidden">
                        <input
                          type="file"
                          id="file-input"
                          className="hidden"
                          onChange={(e) => handleFileChange(e, setFieldValue)}
                        />

                        <label
                          htmlFor="file-input"
                          className="flex items-center justify-center p-2 bg-[#F3F3F3] w-20 font-semibold cursor-pointer transition-colors h-full"
                        >
                          <svg
                            width={10}
                            height={20}
                            viewBox="0 0 10 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M5 0.625C6.15994 0.626242 7.27202 1.08757 8.09222 1.90778C8.91243 2.72798 9.37376 3.84006 9.375 5V16.25C9.375 17.0788 9.04576 17.8737 8.45971 18.4597C7.87366 19.0458 7.0788 19.375 6.25 19.375C5.4212 19.375 4.62634 19.0458 4.04029 18.4597C3.45424 17.8737 3.125 17.0788 3.125 16.25V5C3.125 4.50272 3.32254 4.02581 3.67417 3.67417C4.02581 3.32254 4.50272 3.125 5 3.125C5.49728 3.125 5.97419 3.32254 6.32583 3.67417C6.67746 4.02581 6.875 4.50272 6.875 5V13.75C6.875 13.9158 6.80915 14.0747 6.69194 14.1919C6.57473 14.3092 6.41576 14.375 6.25 14.375C6.08424 14.375 5.92527 14.3092 5.80806 14.1919C5.69085 14.0747 5.625 13.9158 5.625 13.75V5C5.625 4.83424 5.55915 4.67527 5.44194 4.55806C5.32473 4.44085 5.16576 4.375 5 4.375C4.83424 4.375 4.67527 4.44085 4.55806 4.55806C4.44085 4.67527 4.375 4.83424 4.375 5V16.25C4.375 16.7473 4.57254 17.2242 4.92417 17.5758C5.27581 17.9275 5.75272 18.125 6.25 18.125C6.74728 18.125 7.22419 17.9275 7.57583 17.5758C7.92746 17.2242 8.125 16.7473 8.125 16.25V5C8.125 4.1712 7.79576 3.37634 7.20971 2.79029C6.62366 2.20424 5.8288 1.875 5 1.875C4.1712 1.875 3.37634 2.20424 2.79029 2.79029C2.20424 3.37634 1.875 4.1712 1.875 5V13.75C1.875 13.9158 1.80915 14.0747 1.69194 14.1919C1.57473 14.3092 1.41576 14.375 1.25 14.375C1.08424 14.375 0.925269 14.3092 0.808058 14.1919C0.690848 14.0747 0.625 13.9158 0.625 13.75V5C0.626241 3.84006 1.08758 2.72798 1.90778 1.90778C2.72798 1.08757 3.84006 0.626242 5 0.625Z"
                              fill="#6B7280"
                            />
                          </svg>
                        </label>

                        <div className="w-full">
                          {fileFileName ? (
                            <span className="text-[#6B7280] leading-[1.7] text-sm inter font-normal">
                              {fileFileName}
                            </span>
                          ) : (
                            <span className="text-[#6B7280] leading-[1.7] text-sm inter font-light">
                              Ex: Pdf, Doc,Text.
                            </span>
                          )}
                        </div>
                        {fileFileName && (
                          <button
                            type="button"
                            onClick={() => handleCloseFileClick(setFieldValue)}
                            className="text-[#6B7280] cursor-pointer absolute top-1/2 -translate-y-1/2 right-2 transition-colors"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                              strokeWidth={1.5}
                              stroke="currentColor"
                              className="w-5 h-5"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                   {/* <div className="">
                      <TextInput
                        className="w-full h-11 !font-light !placeholder:text-[#6B7280]"
                        label="Remarks"
                        placeholder="Enter your remarks"
                        name="remarks"
                        value={values.remarks}
                        onChange={(e) => setFieldValue('remarks', e.target.value)}
                        error={touched.remarks && errors.remarks}
                      />
                    </div>
                    */}

                    {/* Dynamic Form Fields from Subcategory */}
                    {formFields.length > 0 && (
                      <div className="col-span-2 mt-4">
                        <h4 className="font-semibold text-[#343A40] text-lg mb-3">
                          Additional Information for {subCategoryDetails.data?.data?.title || 'Selected Subcategory'}
                        </h4>
                        <div className="grid grid-cols-2 gap-4">
                          {formFields.map((field, index) => {
                            const fieldName = field.label_name.toLowerCase().replace(/\s+/g, '_');
                            const fieldError =
                              touched.custom_fields?.[fieldName] &&
                              errors.custom_fields?.[fieldName];

                            // Ensure custom_fields object exists
                            const handleChange = (value) => {
                              if (!values.custom_fields) {
                                setFieldValue('custom_fields', {});
                              }
                              setFieldValue(`custom_fields.${fieldName}`, value);
                            };

                            // Determine column span based on field type
                            const colSpan = field.input_type === 'TEXTAREA' ? 'col-span-2' : '';

                            return (
                              <div key={index} className={colSpan}>
                                <DynamicFormField
                                  field={field}
                                  fieldName={fieldName}
                                  value={values.custom_fields?.[fieldName]}
                                  onChange={handleChange}
                                  error={fieldError}
                                  setFieldValue={setFieldValue}
                                />
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="mt-10 flex items-center space-x-4 justify-end">
                    <button
                      type="button"
                      className="py-2.5 px-5 text-sm font-medium text-[#374151] focus:outline-none bg-white rounded-lg border border-[#D1D5DB] inter"
                      onClick={() => router.push('/requests')}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="text-white bg-[#4F46E5] inter font-medium rounded-lg text-sm px-5 py-2.5 disabled:opacity-50"
                    >
                      {isSubmitting ? 'Submitting...' : 'Submit'}
                    </button>
                  </div>
                </Form>
              );
            }}
          </Formik>
        </div>
      </div>
    </>
  );
};

export default Add;