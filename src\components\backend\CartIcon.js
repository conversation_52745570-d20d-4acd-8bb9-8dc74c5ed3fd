"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useFetchApiQuery } from '@/redux/services/api';

const CartIcon = () => {
  const [cartItemCount, setCartItemCount] = useState(0);

  // Fetch cart data
  const { data: cartData, isLoading, error } = useFetchApiQuery({
    endpoint: "/buyer/cart",
    skip: false,
  });

  useEffect(() => {
    if (cartData?.data?.cart_items) {
      setCartItemCount(cartData.data.cart_items.length);
    }
  }, [cartData]);

  return (
    <Link
      href="/buyer/cart"
      className="bg-white text-[#28283C] flex items-center space-x-1 p-1 rounded-full shadow-md relative"
      style={{
        boxShadow: "0px 4px 16px 0px rgba(171, 190, 209, 0.16)",
      }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={1.5}
          d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
        />
      </svg>
      <span className="text-base font-medium">Cart</span>
      {cartItemCount > 0 && (
        <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
          {cartItemCount}
        </span>
      )}
    </Link>
  );
};

export default CartIcon;
