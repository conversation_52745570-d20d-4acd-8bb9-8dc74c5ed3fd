import toast from 'react-hot-toast';
import { baseurl } from "@/config";

/**
 * Refreshes the access token using the refresh token
 * @returns {Promise<Object|null>} - Returns the new tokens or null if refresh failed
 */
export const refreshToken = async () => {
  try {
    const refreshTokenValue = getCookie("refreshToken");

    if (!refreshTokenValue) {
      return null;
    }

    const response = await fetch(`${baseurl}/auth/refresh-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token: refreshTokenValue }),
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const data = await response.json();

    if (data.success && data.data) {
      // Update cookies with new tokens
      document.cookie = `accessToken=${data.data.accessToken};path=/;max-age=31536000`;
      if (data.data.refreshToken) {
        document.cookie = `refreshToken=${data.data.refreshToken};path=/;max-age=31536000`;
      }
      return data.data;
    }

    return null;
  } catch (error) {
    clearAllCookies();
    console.error('Error refreshing token:', error);
    return null;
  }
};

/**
 * Gets a cookie value by name
 * @param {string} name - The name of the cookie
 * @returns {string|null} - The cookie value or null if not found
 */
export const getCookie = (name) => {
  const match = document.cookie.match(new RegExp("(^| )" + name + "=([^;]+)"));
  return match ? decodeURIComponent(match[2]) : null;
};

/**
 * Handles unauthorized (401) and forbidden (403) errors
 * @param {Object} error - The error object from the API
 * @param {Function} router - Next.js router instance
 * @param {boolean} showToast - Whether to show a toast message (default: true)
 * @returns {boolean} - Returns true if handled as 401/403 error, false otherwise
 */
export const handleUnauthorized = (error, router, showToast = true) => {
  // Check if it's a 401 Unauthorized or 403 Forbidden error
    console.log('Unauthorized or forbidden error:', error);
  if (error?.status === 401 || error?.status === 403) {

    // Clear all cookies
    refreshToken();
    // clearAllCookies();

    // Show unauthorized toast if requested
    if (showToast) {
      toast.error('Your session has expired. Please log in again.');
    }

    // Redirect to login page
    setTimeout(() => {
      router.replace('/login');
    }, 1000);

    return true;
  }

  return false;
};

/**
 * Clears all cookies (used for logout)
 */
export const clearAllCookies = () => {
  document.cookie.split(";").forEach(cookie => {
    const [name] = cookie.trim().split("=");
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  });
};
