"use client";

import React, { useState, useEffect } from "react";
import { useFetchApiQuery, useMutateApiMutation } from "../../../../../redux/services/api";
import { useRouter } from "next/navigation";
import GoBack from "@/components/backend/GoBack";
import Breadcrumb from "@/components/backend/Breadcrumb";
import { toast } from "react-hot-toast";
import { asseturl } from "@/config";
import Image from 'next/image';
import Link from "next/link";
import { handleUnauthorized } from '@/utils/auth';
import { addToCart } from '@/utils/cart';

const OfferDetailsPage = ({ params }) => {
  // Unwrap the params promise
  const unwrappedParams = React.use(params);
  const offerId = unwrappedParams?.offerId;

  const router = useRouter();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  return (
    <OfferDetailsContent
      offerId={offerId}
      router={router}
      selectedImageIndex={selectedImageIndex}
      setSelectedImageIndex={setSelectedImageIndex}
    />
  );
};

const OfferDetailsContent = ({
  offerId,
  router,
  selectedImageIndex,
  setSelectedImageIndex,
}) => {
  // Fetch offer details
  const { data: responseData, isLoading, error, refetch } = useFetchApiQuery({
    endpoint: offerId ? `/buyer/offers/${offerId}` : null,
    skip: !offerId,
  });

  // Mutation hook for actions
  const [mutateApi, { isLoading: isActionLoading }] = useMutateApiMutation();

  // Handle adding the offer to cart
  const handleAddToCart = async () => {
    try {
      await addToCart(mutateApi, offerId);
    } catch (error) {
      if (!handleUnauthorized(error, router)) {
        console.error('Error adding to cart:', error);
      }
    }
  };

  // Add AuthErrorHandler for handling 401/403 errors
  const AuthErrorHandler = ({ error }) => {
    useEffect(() => {
      if (error?.status === 401 || error?.status === 403) {
        // Import and use the handleUnauthorized function
        import('@/utils/auth').then(({ handleUnauthorized }) => {
          handleUnauthorized(error, router);
        });
      }
    }, [error]);

    return null;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <GoBack />
        <Breadcrumb />
        <div className="mt-5 bg-white p-6 rounded-lg shadow-sm">
          <div className="text-center py-10">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-16 w-16 mx-auto text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Offer not found</h3>
            <p className="mt-1 text-sm text-gray-500">The offer you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.</p>
            <div className="mt-6">
              <button
                onClick={() => router.push("/buyer/processed-offers")}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Go back to offers
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const offer = responseData?.data || {};
  const request = offer.request || {};
  const attachments = offer.attachments || [];
  const statusHistory = offer.status_history || [];
  const negotiations = offer.negotiations || [];

  const isImageFile = (file) => {
    if (!file || !file.file_path) return false;

    // Check by file_type if available
    if (file.file_type) {
      return file.file_type.startsWith('image/');
    }

    // Fallback to extension check
    const extension = file.file_path.split('.').pop().toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension);
  };

  const getFileTypeColor = (file) => {
    if (!file || !file.file_path) return "#95a5a6";

    // Check by file_type if available
    if (file.file_type) {
      if (file.file_type.includes('pdf')) return "#e74c3c";
      if (file.file_type.includes('word') || file.file_type.includes('doc')) return "#2980b9";
      if (file.file_type.includes('excel') || file.file_type.includes('sheet')) return "#27ae60";
      if (file.file_type.includes('powerpoint') || file.file_type.includes('presentation')) return "#e67e22";
      if (file.file_type.includes('image')) return "#3498db";
      if (file.file_type.includes('video')) return "#9b59b6";
      if (file.file_type.includes('audio')) return "#f39c12";
      if (file.file_type.includes('csv')) return "#16a085";
      if (file.file_type.includes('zip') || file.file_type.includes('compressed')) return "#7f8c8d";
      if (file.file_type.includes('html') || file.file_type.includes('javascript') || file.file_type.includes('json')) return "#2c3e50";
    }

    // Fallback to extension check
    const extension = file.file_path.split('.').pop().toLowerCase();

    switch (extension) {
      case 'pdf': return "#e74c3c";
      case 'doc': case 'docx': return "#2980b9";
      case 'xls': case 'xlsx': return "#27ae60";
      case 'ppt': case 'pptx': return "#e67e22";
      case 'jpg': case 'jpeg': case 'png': case 'gif': case 'bmp': case 'webp': case 'svg': return "#3498db";
      case 'mp4': case 'avi': case 'mov': case 'wmv': return "#9b59b6";
      case 'mp3': case 'wav': case 'ogg': return "#f39c12";
      case 'csv': return "#16a085";
      case 'zip': case 'rar': case '7z': return "#7f8c8d";
      case 'html': case 'css': case 'js': case 'json': case 'xml': return "#2c3e50";
      default: return "#95a5a6";
    }
  };

  const imageAttachments = attachments.filter(att => isImageFile(att));

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getStatusColor = (status) => {
    if (!status) return 'gray';

    switch (status.toLowerCase()) {
      case 'approved': case 'completed': return 'green';
      case 'in progress': case 'processing': return 'yellow';
      case 'rejected': case 'cancelled': return 'red';
      case 'pending': return 'blue';
      default: return 'gray';
    }
  };

  const previewImage = imageAttachments.length > 0 && imageAttachments[selectedImageIndex]?.file_path
    ? imageAttachments[selectedImageIndex].file_path
    : "/placeholder.jpg";

  const getFileName = (file) => {
    if (!file || !file.file_path) return "Unknown";
    return file.file_name || file.file_path.split('/').pop();
  };

  const handleThumbnailClick = (index) => {
    setSelectedImageIndex(index);
  };

  const handleDocumentClick = (file) => {
    if (!file || !file.file_path) return;
    window.open(asseturl + file.file_path, '_blank');
  };

  return (
    <div className="p-6">
      {/* Handle authentication errors */}
      <AuthErrorHandler error={error} />

      <GoBack />
      <Breadcrumb />

      <div className="bg-white rounded-xl shadow-md p-6 mt-5 grid md:grid-cols-3 gap-6">

        <div className="col-span-3 grid grid-cols-1 md:grid-cols-2 gap-6 space-y-3 border border-gray-200 pl-6 rounded-lg">
          <div className="p-4">
            <div className="flex justify-between items-start">
              <h2 className="text-2xl font-bold text-gray-800">Offer for: {request.title || "Request Title"}</h2>
            </div>

            <div className="text-sm text-gray-500 space-x-4">
              <span>{request?.category?.name || "Category"}</span> -
              <span>{request?.sub_category?.name || "Subcategory"}</span>
            </div>

            <div className="text-xl font-semibold text-gray-700 mt-2">
              Price: ${offer.price || 0}
            </div>

            <div className="text-sm text-gray-600 mt-2">Delivery Time: {offer.delivery_time || 0} days</div>

            <div className="mt-3">
              <p className="text-gray-800 font-semibold">Status:</p>
              <span className={`px-2 py-1 rounded-full text-xs ${
                offer.status === 'Approved' ? 'bg-green-100 text-green-800' :
                offer.status === 'Pending' ? 'bg-blue-100 text-blue-800' :
                offer.status === 'Rejected' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {offer.status}
              </span>
            </div>

            <div className="mt-3">
              <p className="text-gray-800 font-semibold">Message:</p>
              <p className="text-gray-600">{offer.message || "N/A"}</p>
            </div>

            <div className="mt-3">
              <p className="text-gray-800 font-semibold">Description:</p>
              <p className="text-gray-600 whitespace-pre-line">{offer.description || "N/A"}</p>
            </div>

            <div className="mt-3">
              <p className="text-gray-800 font-semibold">Created At:</p>
              <p className="text-gray-600">{formatDateTime(offer.created_at)}</p>
            </div>

            <div className="mt-3">
              <p className="text-gray-800 font-semibold">Updated At:</p>
              <p className="text-gray-600">{formatDateTime(offer.updated_at)}</p>
            </div>

            {offer.status === 'Approved' && (
              <div className="mt-6">
                <button
                  onClick={handleAddToCart}
                  disabled={isActionLoading}
                  className="w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors cursor-pointer flex items-center justify-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" />
                  </svg>
                  Add to Cart
                </button>
              </div>
            )}
          </div>

          {/* Vertical divider */}
          <div className="space-y-3 p-4 w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Request Details</h3>
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div>
                <p className="text-gray-700 font-medium">Request Title:</p>
                <p className="text-gray-600 mt-1">{request.title || "N/A"}</p>
              </div>

              <div>
                <p className="text-gray-700 font-medium">Short Description:</p>
                <p className="text-gray-600 mt-1">{request.short_description || "N/A"}</p>
              </div>

              <div>
                <p className="text-gray-700 font-medium">Description:</p>
                <p className="text-gray-600 mt-1">{request.description || "N/A"}</p>
              </div>

              <div className="flex justify-between">
                <div>
                  <p className="text-gray-700 font-medium">Budget:</p>
                  <p className="text-gray-600 mt-1">${request.budget_min || 0} - ${request.budget_max || 0}</p>
                </div>
                <div>
                  <p className="text-gray-700 font-medium">Urgency:</p>
                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    request.urgency === 'High' ? 'bg-red-100 text-red-800' :
                    request.urgency === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {request.urgency || 'Low'}
                  </span>
                </div>
              </div>

              <div>
                <p className="text-gray-700 font-medium">Deadline:</p>
                <p className="text-gray-600 mt-1">
                  {request.deadline ? formatDateTime(request.deadline) : 'No deadline specified'}
                </p>
              </div>

              <div className="flex justify-end pt-3 space-x-3">
                <Link
                  href={`/requests/${request.id}`}
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer"
                >
                  View Request
                </Link>
                {offer.status === 'Approved' && (
                  <button
                    onClick={handleAddToCart}
                    disabled={isActionLoading}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 cursor-pointer"
                  >
                    Add to Cart
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {statusHistory && statusHistory.length > 0 && (
        <div className="mt-6 bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Status History</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Previous Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Updated By
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reason
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {statusHistory.map((history, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDateTime(history.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${getStatusColor(history.status)}-100 text-${getStatusColor(history.status)}-800`}>
                        {history.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {history.previous_status ? (
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${getStatusColor(history.previous_status)}-100 text-${getStatusColor(history.previous_status)}-800`}>
                          {history.previous_status}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {history.updated_by ? (
                        <div className="flex items-center">
                          <div className="ml-1">
                            <div className="text-sm font-medium text-gray-900">
                              {history.updated_by.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {history.updated_by.roles && history.updated_by.roles.join(', ')}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {history.reason || <span className="text-gray-400">-</span>}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {negotiations && negotiations.length > 0 && (
        <div className="mt-6 bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Negotiations</h2>
          <div className="space-y-4">
            {negotiations.map((negotiation, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg ${
                  negotiation.is_from_buyer
                    ? 'bg-blue-50 border-l-4 border-blue-500 ml-auto'
                    : 'bg-gray-50 border-l-4 border-gray-500'
                } max-w-3xl ${negotiation.is_from_buyer ? 'ml-auto' : 'mr-auto'}`}
              >
                <div className="flex justify-between items-start mb-2">
                  <span className="font-medium text-gray-800">
                    {negotiation.is_from_buyer ? 'You' : 'Seller'}
                  </span>
                  <span className="text-xs text-gray-500">
                    {formatDateTime(negotiation.created_at)}
                  </span>
                </div>
                <p className="text-gray-700 mb-3">{negotiation.message}</p>
                <div className="flex flex-wrap gap-4 text-sm">
                  {negotiation.proposed_price && (
                    <div className="bg-white px-3 py-1 rounded-full border border-gray-200">
                      <span className="font-medium">Proposed Price:</span> ${negotiation.proposed_price}
                    </div>
                  )}
                  {negotiation.proposed_delivery_time && (
                    <div className="bg-white px-3 py-1 rounded-full border border-gray-200">
                      <span className="font-medium">Delivery Time:</span> {negotiation.proposed_delivery_time} days
                    </div>
                  )}
                  <div className="bg-white px-3 py-1 rounded-full border border-gray-200">
                    <span className="font-medium">Status:</span> {negotiation.status}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default OfferDetailsPage;
