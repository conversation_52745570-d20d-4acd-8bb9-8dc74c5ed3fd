"use client";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import { useFetchApiQuery } from "@/redux/services/api";
import { useMutateApiMutation } from "../../../redux/services/api";
import Image from "next/image";
import React, { useRef } from "react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { asseturl } from "@/config";
import SubscriptionCard from "@/components/backend/SubscriptionCard";

const Profile = () => {
  const router = useRouter();
  // const { mutateApi, isLoading: uploadLoading } = useMutateApiMutation();
    const [mutateApi ] = useMutateApiMutation();

  const { data: data, isLoading, refetch } = useFetchApiQuery({
    endpoint: `/auth/profile`,
    skip: false
  });

  const user = data?.data;
  const profileInputRef = useRef(null);

  // Set profile image from user data
  const profileImageUrl = user?.profile_picture_url
    ? `${asseturl}${user.profile_picture_url}`
    : "/assets/backend_assets/images/user-img.png";

  // Handle profile picture upload
  const handleProfileImageChange = async (e) => {
    const file = e.target.files?.[0];
    if (file) {
      const formData = new FormData();
      formData.append('profile_picture', file);

      try {
        const response = await mutateApi({
          endpoint: "/auth/upload-profile-picture",
          data: formData,
          isFormData: true
        });

        if (response.success) {
          toast.success("Profile picture updated successfully!");
          refetch(); // Refresh user data
        }
      } catch (error) {
        console.error("Profile picture upload error:", error);
        toast.error("Failed to upload profile picture");
      }
    }
  };

  // Calculate profile completion percentage
  const calculateProfileCompletion = () => {
    if (!user) return 0;

    const fields = [
      'first_name', 'last_name', 'email', 'phone_number',
      'street', 'city', 'state', 'zip', 'country'
    ];

    if (user.user_type === 'personal') {
      fields.push('occupation', 'date_of_birth', 'gender');
    } else if (user.user_type === 'business') {
      fields.push('business_name', 'business_type', 'business_address');
    }

    const completedFields = fields.filter(field => user[field] && user[field].toString().trim() !== '');
    return Math.round((completedFields.length / fields.length) * 100);
  };

  const profileCompletion = calculateProfileCompletion();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <div className="max-w-7xl mx-auto bg-white shadow-sm rounded-lg overflow-hidden">
          {/* Banner */}
          <div className="relative h-48 bg-gradient-to-r from-blue-600 to-purple-600">
            <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            <div className="absolute top-4 right-4">
              <button
                onClick={() => router.push('/complete-profile')}
                className="bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-colors flex items-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Profile
              </button>
            </div>
          </div>

          {/* Profile section */}
          <div className="px-6 pb-6">
            <div className="flex flex-col md:flex-row">
              {/* Profile picture with upload button */}
              <div className="relative -mt-36 mb-4">
                <div className="relative inline-block w-[260px] h-[260px] bg-white rounded-full">
                  <Image
                    src={profileImageUrl}
                    fill
                    alt="Profile picture"
                    className="w-full h-full p-2 rounded-full object-cover"
                  />
                  <button
                    className="absolute w-[60px] h-[60px] bottom-8 right-0 bg-blue-600 p-2 rounded-full flex items-center justify-center transition-colors hover:bg-blue-700"
                    onClick={() => profileInputRef.current?.click()}
                    // disabled={uploadLoading}
                  >
                   
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="size-7 text-white"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"
                        />
                      </svg>
                    
                  </button>
                  <input
                    type="file"
                    ref={profileInputRef}
                    onChange={handleProfileImageChange}
                    accept="image/*"
                    className="hidden"
                  />
                </div>
              </div>

              {/* Profile info */}
              <div className="md:ml-6 flex-1">
                <div className="flex mt-8 flex-col md:flex-row md:items-center justify-between">
                  <div>
                    <h1 className="text-5xl archivo font-bold text-black">
                      {user?.first_name || "First"} {user?.last_name || "Last"}
                    </h1>
                    <p className="text-[#9A9A9B] font-2xl font-medium archivo mt-2 flex items-center">
                      @{user?.username || "username"}
                    </p>
                    <p className="text-[#9A9A9B] font-2xl font-medium archivo mt-1 flex items-center">
                      {user?.email}
                      {user?.is_email_verified ? (
                        <span className="ml-2 text-green-500" title="Email Verified">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </span>
                      ) : (
                        <span className="ml-2 text-red-500" title="Email Not Verified">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                        </span>
                      )}
                    </p>
                    {user?.user_type === 'business' && user?.business_name && (
                      <p className="text-[#9A9A9B] font-2xl font-medium archivo mt-1">
                        🏢 {user.business_name}
                      </p>
                    )}
                  </div>
                  <div className="bg-white border border-gray-200 rounded-lg p-4 w-full md:w-auto">
                    <div className="flex flex-col w-[328px]">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-[#242B3A] archivo text-xl capitalize font-medium">
                          Profile Completion: {profileCompletion}%
                        </span>
                      </div>

                      <div className="w-full bg-[#DADADA] rounded-full h-2.5">
                        <div
                          className="bg-[#0A67F2] h-2.5 rounded-full transition-all duration-300"
                          style={{ width: `${profileCompletion}%` }}
                        />
                      </div>

                      {user?.roles && user.roles.length > 0 && (
                        <div className="mt-3">
                          <span className="text-[#242B3A] archivo text-sm font-medium">
                            Roles: {user.roles.join(", ")}
                          </span>
                        </div>
                      )}

                      <div className="mt-2">
                        <span className="text-[#242B3A] archivo text-sm font-medium">
                          Account Type: <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 capitalize">
                            {user?.user_type || "N/A"}
                          </span>
                        </span>
                      </div>

                      <div className="mt-2">
                        <span className="text-[#242B3A] archivo text-sm font-medium">
                          Status: <span className={`px-2 py-1 rounded-full text-xs ${user?.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                            {user?.status ? user.status.charAt(0).toUpperCase() + user.status.slice(1) : "N/A"}
                          </span>
                        </span>
                      </div>

                      {user?.is_approved !== undefined && (
                        <div className="mt-2">
                          <span className="text-[#242B3A] archivo text-sm font-medium">
                            Approval: <span className={`px-2 py-1 rounded-full text-xs ${user.is_approved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                              {user.is_approved ? "Approved" : "Pending"}
                            </span>
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <p className="text-[#9A9A9B] font-2xl font-normal archivo mt-2">
                    {user?.bio || "No bio available. Click 'Edit Profile' to add a bio and complete your profile."}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="max-w-7xl mx-auto p-6 bg-white shadow-sm rounded-lg overflow-hidden mt-5">
          <div className="max-w-full">
            {/* Profile Information Header */}
            <div className="pb-5 border-b border-[#E5E7EB]">
              <div className="border-b border-[#E5E7EB] pb-4">
                <h5 className="font-medium text-[#343A40] text-xl inter">
                  Profile Information
                </h5>
                <p className="text-[#9A9A9B] text-sm font-normal inter mt-1">
                  {"View your profile details. Click 'Edit Profile' to make changes."}
                </p>
              </div>

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-5">
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Username</p>
                  <p className="text-[#6B7280] text-sm">{user?.username || "Not provided"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Email</p>
                  <p className="text-[#6B7280] text-sm">{user?.email || "Not provided"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Phone Number</p>
                  <p className="text-[#6B7280] text-sm">{user?.phone_number || "Not provided"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">User Type</p>
                  <p className="text-[#6B7280] text-sm capitalize">{user?.user_type || "Not provided"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Gender</p>
                  <p className="text-[#6B7280] text-sm capitalize">{user?.gender || "Not provided"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Date of Birth</p>
                  <p className="text-[#6B7280] text-sm">
                    {user?.date_of_birth ? new Date(user.date_of_birth).toLocaleDateString() : "Not provided"}
                  </p>
                </div>
              </div>
            </div>
            {/* Address Information */}
            <div className="border-b border-[#E5E7EB] py-5">
              <div className="border-b border-[#E5E7EB] pb-4">
                <h5 className="font-medium text-[#343A40] text-xl inter">
                  Address Information
                </h5>
                <p className="text-[#9A9A9B] text-sm font-normal inter mt-1">
                  Your current address details.
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-5">
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Street Address</p>
                  <p className="text-[#6B7280] text-sm">{user?.street || "Not provided"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">City</p>
                  <p className="text-[#6B7280] text-sm">{user?.city || "Not provided"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">State/Province</p>
                  <p className="text-[#6B7280] text-sm">{user?.state || "Not provided"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">ZIP/Postal Code</p>
                  <p className="text-[#6B7280] text-sm">{user?.zip || "Not provided"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Country</p>
                  <p className="text-[#6B7280] text-sm">{user?.country || "Not provided"}</p>
                </div>
              </div>
            </div>

            {/* Business Information (if business user) */}
            {user?.user_type === 'business' && (
              <div className="border-b border-[#E5E7EB] py-5">
                <div className="border-b border-[#E5E7EB] pb-4">
                  <h5 className="font-medium text-[#343A40] text-xl inter">
                    Business Information
                  </h5>
                  <p className="text-[#9A9A9B] text-sm font-normal inter mt-1">
                    Your business details and registration information.
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-5">
                  <div className="space-y-1">
                    <p className="text-[#374151] text-sm font-medium inter">Business Name</p>
                    <p className="text-[#6B7280] text-sm">{user?.business_name || "Not provided"}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-[#374151] text-sm font-medium inter">Business Type</p>
                    <p className="text-[#6B7280] text-sm">{user?.business_type || "Not provided"}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-[#374151] text-sm font-medium inter">Business Website</p>
                    <p className="text-[#6B7280] text-sm">
                      {user?.business_website ? (
                        <a href={user.business_website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                          {user.business_website}
                        </a>
                      ) : (
                        "Not provided"
                      )}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-[#374151] text-sm font-medium inter">Registration Number</p>
                    <p className="text-[#6B7280] text-sm">{user?.business_registration_number || "Not provided"}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-[#374151] text-sm font-medium inter">Tax/VAT ID</p>
                    <p className="text-[#6B7280] text-sm">{user?.tax_id || "Not provided"}</p>
                  </div>
                  <div className="space-y-1 md:col-span-2 lg:col-span-3">
                    <p className="text-[#374151] text-sm font-medium inter">Business Address</p>
                    <p className="text-[#6B7280] text-sm">{user?.business_address || "Not provided"}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Personal Information (if personal user) */}
            {user?.user_type === 'personal' && (
              <div className="border-b border-[#E5E7EB] py-5">
                <div className="border-b border-[#E5E7EB] pb-4">
                  <h5 className="font-medium text-[#343A40] text-xl inter">
                    Personal Information
                  </h5>
                  <p className="text-[#9A9A9B] text-sm font-normal inter mt-1">
                    Your personal details and preferences.
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-5">
                  <div className="space-y-1">
                    <p className="text-[#374151] text-sm font-medium inter">Occupation</p>
                    <p className="text-[#6B7280] text-sm">{user?.occupation || "Not provided"}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-[#374151] text-sm font-medium inter">Interests</p>
                    <p className="text-[#6B7280] text-sm">{user?.interests || "Not provided"}</p>
                  </div>
                  <div className="space-y-1 md:col-span-2 lg:col-span-3">
                    <p className="text-[#374151] text-sm font-medium inter">Bio</p>
                    <p className="text-[#6B7280] text-sm">{user?.bio || "Not provided"}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Account Information */}
            <div className="py-5">
              <div className="border-b border-[#E5E7EB] pb-4">
                <h5 className="font-medium text-[#343A40] text-xl inter">
                  Account Information
                </h5>
                <p className="text-[#9A9A9B] text-sm font-normal inter mt-1">
                  Account status and verification details.
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-5">
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Account Created</p>
                  <p className="text-[#6B7280] text-sm">
                    {user?.created_at ? new Date(user.created_at).toLocaleDateString() : "Not available"}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Last Updated</p>
                  <p className="text-[#6B7280] text-sm">
                    {user?.updated_at ? new Date(user.updated_at).toLocaleDateString() : "Not available"}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Last Login</p>
                  <p className="text-[#6B7280] text-sm">
                    {user?.last_login_at ? new Date(user.last_login_at).toLocaleDateString() : "Not available"}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Email Verification</p>
                  <p className={`text-sm ${user?.is_email_verified ? 'text-green-600' : 'text-red-600'}`}>
                    {user?.is_email_verified ? "Verified" : "Not Verified"}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Profile Completion</p>
                  <p className={`text-sm ${user?.is_completed_profile ? 'text-green-600' : 'text-yellow-600'}`}>
                    {user?.is_completed_profile ? "Complete" : "Incomplete"}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#374151] text-sm font-medium inter">Account Status</p>
                  <p className={`text-sm capitalize ${user?.status === 'active' ? 'text-green-600' : 'text-gray-600'}`}>
                    {user?.status || "Unknown"}
                  </p>
                </div>
              </div>
            </div>
            {/* Action Buttons */}
            <div className="mt-10 flex items-center space-x-4 justify-end">
              <button
                type="button"
                onClick={() => router.push('/complete-profile')}
                className="text-white bg-[#4F46E5] hover:bg-[#4338CA] inter font-medium rounded-lg text-sm px-6 py-3 flex items-center gap-2 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Profile
              </button>
            </div>
          </div>
        </div>

        
      </div>
    </>
  );
};

export default Profile;
