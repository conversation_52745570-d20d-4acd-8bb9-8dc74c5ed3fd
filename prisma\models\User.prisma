model users {
  id                         String   @id @default(uuid())
  username                   String?  @unique
  first_name                 String?
  last_name                  String?
  father_name                String?  @default("")
  mother_name                String?  @default("")
  date_of_birth              DateTime?
  gender                     String?  @default("")

  // Address fields (detailed)
  address                    String?  @default("")
  street                     String?
  city                       String?
  state                      String?
  zip                        String?
  country                    String?

  phone_number               String?   @unique
  email                      String   @unique
  business_name               String?
  is_email_verified          <PERSON>olean  @default(false)
  email_verification_token   String?
  email_verification_expires DateTime?
  national_id_number         String?  @unique
  passport_number            String?  @unique
  profile_picture_url        String?
  password_hash              String
  refresh_token              String?
  is_approved                Boolean  @default(false)
  status                     String   @default("active")
  is_deleted                 <PERSON>olean  @default(false)

  // New profile fields
  age_confirm                Boolean? @default(false)
  user_type                  String?  // 'business' or 'personal'
  occupation                 String?
  interests                  String?
  bio                        String?

  // Business-specific fields
  business_type              String?
  business_website           String?
  business_registration_number String?
  business_address           String?
  tax_id                     String?
  business_document_url      String?

  // Document uploads
  nid_document_url           String?

  // Profile completion tracking
  is_completed_profile       Boolean? @default(false)

  created_at                 DateTime @default(now())
  updated_at                 DateTime @updatedAt
  last_login_at              DateTime?
  password_reset_token       String?
  password_reset_expires_at  DateTime?

  // Relationships
  roles          user_roles[]
  permissions    user_permissions[]

  sent_offers         offer_negotiations[] @relation("Sender")
  received_offers     offer_negotiations[] @relation("Recipient")

  seller_offers       offers[] @relation("Seller")
  requests           requests[] @relation("Buyer")
  merged_requests    request_merged_items[] @relation("MergedByAdmin")
  request_status_updator request_statuses[] @relation("RequestUpdator")
  order_refunds      order_refunds[] @relation("BuyerOrderRefund")
  order_payments     order_payments[] @relation("BuyerOrderPayment")
  order_status_changes order_status_changes[] @relation("OrderStatusUpdator")
  offer_status_changes offer_status_changes[] @relation("OfferStatusUpdator")
  buyer_orders       orders[] @relation("BuyerOrder")
  seller_orders      orders[] @relation("SellerOrder")
  seller_products      products[] @relation("SellerProduct")
  seller_services      services[] @relation("SellerService")

  assigned_requests request_assigned_sellers[] @relation("AssignedSeller")
  seller_assignments request_assigned_sellers[] @relation("SellerAssigner")

  buyer_cart        carts[] @relation("BuyerCart")
  business_informations business_informations[]
  user_subscriptions user_subscriptions[]

  // Seller interests
  seller_category_interests    seller_interested_categories[] @relation("SellerCategoryInterests")
  seller_subcategory_interests seller_interested_subcategories[] @relation("SellerSubcategoryInterests")

  // Blog posts
  blog_posts                   blog_posts[] @relation("BlogPostAuthor")

}



model roles {
  id         String   @id @default(uuid())
  name       String   @unique
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relationships
  users        user_roles[]
  permissions  role_permissions[]
}

model permissions {
  id         String   @id @default(uuid())
  name       String   @unique
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relationships
  users user_permissions[]
  roles role_permissions[]
}

model user_roles {
  id       String  @id @default(uuid())
  user_id  String
  role_id  String

  user     users   @relation(fields: [user_id], references: [id], onDelete: Cascade)
  role     roles   @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([user_id, role_id])
}

model role_permissions {
  id          String  @id @default(uuid())
  role_id     String
  permission_id String

  role        roles        @relation(fields: [role_id], references: [id], onDelete: Cascade)
  permission  permissions  @relation(fields: [permission_id], references: [id], onDelete: Cascade)

  @@unique([role_id, permission_id])
}

model user_permissions {
  id            String  @id @default(uuid())
  user_id       String
  permission_id String

  user         users         @relation(fields: [user_id], references: [id], onDelete: Cascade)
  permission   permissions   @relation(fields: [permission_id], references: [id], onDelete: Cascade)

  @@unique([user_id, permission_id])
}

