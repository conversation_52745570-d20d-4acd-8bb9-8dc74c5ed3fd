const BlogPostModel = require('../../models/blogPostModel');
const BlogCategoryModel = require('../../models/blogCategoryModel');
const BlogService = require('../../services/blogService');
const sendResponse = require('../../utils/sendResponse');

/**
 * Admin Blog Controller - Handles admin blog operations
 */
class AdminBlogController {
  /**
   * Get all blog posts (Admin)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getAllBlogPosts(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        category_id,
        author_id,
        is_featured,
        search
      } = req.query;

      const filters = {
        ...(status && { status }),
        ...(category_id && { category_id }),
        ...(author_id && { author_id }),
        ...(is_featured !== undefined && { is_featured: is_featured === 'true' }),
        ...(search && { search })
      };

      const result = await BlogPostModel.getAllBlogPosts(
        filters,
        parseInt(page),
        parseInt(limit)
      );

      return sendResponse(
        res,
        true,
        'Blog posts retrieved successfully',
        result,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve blog posts',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Get blog post by ID (Admin)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBlogPostById(req, res) {
    try {
      const { id } = req.params;

      const blogPost = await BlogPostModel.getBlogPostById(id);

      if (!blogPost) {
        return sendResponse(
          res,
          false,
          'Blog post not found',
          null,
          { general: ['Blog post not found'] },
          null,
          404
        );
      }

      // Format tags for response
      if (blogPost.tags) {
        blogPost.tags = BlogService.formatTags(blogPost.tags);
      }

      return sendResponse(
        res,
        true,
        'Blog post retrieved successfully',
        blogPost,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve blog post',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Create blog post (Admin)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async createBlogPost(req, res) {
    try {
      const data = req.body;
      data.author_id = req.user.id; // Set author to current admin

      // Validate data
      const validation = BlogService.validateBlogPostData(data);
      if (!validation.isValid) {
        return sendResponse(
          res,
          false,
          'Validation failed',
          null,
          validation.errors,
          null,
          400
        );
      }

      // Process data
      const processedData = await BlogService.processBlogPostData(data);

      // Create blog post
      const blogPost = await BlogPostModel.createBlogPost(processedData);

      // Format tags for response
      if (blogPost.tags) {
        blogPost.tags = BlogService.formatTags(blogPost.tags);
      }

      return sendResponse(
        res,
        true,
        'Blog post created successfully',
        blogPost,
        null,
        null,
        201
      );
    } catch (error) {
      if (error.code === 'P2002') {
        return sendResponse(
          res,
          false,
          'Blog post with this slug already exists',
          null,
          { slug: ['Slug must be unique'] },
          null,
          400
        );
      }

      return sendResponse(
        res,
        false,
        'Failed to create blog post',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Update blog post (Admin)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateBlogPost(req, res) {
    try {
      const { id } = req.params;
      const data = req.body;

      // Validate data
      const validation = BlogService.validateBlogPostData(data, true);
      if (!validation.isValid) {
        return sendResponse(
          res,
          false,
          'Validation failed',
          null,
          validation.errors,
          null,
          400
        );
      }

      // Process data
      const processedData = await BlogService.processBlogPostData(data, true);

      // Update blog post
      const blogPost = await BlogPostModel.updateBlogPost(id, processedData);

      // Format tags for response
      if (blogPost.tags) {
        blogPost.tags = BlogService.formatTags(blogPost.tags);
      }

      return sendResponse(
        res,
        true,
        'Blog post updated successfully',
        blogPost,
        null,
        null,
        200
      );
    } catch (error) {
      if (error.code === 'P2002') {
        return sendResponse(
          res,
          false,
          'Blog post with this slug already exists',
          null,
          { slug: ['Slug must be unique'] },
          null,
          400
        );
      }

      if (error.code === 'P2025') {
        return sendResponse(
          res,
          false,
          'Blog post not found',
          null,
          { general: ['Blog post not found'] },
          null,
          404
        );
      }

      return sendResponse(
        res,
        false,
        'Failed to update blog post',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Delete blog post (Admin)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async deleteBlogPost(req, res) {
    try {
      const { id } = req.params;

      await BlogPostModel.deleteBlogPost(id);

      return sendResponse(
        res,
        true,
        'Blog post deleted successfully',
        null,
        null,
        null,
        200
      );
    } catch (error) {
      if (error.code === 'P2025') {
        return sendResponse(
          res,
          false,
          'Blog post not found',
          null,
          { general: ['Blog post not found'] },
          null,
          404
        );
      }

      return sendResponse(
        res,
        false,
        'Failed to delete blog post',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Get all blog categories (Admin)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getAllBlogCategories(req, res) {
    try {
      const { is_active, search } = req.query;

      const filters = {
        ...(is_active !== undefined && { is_active: is_active === 'true' }),
        ...(search && { search })
      };

      const categories = await BlogCategoryModel.getAllBlogCategories(filters);

      return sendResponse(
        res,
        true,
        'Blog categories retrieved successfully',
        categories,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve blog categories',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Get blog category by ID (Admin)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBlogCategoryById(req, res) {
    try {
      const { id } = req.params;

      const category = await BlogCategoryModel.getBlogCategoryById(id);

      if (!category) {
        return sendResponse(
          res,
          false,
          'Blog category not found',
          null,
          { general: ['Blog category not found'] },
          null,
          404
        );
      }

      return sendResponse(
        res,
        true,
        'Blog category retrieved successfully',
        category,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve blog category',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Create blog category (Admin)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async createBlogCategory(req, res) {
    try {
      const data = req.body;

      // Validate data
      const validation = BlogService.validateBlogCategoryData(data);
      if (!validation.isValid) {
        return sendResponse(
          res,
          false,
          'Validation failed',
          null,
          validation.errors,
          null,
          400
        );
      }

      // Check if name or slug already exists
      const [nameExists, slugExists] = await Promise.all([
        BlogCategoryModel.nameExists(data.name),
        BlogCategoryModel.slugExists(data.slug)
      ]);

      if (nameExists) {
        return sendResponse(
          res,
          false,
          'Category name already exists',
          null,
          { name: ['Name must be unique'] },
          null,
          400
        );
      }

      if (slugExists) {
        return sendResponse(
          res,
          false,
          'Category slug already exists',
          null,
          { slug: ['Slug must be unique'] },
          null,
          400
        );
      }

      // Create category
      const category = await BlogCategoryModel.createBlogCategory(data);

      return sendResponse(
        res,
        true,
        'Blog category created successfully',
        category,
        null,
        null,
        201
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to create blog category',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }

  /**
   * Get blog statistics (Admin)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBlogStatistics(req, res) {
    try {
      const statistics = await BlogService.getBlogStatistics();

      return sendResponse(
        res,
        true,
        'Blog statistics retrieved successfully',
        statistics,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        'Failed to retrieve blog statistics',
        null,
        { general: [error.message] },
        null,
        500
      );
    }
  }
}

module.exports = AdminBlogController;
