"use client";
export const dynamic = "force-dynamic";
import Breadcrumb from "@/components/backend/Breadcrumb";
import GoBack from "@/components/backend/GoBack";
import Pagination from "@/components/backend/Pagination";
import SelectInput from "@/components/backend/SelectInput";
import Link from "next/link";
import React, { useState } from "react";
import { useFetchApiQuery, useMutateApiMutation } from "../../../../redux/services/api";
import { handleUnauthorized } from '@/utils/auth';
import { useRouter } from 'next/navigation';
import { addToCart } from '@/utils/cart';
import Button from "@/components/backend/Button";

const ProcessedOffers = () => {
  const router = useRouter();
  const [selectedFilter, setSelectedFilter] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [subCategories, setSubCategories] = useState([]);
  const [selectedSubCategory, setSelectedSubCategory] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch processed offers
  const { data: processedOffersData, isLoading, error, refetch } = useFetchApiQuery({
    endpoint: "/buyer/processed-offers",
    skip: false, // Ensure the query always runs with a valid endpoint
  });

  // Fetch categories for filtering
  const { data: categoriesData } = useFetchApiQuery({
    endpoint: "/buyer/categories",
    skip: false, // Ensure the query always runs with a valid endpoint
  });

  // Mutation hook for cart operations
  const [mutateApi, { isLoading: isAddingToCart }] = useMutateApiMutation();

  // Handle adding an item to the cart
  const handleAddToCart = async (offerId) => {
    try {
      await addToCart(mutateApi, offerId);
    } catch (error) {
      if (!handleUnauthorized(error, router)) {
        console.error('Error adding to cart:', error);
      }
    }
  };

  // Handle category change
  const handleCategoryChange = (e) => {
    const categoryId = e.target.value;
    setSelectedCategory(categoryId);

    // Find the selected category and get its subcategories
    if (categoryId && categoriesData?.data) {
      const category = categoriesData.data.find(cat => cat.id === categoryId);
      if (category && category.sub_categories) {
        setSubCategories(category.sub_categories);
      } else {
        setSubCategories([]);
      }
    } else {
      setSubCategories([]);
    }

    setSelectedSubCategory("");
  };

  const processedOffers = processedOffersData?.data || [];
  const totalCount = processedOffersData?.meta?.total || 0;
  const totalPages = processedOffersData?.meta?.pages || 1;
  const perPage = processedOffersData?.meta?.per_page || 10;
  const categories = categoriesData?.data || [];

  // Format date to "17th January, 2025 2:00 pm" format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString('default', { month: 'long' });
    const year = date.getFullYear();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'pm' : 'am';
    const formattedHours = hours % 12 || 12;

    // Add ordinal suffix to day
    const suffixes = ['th', 'st', 'nd', 'rd'];
    const relevantDigits = (day < 30) ? day % 20 : day % 30;
    const suffix = (relevantDigits <= 3) ? suffixes[relevantDigits] : suffixes[0];

    return `${day}${suffix} ${month}, ${year} ${formattedHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  };

  // Filter options
  const filterOptions = [
    { id: "newest", title: "Newest" },
    { id: "oldest", title: "Oldest" },
    { id: "price_high", title: "Price: High to Low" },
    { id: "price_low", title: "Price: Low to High" },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    // Handle unauthorized access
    handleUnauthorized(error, router);

    return (
      <div className="text-center py-10">
        <p className="text-red-500">Error loading processed offers</p>
      </div>
    );
  }

  return (
    <>
      <GoBack />
      <Breadcrumb />
      <div className="mt-5">
        <h5 className="font-semibold text-[#343A40] text-xl poppins">
          Processed Offers
        </h5>
        <div className="relative overflow-x-auto drop-shadow-xs mt-5 bg-white p-4">
          <div className="mb-4 flex flex-wrap items-center gap-2">
            <div>
              <SelectInput
                options={categories}
                value={selectedCategory}
                onChange={handleCategoryChange}
                placeholder="Select Category"
                className="w-[170px]"
              />
            </div>
            <div>
              <SelectInput
                options={subCategories}
                value={selectedSubCategory}
                onChange={(e) => setSelectedSubCategory(e.target.value)}
                placeholder="Select Sub Category"
                className="w-[200px]"
                disabled={!selectedCategory}
              />
            </div>
            <div>
              <SelectInput
                options={filterOptions}
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                placeholder="Filter"
                className="w-[200px]"
              />
            </div>
            <div className="flex-grow md:flex-grow-0">
              <div className="relative">
                <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                  <svg
                    className="w-4 h-4 text-[#374151]"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 20 20"
                  >
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                    />
                  </svg>
                </div>
                <input
                  type="search"
                  id="default-search"
                  className="ps-10 border inter font-medium border-[#D1D5DB] placeholder:text-[#374151] text-[#374151] text-sm rounded-lg outline-0 block p-2 h-10 w-full"
                  placeholder="Search by title"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>

          {processedOffers.length === 0 ? (
            <div className="text-center py-10">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              <p className="mt-4 text-lg text-gray-600">No processed offers found</p>
              <p className="text-gray-500">You don&apos;t have any processed offers at the moment.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left rtl:text-right text-gray-500 inter">
                <thead className="text-xs text-[#4C596E] capitalize bg-[#E2E8F0]">
                  <tr>
                    <th scope="col" className="px-3 py-3 font-medium">
                      #
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Request Title
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Category
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Sub Category
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Price
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Delivery Time
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Status
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Created At
                    </th>
                    <th scope="col" className="px-3 py-3 font-medium">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {processedOffers.map((offer, index) => (
                    <tr key={offer.id} className="bg-white border-b border-gray-200">
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        {index + 1}
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        <Link
                          href={`/requests/${offer.request?.id}`}
                          className="text-indigo-600 hover:text-indigo-800 hover:underline"
                        >
                          {offer.request?.title || 'N/A'}
                        </Link>
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        {offer.request?.category?.name || 'N/A'}
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        {offer.request?.sub_category?.name || 'N/A'}
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        ${offer.price}
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        {offer.delivery_time} days
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          offer.status === 'Approved' ? 'bg-green-100 text-green-800' :
                          offer.status === 'Pending' ? 'bg-blue-100 text-blue-800' :
                          offer.status === 'Rejected' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {offer.status}
                        </span>
                      </td>
                      <td className="px-3 py-4 font-semibold text-sm inter text-[#4A5568] whitespace-nowrap">
                        {formatDate(offer.created_at)}
                      </td>
                      <td className="px-3 py-4 font-medium inter text-[#4A5568] whitespace-nowrap">
                        <div className="flex space-x-2">
                          <Link
                            href={`/buyer/offers/${offer.id}`}
                            className="text-sm font-medium px-3 py-1 bg-[#FC791A] text-white rounded-sm hover:bg-[#e66c13] transition-colors"
                          >
                            View Details
                          </Link>
                          {offer.status === 'Approved' && (
                            <Button
                              onClick={() => handleAddToCart(offer.id)}
                              disabled={isAddingToCart}
                              className="text-sm font-medium px-3 py-1 bg-green-600 text-white rounded-sm hover:bg-green-700 transition-colors"
                            >
                              Add to Cart
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          <Pagination
            totalItems={totalCount}
            pageSize={perPage}
            currentPage={currentPage}
            onPageChange={(page) => setCurrentPage(page)}
          />
        </div>
      </div>
    </>
  );
};

export default ProcessedOffers;
