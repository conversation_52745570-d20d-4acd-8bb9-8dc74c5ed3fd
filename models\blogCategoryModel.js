const { prisma } = require('../config/dbConfig');

/**
 * BlogCategoryModel - Handles all blog category related database operations
 */
class BlogCategoryModel {
  /**
   * Get all blog categories (Admin)
   * @param {Object} filters - Filter criteria
   * @returns {Promise<Array>} All blog categories
   */
  static async getAllBlogCategories(filters = {}) {
    const whereClause = {
      ...(filters.is_active !== undefined && { is_active: filters.is_active }),
      ...(filters.search && {
        OR: [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } }
        ]
      })
    };

    return await prisma.blog_categories.findMany({
      where: whereClause,
      orderBy: [
        { sort_order: 'asc' },
        { name: 'asc' }
      ],
      include: {
        _count: {
          select: {
            blog_posts: {
              where: {
                is_deleted: false,
                status: 'published'
              }
            }
          }
        }
      }
    });
  }

  /**
   * Get active blog categories for public view
   * @returns {Promise<Array>} Active blog categories
   */
  static async getActiveBlogCategories() {
    return await prisma.blog_categories.findMany({
      where: { is_active: true },
      orderBy: [
        { sort_order: 'asc' },
        { name: 'asc' }
      ],
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        color: true,
        icon: true,
        _count: {
          select: {
            blog_posts: {
              where: {
                is_deleted: false,
                status: 'published',
                published_at: { lte: new Date() }
              }
            }
          }
        }
      }
    });
  }

  /**
   * Get blog category by ID (Admin)
   * @param {string} id - Category ID
   * @returns {Promise<Object|null>} Blog category
   */
  static async getBlogCategoryById(id) {
    return await prisma.blog_categories.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            blog_posts: {
              where: { is_deleted: false }
            }
          }
        }
      }
    });
  }

  /**
   * Get blog category by slug (Public)
   * @param {string} slug - Category slug
   * @returns {Promise<Object|null>} Blog category
   */
  static async getBlogCategoryBySlug(slug) {
    return await prisma.blog_categories.findUnique({
      where: { slug, is_active: true },
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        color: true,
        icon: true,
        _count: {
          select: {
            blog_posts: {
              where: {
                is_deleted: false,
                status: 'published',
                published_at: { lte: new Date() }
              }
            }
          }
        }
      }
    });
  }

  /**
   * Create blog category (Admin)
   * @param {Object} data - Category data
   * @returns {Promise<Object>} Created category
   */
  static async createBlogCategory(data) {
    return await prisma.blog_categories.create({
      data
    });
  }

  /**
   * Update blog category (Admin)
   * @param {string} id - Category ID
   * @param {Object} data - Updated category data
   * @returns {Promise<Object>} Updated category
   */
  static async updateBlogCategory(id, data) {
    return await prisma.blog_categories.update({
      where: { id },
      data
    });
  }

  /**
   * Delete blog category (Admin)
   * @param {string} id - Category ID
   * @returns {Promise<Object>} Deleted category
   */
  static async deleteBlogCategory(id) {
    // Check if category has blog posts
    const postsCount = await prisma.blog_posts.count({
      where: { category_id: id, is_deleted: false }
    });

    if (postsCount > 0) {
      throw new Error('Cannot delete category that has blog posts. Please move or delete the posts first.');
    }

    return await prisma.blog_categories.delete({
      where: { id }
    });
  }

  /**
   * Update category sort order (Admin)
   * @param {Array} categories - Array of {id, sort_order}
   * @returns {Promise<void>}
   */
  static async updateCategorySortOrder(categories) {
    const updatePromises = categories.map(category =>
      prisma.blog_categories.update({
        where: { id: category.id },
        data: { sort_order: category.sort_order }
      })
    );

    await Promise.all(updatePromises);
  }

  /**
   * Check if category slug exists
   * @param {string} slug - Category slug
   * @param {string} excludeId - ID to exclude from check (for updates)
   * @returns {Promise<boolean>} Whether slug exists
   */
  static async slugExists(slug, excludeId = null) {
    const whereClause = { slug };
    if (excludeId) {
      whereClause.id = { not: excludeId };
    }

    const category = await prisma.blog_categories.findFirst({
      where: whereClause
    });

    return !!category;
  }

  /**
   * Check if category name exists
   * @param {string} name - Category name
   * @param {string} excludeId - ID to exclude from check (for updates)
   * @returns {Promise<boolean>} Whether name exists
   */
  static async nameExists(name, excludeId = null) {
    const whereClause = { name };
    if (excludeId) {
      whereClause.id = { not: excludeId };
    }

    const category = await prisma.blog_categories.findFirst({
      where: whereClause
    });

    return !!category;
  }
}

module.exports = BlogCategoryModel;
