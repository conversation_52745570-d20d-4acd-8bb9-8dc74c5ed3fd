import Image from "next/image";
import Link from "next/link";
import React from "react";
const FeaturedCard = ({ item, index }) => {
  const { title, image, des } = item;
  return (
    <>
      <div
        className="bg-[#FFFFFF01] rounded-[10px] overflow-hidden"
        data-aos="fade-left"
        data-aos-offset="200"
        data-aos-delay={50 * (index + 1)}
        data-aos-duration="1000"
        data-aos-easing="ease-in-out"
      >
        <div className="relative overflow-hidden rounded-[16px]">
          <Image
            src={image}
            width={1000}
            height={1000}
            quality={100}
            className="object-contain mx-auto w-full h-auto"
            alt={title || "Featured image"}
          />
          <span className="bg-[#03C95A] text-white text-base font-bold px-2.5 py-1 rounded-sm inline-block mb-2 archivo absolute top-0 left-0 w-[100px] text-center rounded-br-[16px]">
            New
          </span>
        </div>

        <div className="flex flex-col py-4">
          <span className="bg-[#0A67F21A] text-[#0A67F2] text-sm font-bold px-2.5 py-1 rounded-sm inline-block w-max mb-2 dm_sans">
            Limited Offer
          </span>

          <h6 className="text-[#242B3A] archivo font-semibold text-lg mb-1">
            {title}{" "}
          </h6>
          <p className="text-[#74788D] archivo font-normal text-sm mb-1">
            {des}
          </p>

          <Link
            href="/offers/details"
            className="relative flex items-center font-bold text-sm text-[#0C1228] mt-2"
          >
            Offer Details{" "}
            <svg
              width={13}
              height={11}
              className="ml-2"
              viewBox="0 0 13 11"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7.54478 10.166C7.49829 10.1196 7.46141 10.0645 7.43625 10.0038C7.41109 9.94306 7.39814 9.878 7.39814 9.81229C7.39814 9.74658 7.41109 9.68152 7.43625 9.62082C7.46141 9.56012 7.49829 9.50498 7.54478 9.45854L11.1917 5.81229H1.39853C1.26592 5.81229 1.13875 5.75961 1.04498 5.66584C0.951211 5.57208 0.898533 5.4449 0.898533 5.31229C0.898533 5.17968 0.951211 5.0525 1.04498 4.95874C1.13875 4.86497 1.26592 4.81229 1.39853 4.81229H11.1917L7.54478 1.16604C7.45096 1.07222 7.39825 0.944972 7.39825 0.81229C7.39825 0.679608 7.45096 0.55236 7.54478 0.45854C7.6386 0.364719 7.76585 0.312012 7.89853 0.312012C8.03121 0.312012 8.15846 0.364719 8.25228 0.45854L12.7523 4.95854C12.7988 5.00498 12.8357 5.06012 12.8608 5.12082C12.886 5.18152 12.8989 5.24658 12.8989 5.31229C12.8989 5.378 12.886 5.44306 12.8608 5.50376C12.8357 5.56446 12.7988 5.6196 12.7523 5.66604L8.25228 10.166C8.20585 10.2125 8.1507 10.2494 8.09 10.2746C8.0293 10.2997 7.96424 10.3127 7.89853 10.3127C7.83282 10.3127 7.76776 10.2997 7.70706 10.2746C7.64636 10.2494 7.59122 10.2125 7.54478 10.166Z"
                fill="black"
              />
            </svg>
          </Link>
        </div>
      </div>
    </>
  );
};

export default FeaturedCard;
